version: 2
updates:
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "friday"
    commit-message:
      prefix: "Infrastructure:"
  - package-ecosystem: "gitsubmodule"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "friday"
    ignore:
      # ignore vcpkg as it is updated often, and this goes off commits, not releases
      - dependency-name: "3rdparty/vcpkg"
    commit-message:
      prefix: "Infrastructure:"
