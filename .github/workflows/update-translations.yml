name: Update texts for translators

on:
  schedule:
    - cron: '0 0 * * fri'

  workflow_dispatch:

jobs:
  update-and-commit-text:
    runs-on: ubuntu-latest
    permissions:
      contents: write  # for gr2m/create-or-update-pull-request-action to push local changes
      pull-requests: write  # for gr2m/create-or-update-pull-request-action to create a PR
    if: ${{ github.repository_owner == 'Mudlet' }}
    steps:
    - uses: actions/checkout@v4
      with:
        ref: development
        submodules: true
        fetch-depth: 0
        persist-credentials: false

    - name: update text for translation
      uses: Mudlet/lupdate-action@master
      with:
        args: -recursive ./src/ ./3rdparty/dblsqd/dblsqd ./3rdparty/edbee-lib/edbee-lib -ts ./translations/mudlet.ts

    - name: check if any UI text was modified
      id: getdiff
      run: |
        # see if we had any changes in the diff, ignoring the <location... changes
        lines_changed=$(git diff --patch --unified=0 translations/mudlet.ts | awk '! /^((@@|diff|index|---|\+\+\+)|((-|+) *<location))/ { count++ } END { print count }')
        echo "$lines_changed lines changed."
        echo ::set-output name=lines_changed::$lines_changed

    - name: send in a pull request
      uses: peter-evans/create-pull-request@v7
      if: steps.getdiff.outputs.lines_changed > 0
      with:
        token: ${{ secrets.GH_PAT_UPDATE_3RDPARTY }}
        add-paths: translations/
        branch: update-texts-for-translators
        commit-message: (autocommit) Updated text for translation
        title: "Infrastructure: Update text for translation in Crowdin"
        body: |
          #### Brief overview of PR changes/additions
          :crown: An automated PR to make new text available for translation in Crowdin from ${{ github.ref }} (${{ github.sha }}).
          #### Motivation for adding to Mudlet
          So translators can translate the new text before the upcoming release.
        author: mudlet-machine-account <<EMAIL>>
