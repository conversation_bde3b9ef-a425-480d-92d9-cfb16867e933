name: 🔨 Build Mudlet
on:
  push:
    branches: [master, development, release-*]
    tags: [Mudlet-*]
  pull_request:
  workflow_dispatch:
    inputs:
      scheduled:
        description: 'Imitate a scheduled build'
        required: false
        default: 'false'
  schedule:
    - cron: '0 2 * * *'

jobs:
  compile-mudlet:
    name: ${{matrix.buildname}}
    runs-on: ${{matrix.os}}
    if: ${{ github.repository_owner == 'Mudlet' }}
    strategy:
      fail-fast: false
      matrix:
        include:
          # Note: using / in the "buildname" has significance - replacing the
          # ',' in some of the following with a '/' seemed to cause extra
          # spurious steps in the build and broke things! - SlySven
          # oldest OS supported for maximum compatibility of built AppImage
          - os: ubuntu-22.04
            buildname: 'ubuntu / gcc / lua tests'
            triplet: x64-linux
            compiler: gcc_64
            gcc_compiler_version: 10
            qt: '6.9.0'
            deploy: 'deploy'
            run_tests: 'true'
          - os: ubuntu-22.04
            # Another try to use GCC12
            buildname: 'ubuntu (x86_64)'
            triplet: x64-linux
            compiler: gcc_64
            gcc_compiler_version: 12
            qt: '6.9.0'
          - os: ubuntu-latest
            buildname: 'ubuntu / clang'
            triplet: x64-linux
            compiler: clang_64
            gcc_compiler_version: 10
            qt: '6.9.0'
          - os: macos-13
            buildname: 'macos (x86_64) / c++, lua tests'
            triplet: x64-osx
            compiler: clang_64
            qt: '6.9.0'
            deploy: 'deploy'
            run_tests: 'true'
          - os: macos-14
            buildname: 'macos (arm64) / c++, lua tests'
            triplet: arm64-osx
            compiler: clang_64
            qt: '6.9.0'
            deploy: 'deploy'
            run_tests: 'true'

    env:
      BOOST_ROOT: ${{github.workspace}}/3rdparty/boost
      BOOST_URL: https://sourceforge.net/projects/boost/files/boost/1.83.0/boost_1_83_0.tar.bz2/download
      VCPKG_ROOT: ${{github.workspace}}/3rdparty/vcpkg

    steps:
    - name: Checkout Mudlet source code
      uses: actions/checkout@v4
      with:
        # This SHOULD NOT be needed both the CMAke and qmake metabuild systems
        # contain conditional code to pull in the required submodules themselves
        # except vcpkg - if that can be handled we could avoid this setting that
        # fetches everything...
        submodules: true
        fetch-depth: 0

    - name: Install Qt
      uses: jurplel/install-qt-action@v4
      with:
        version: ${{matrix.qt}}
        dir: ${{runner.workspace}}
        cache: true
        modules: qt5compat qtmultimedia

    - name: Restore Boost cache
      id: restore-boost
      uses: actions/cache/restore@v4
      with:
        path: ${{env.BOOST_ROOT}}
        key: boost

    - name: Install Boost
      if: steps.restore-boost.outputs.cache-hit != 'true'
      run: |
        if [ "$OS" == "Windows_NT" ]; then
          BOOST_ROOT=$(echo $BOOST_ROOT | sed 's/\\/\//g')
        fi
        mkdir -p $BOOST_ROOT
        curl --progress-bar --location --output $BOOST_ROOT/download.tar.bz2 $BOOST_URL
        7z -o$BOOST_ROOT x $BOOST_ROOT/download.tar.bz2 -y -bd
        7z -o$BOOST_ROOT x $BOOST_ROOT/download.tar -y -bd
        cd $BOOST_ROOT && cp -r boost_*/* .
        rm -rf boost_*/* download.tar.bz2 download.tar
      shell: bash

    - name: Save Boost cache
      if: steps.restore-boost.outputs.cache-hit != 'true'
      uses: actions/cache/save@v4
      with:
        path: ${{env.BOOST_ROOT}}
        key: ${{ steps.restore-boost.outputs.cache-primary-key }}

    - name: Use CMake 3.30.3
      uses: lukka/get-cmake@v3.30.3

    - name: (macOS) Install non-vcpkg dependencies (1/2)
      if: runner.os == 'macOS'
      env:
        HOMEBREW_NO_ANALYTICS: "ON"
        HOMEBREW_NO_AUTO_UPDATE: "ON"
        HOMEBREW_NO_BOTTLE_SOURCE_FALLBACK: "ON"
        HOMEBREW_NO_INSTALL_CLEANUP: "ON"
        QT_VERSION: ${{ matrix.qt }}
      run: |
        # dependencies needed for vcpkg specifically.
        BREWS=("automake" "autoconf" "pkg-config")

        # Loop through each brew package
        for brew in "${BREWS[@]}"; do
         if ! brew list --formula "${brew}" &>/dev/null; then
          echo "Installing ${brew}..."
          brew install "$brew"
         else
          echo "${brew} is already installed."
         fi
        done


        # Set these here so that vcpkg can use them.
        # Use latest available XCode
        echo "DEVELOPER_DIR=$(xcode-select --print-path)" >> $GITHUB_ENV
        echo "SDKROOT=$(xcrun --sdk macosx --show-sdk-path)" >> $GITHUB_ENV

    - name: (Linux) Install non-vcpkg dependencies (1/2)
      if: runner.os == 'Linux'
      run: |
        # gettext is needed for vcpkg
        sudo apt-get install gettext -y

    - name: Restore from cache and run vcpkg
      uses: lukka/run-vcpkg@v7
      env:
        vcpkgResponseFile: ${{github.workspace}}/3rdparty/our-vcpkg-dependencies/vcpkg-${{matrix.triplet}}-dependencies
      with:
        vcpkgArguments: '@${{env.vcpkgResponseFile}}'
        vcpkgDirectory: '${{github.workspace}}/3rdparty/vcpkg'
        appendedCacheKey: ${{hashFiles(env.vcpkgResponseFile)}}-${{matrix.triplet}}-cachekey

    - name: (macOS) Install non-vcpkg dependencies (2/2)
      if: runner.os == 'macOS'
      env:
        HOMEBREW_NO_ANALYTICS: "ON"
        HOMEBREW_NO_AUTO_UPDATE: "ON"
        HOMEBREW_NO_BOTTLE_SOURCE_FALLBACK: "ON"
        HOMEBREW_NO_INSTALL_CLEANUP: "ON"
      run: |
        # these aren't available or don't work well in vcpkg
        BREWS=("libzzip" "libzip" "ccache" "luarocks" "expect")

        # these are available in vcpkg but not working in our old version
        BREWS+=("hunspell")

        # Loop through each brew package
        for brew in "${BREWS[@]}"; do
          if ! brew list --formula "{brew}" &>/dev/null; then
            echo "Installing ${brew}..."
            brew install "$brew"
          else
           echo "${brew} is already installed."
         fi
        done

        echo "CCACHE_DIR=${{runner.workspace}}/ccache" >> $GITHUB_ENV

        # Install lua-yajl early to generate translation statistics
        export PATH="${{env.VCPKG_ROOT}}/installed/${{matrix.triplet}}/tools/lua:$PATH"
        # workaround https://github.com/lloyd/yajl/issues/209
        # Rock locations search is hardcoded to -L/usr/local/lib and not adjustable
        export LIBRARY_PATH="${{env.VCPKG_ROOT}}/installed/${{matrix.triplet}}/lib:$LIBRARY_PATH"
        # LUA_INCDIR needs to be passed as well due to https://github.com/luarocks/luarocks/issues/1239
        luarocks --lua-dir "${{env.VCPKG_ROOT}}/installed/${{matrix.triplet}}/tools/lua" --lua-version "5.1" LUA_INCDIR="${{env.VCPKG_ROOT}}/installed/${{matrix.triplet}}/include" install YAJL_INCDIR="${{env.VCPKG_ROOT}}/installed/${{matrix.triplet}}/include" YAJL_LIBDIR="${{env.VCPKG_ROOT}}/installed/${{matrix.triplet}}/lib" LUA_INCDIR="${{env.VCPKG_ROOT}}/installed/${{matrix.triplet}}/include" --local lua-yajl
        # for some reason it gets installed in a place where lua-yajl can't find it after, fix that
        mkdir -p /Users/<USER>/work/Mudlet/Mudlet/3rdparty/vcpkg/packages/yajl_${{matrix.triplet}}/lib/
        cp "${{env.VCPKG_ROOT}}/installed/${{matrix.triplet}}/lib/libyajl.2.dylib" "${{env.VCPKG_ROOT}}/packages/yajl_${{matrix.triplet}}/lib/libyajl.2.dylib"


        # Allow stats generation script to see location of lua-yajl
        eval "$(luarocks path --local --lua-version "5.1")"
        echo "LUA_PATH=$LUA_PATH" >> $GITHUB_ENV
        echo "LUA_CPATH=$LUA_CPATH" >> $GITHUB_ENV

      # though we vcpkg installs Lua for us, the way it arranges directories is not compatible with Luarocks,
      # so install it in a format that works
    - name: (Linux) Install Lua
      uses: leafo/gh-actions-lua@v11
      if: runner.os == 'Linux'
      with:
        luaVersion: "5.1.5"
        buildCache: false

    - name: (Linux) Install latest Luarocks
      uses: leafo/gh-actions-luarocks@v5
      if: runner.os == 'Linux'

    - name: (Linux) Install non-vcpkg dependencies (2/2)
      if: runner.os == 'Linux'
      run: |
        sudo apt-get update

        # Install from vcpkg everything we can for cross-platform consistency
        # If not available, use other methods
        # imagemagick is for the desktop screenshot
        sudo apt-get install libsecret-1-dev ccache pkg-config pcregrep expect libzip-dev libglu1-mesa-dev libpulse-dev g++-${{matrix.gcc_compiler_version}} \
          libxkbcommon-x11-0 imagemagick -y

        # switch to GCC that supports C++20 while retaining support for older OS's
        sudo update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-${{matrix.gcc_compiler_version}} ${{matrix.gcc_compiler_version}}
        sudo update-alternatives --install /usr/bin/g++ g++ /usr/bin/g++-${{matrix.gcc_compiler_version}} ${{matrix.gcc_compiler_version}}
        sudo update-alternatives --set gcc /usr/bin/gcc-${{matrix.gcc_compiler_version}}
        sudo update-alternatives --set g++ /usr/bin/g++-${{matrix.gcc_compiler_version}}

        echo "CCACHE_DIR=${{runner.workspace}}/ccache" >> $GITHUB_ENV

        # Install lua-yajl early to generate translation statistics
        export PATH="${{env.VCPKG_ROOT}}/installed/${{matrix.triplet}}/tools/lua:$PATH"
        # workaround https://github.com/lloyd/yajl/issues/209
        mv ${{env.VCPKG_ROOT}}/installed/${{matrix.triplet}}/lib/libyajl_s.a ${{env.VCPKG_ROOT}}/installed/${{matrix.triplet}}/lib/libyajl.a
        mv ${{env.VCPKG_ROOT}}/installed/${{matrix.triplet}}/debug/lib/libyajl_s.a ${{env.VCPKG_ROOT}}/installed/${{matrix.triplet}}/debug/lib/libyajl.a
        # Rock locations search is hardcoded to -L/usr/local/lib and not adjustable
        export LIBRARY_PATH="${{env.VCPKG_ROOT}}/installed/${{matrix.triplet}}/lib:$LIBRARY_PATH"
        # LUA_INCDIR needs to be passed as well due to https://github.com/luarocks/luarocks/issues/1239
        luarocks install YAJL_INCDIR="${{env.VCPKG_ROOT}}/installed/${{matrix.triplet}}/include" YAJL_LIBDIR="${{env.VCPKG_ROOT}}/installed/${{matrix.triplet}}/lib" LUA_INCDIR="${{env.VCPKG_ROOT}}/installed/${{matrix.triplet}}/include" --local lua-yajl

        # Allow stats generation script to see location of lua-yajl
        eval "$(luarocks path --local --lua-version "5.1")"
        echo "LUA_PATH=$LUA_PATH" >> $GITHUB_ENV
        echo "LUA_CPATH=$LUA_CPATH" >> $GITHUB_ENV

    - name: (Linux Clang) change compiler & disable optional components
      if: runner.os == 'Linux' && matrix.compiler == 'clang_64'
      run: |
         echo "CXX=clang++" >> $GITHUB_ENV
         echo "CC=clang" >> $GITHUB_ENV
         echo "WITH_UPDATER=no" >> $GITHUB_ENV
         echo "WITH_3DMAPPER=no" >> $GITHUB_ENV
         echo "WITH_FONTS=no" >> $GITHUB_ENV

    - name: Restore ccache
      id: restore-ccache
      uses: actions/cache/restore@v4
      with:
        path: ${{runner.workspace}}/ccache
        key: ccache-${{matrix.os}}-${{matrix.compiler}}-${{matrix.qt}}-${{ github.sha }}
        restore-keys: ccache-${{matrix.os}}-${{matrix.compiler}}-${{matrix.qt}}

    - name: (Linux) Set build info
      if: runner.os == 'Linux'
      env:
        GITHUB_EVENT_INPUTS_SCHEDULED: ${{inputs.scheduled}}
      run: |
        sudo apt-get -y install pcregrep
        ${{github.workspace}}/CI/travis.validate_deployment.sh
        ${{github.workspace}}/CI/travis.set-build-info.sh

    - name: (macOS) Set build info
      if: runner.os == 'macOS'
      env:
        GITHUB_EVENT_INPUTS_SCHEDULED: ${{inputs.scheduled}}
      run: |
        brew install pcre
        ${{github.workspace}}/CI/travis.validate_deployment.sh
        ${{github.workspace}}/CI/travis.set-build-info.sh
        # remove it as it breaks packaging down the line: https://github.com/Mudlet/Mudlet/issues/6668
        brew uninstall pcre
        # ccache can't reliably detect that the compiler is clang on macOS, so just say so explicitly.
        echo "CXX=clang++" >> $GITHUB_ENV
        echo "CC=clang" >> $GITHUB_ENV

    - name: check ccache stats prior to build
      run: ccache --zero-stats --show-stats

    - name: Build Mudlet
      uses: lukka/run-cmake@v3
      with:
        cmakeListsOrSettingsJson: CMakeListsTxtAdvanced
        cmakeListsTxtPath: '${{github.workspace}}/CMakeLists.txt'
        useVcpkgToolchainFile: true
        buildDirectory: '${{runner.workspace}}/b/ninja'
        cmakeAppendedArgs: >-
          -G Ninja
          -DCMAKE_PREFIX_PATH=${{ env.QT_PREFIX != '' && env.QT_PREFIX || env.MINGW_BASE_DIR }}
          -DVCPKG_APPLOCAL_DEPS=OFF
          -DUSE_SANITIZER=""
      env:
        NINJA_STATUS: '[%f/%t %o/sec] '

    - name: Save ccache
      if: always() && steps.restore-ccache.outputs.cache-hit != 'true'
      uses: actions/cache/save@v4
      with:
        path: ${{runner.workspace}}/ccache
        key: ${{ steps.restore-ccache.outputs.cache-primary-key }}

    - name: Upload GitHub Actions artifact of vcpkg build logs
      if: failure()
      uses: actions/upload-artifact@v4
      with:
        name: vcpkg-logs-${{ runner.os }}
        path: ${{ github.workspace }}/vcpkg/buildtrees/**/*.log

    - name: check ccache stats post build
      run: ccache --show-stats

    - name: (macOS) Run C++ tests
      if: runner.os == 'macOS'
      working-directory: '${{runner.workspace}}/b/ninja'
      run: ctest --output-on-failure

    - name: install dependencies for packaging/tests
      if: matrix.deploy == 'deploy' || matrix.run_tests == 'true'
      run: |
        export PATH="${{env.VCPKG_ROOT}}/installed/${{matrix.triplet}}/tools/lua:$PATH"

        shopt -s expand_aliases
        if [ "${RUNNER_OS}" = "macOS" ]; then
          alias run-luarocks="luarocks --lua-dir "${{env.VCPKG_ROOT}}/installed/${{matrix.triplet}}/tools/lua" --lua-version "5.1" LUA_INCDIR="${{env.VCPKG_ROOT}}/installed/${{matrix.triplet}}/include""
        else
          alias run-luarocks="luarocks"
        fi

        run-luarocks install --local LuaFileSystem
        run-luarocks install --local luautf8
        run-luarocks install --local lua-zip
        run-luarocks install SQLITE_INCDIR="${{env.VCPKG_ROOT}}/installed/${{matrix.triplet}}/include" --local LuaSQL-SQLite3 2.6.1
        run-luarocks install PCRE_INCDIR="${{env.VCPKG_ROOT}}/installed/${{matrix.triplet}}/include" --local lrexlib-pcre

        # CI changelog generation dependencies
        run-luarocks install --local argparse
        run-luarocks install --local lunajson

        # Lua-based tests
        run-luarocks install --local busted

        # necessary for Qt multimedia and modern Qt
        # libfuse2 could be needed for linuxdeployqt on Ubuntu 22.04:
        if [ "${RUNNER_OS}" = "Linux" ]; then
          sudo apt-get install libgstreamer-plugins-base1.0-0 libxcb-icccm4 libxcb-image0 libxcb-keysyms1 libxcb-render-util0 libxcb-xinerama0 xvfb libfuse2 -y
        fi
      env:
        BUILD_FOLDER: ${{runner.workspace}}/b/ninja
        RUNNER_OS: ${{runner.os}}
        DEPLOY: ${{matrix.deploy}}

    - name: add ssh-agent for release uploads
      if: (runner.os == 'Linux' || runner.os == 'macOS') && matrix.deploy == 'deploy' && startsWith(github.ref, 'refs/tags/Mudlet-')
      uses: webfactory/ssh-agent@v0.9.1
      with:
        ssh-private-key: ${{secrets.UPLOAD_PRIVATEKEY}}

    - name: package Mudlet
      if: (runner.os == 'Linux' || runner.os == 'macOS') && matrix.deploy == 'deploy'
      run: |
        export ARCH=$(uname -m)
        echo "ARCH=$ARCH" >> $GITHUB_ENV
        if [ "$RUNNER_OS" = "macOS" ] && [ "$ARCH" = "arm64" ]; then
          # dblsqd only supports 'arm' as an architecture and not 'arm64'
          export ARCH_DBLSQD=arm
          echo "ARCH_DBLSQD=arm" >> $GITHUB_ENV
        else
          export ARCH_DBLSQD=$ARCH
          echo "ARCH_DBLSQD=$ARCH" >> $GITHUB_ENV
        fi
        ${{github.workspace}}/CI/travis.after_success.sh
      env:
        BUILD_FOLDER: ${{runner.workspace}}/b/ninja
        RUNNER_OS: ${{runner.os}}
        X_WP_DOWNLOAD_TOKEN: ${{secrets.X_WP_DOWNLOAD_TOKEN}}
        DEPLOY: ${{matrix.deploy}}
        DEPLOY_PATH: ${{secrets.DEPLOY_PATH}}
        DBLSQD_USER: ${{secrets.DBLSQD_USER}}
        DBLSQD_PASS: ${{secrets.DBLSQD_PASS}}
        DEPLOY_KEY_PASS: ${{secrets.DEPLOY_KEY_PASS}}
        MACOS_SIGNING_PASS: ${{secrets.MACOS_SIGNING_PASS}}
        APPLE_USERNAME: ${{secrets.APPLE_USERNAME}}
        APPLE_PASSWORD: ${{secrets.APPLE_PASSWORD}}
        APPLE_TEAM_ID: ${{secrets.APPLE_TEAM_ID}}
        TRIPLET: ${{matrix.triplet}}

    - name: Upload packaged Mudlet
      uses: actions/upload-artifact@v4
      if: env.UPLOAD_FILENAME
      with:
        name: ${{env.UPLOAD_FILENAME}}
        path: ${{env.FOLDER_TO_UPLOAD}}

    - name: Submit to make.mudlet.org
      if: env.UPLOAD_FILENAME
      run: curl --retry 5 -X POST "https://make.mudlet.org/snapshots/gha_queue.php?artifact_name=${{env.UPLOAD_FILENAME}}&unzip=1"
      shell: bash


    # we need to be in the right place for LuaGlobals.lua to load
    - name: (Linux) Prep binary for running tests
      if: matrix.run_tests == 'true' && runner.os =='Linux'
      run: cp ${{runner.workspace}}/b/ninja/src/mudlet ${{github.workspace}}/src
      shell: bash

    - name: (macOS) Prep binary for running tests
      if: matrix.run_tests == 'true' && runner.os == 'macOS'
      run: |
        if [ -d "${{runner.workspace}}/b/ninja/${{env.UPLOAD_FILENAME}}.app" ]; then
          cp -r ${{runner.workspace}}/b/ninja/${{env.UPLOAD_FILENAME}}.app ~/Desktop/Mudlet.app
        elif [ -d "${{runner.workspace}}/b/ninja/Mudlet PTB.app" ]; then
          cp -r "${{runner.workspace}}/b/ninja/Mudlet PTB.app" ~/Desktop/Mudlet.app
        else
          cp -r ${{runner.workspace}}/b/ninja/src/mudlet.app ~/Desktop/Mudlet.app
        fi
        cd ~/Desktop
        sudo codesign --remove-signature ~/Desktop/Mudlet.app
      shell: bash

    - name: (Linux) Run Lua tests
      if: matrix.run_tests == 'true' && runner.os == 'Linux'
      timeout-minutes: 5
      run: xvfb-run --auto-servernum ${{github.workspace}}/src/mudlet --profile "Mudlet self-test" --mirror
      env:
        AUTORUN_BUSTED_TESTS: 'true'
        TESTS_DIRECTORY: ${{github.workspace}}/src/mudlet-lua/tests
        QUIT_MUDLET_AFTER_TESTS: 'true'

    - name: (macOS) Run Lua tests
      if: matrix.run_tests == 'true' && runner.os == 'macOS'
      timeout-minutes: 5
      shell: bash
      run: ~/Desktop/Mudlet.app/Contents/MacOS/mudlet --profile "Mudlet self-test" --mirror
      env:
        AUTORUN_BUSTED_TESTS: 'true'
        TESTS_DIRECTORY: ${{github.workspace}}/src/mudlet-lua/tests
        QUIT_MUDLET_AFTER_TESTS: 'true'

    - name: Passed Lua tests
      if: matrix.run_tests == 'true'
      run: |
        if [ -e /tmp/busted-tests-failed ]
        then
          echo "Lua tests failed - see the action called 'Run Lua tests' above for detailed output."
          exit 1
        fi
