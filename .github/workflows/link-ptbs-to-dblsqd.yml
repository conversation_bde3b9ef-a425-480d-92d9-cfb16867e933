name: Link Linux/macOS PTBs to the updater (dblsqd)

on:
  workflow_dispatch:
  schedule:
    - cron: '0 3 * * *'

jobs:
  link-ptbs:
    runs-on: ubuntu-latest
    if: ${{ github.repository_owner == 'Mudlet' }}

    steps:
    - uses: actions/checkout@v4
    - uses: leafo/gh-actions-lua@v11
      with:
        luaVersion: "5.1.5"
    - uses: actions/setup-node@v4
    - uses: leafo/gh-actions-luarocks@v5

    - name: Retrieve latest PTB version
      run: |
        # Get the latest Windows version, because that one runs first and will be registered in dblsqd. Linux/macOS won't exist yet
        export PTB_VERSION=$(curl --silent https://feeds.dblsqd.com/MKMMR7HNSP65PquQQbiDIw/public-test-build/win/x86 | jq --raw-output ".releases[0].version")
        export PTB_COMMIT=$(lua -e "print(os.getenv('PTB_VERSION'):match('.+\-(.+)$'))")

        echo "Latest PTB version is: $PTB_VERSION"
        echo "Latest PTB commit is: $PTB_COMMIT"

        {
          echo "PTB_VERSION=$PTB_VERSION"
          echo "PTB_COMMIT=$PTB_COMMIT"
        } >> "$GITHUB_ENV"

    - name: Retrieve binaries available on make.mudlet.org
      run: |
        LATEST_FEED=$(mktemp /tmp/latest_feed.XXXXXX) || exit 1
        curl --silent --output $LATEST_FEED "https://make.mudlet.org/snapshots/json.php?commitid=$PTB_COMMIT"

        if [ "$(cat "$LATEST_FEED" | jq --raw-output '.status')" != "ok" ]; then
          echo "Downloaded feed from https://make.mudlet.org/snapshots/json.php?commitid=$PTB_COMMIT wasn't OK, feed is:"
          cat "${LATEST_FEED}" | jq
        fi

        export LINUX_PTB_URL=$(cat $LATEST_FEED | jq --raw-output '.data[] | select(.platform == "linux" and (.url | contains("-ptb-"))) | .url')
        export MACOS_X64_PTB_URL=$(cat $LATEST_FEED | jq --raw-output '.data[] | select(.platform == "macos" and (.url | contains("x86_64")) and (.url | contains("-ptb-"))) | .url')
        export MACOS_ARM64_PTB_URL=$(cat $LATEST_FEED | jq --raw-output '.data[] | select(.platform == "macos" and (.url | contains("arm64")) and (.url | contains("-ptb-"))) | .url')

        echo "Linux PTB link: $LINUX_PTB_URL"
        echo "macOS x64 PTB link: $MACOS_X64_PTB_URL"
        echo "macOS ARM64 PTB link: $MACOS_ARM64_PTB_URL"

        {
          echo "LINUX_PTB_URL=${LINUX_PTB_URL}"
          echo "MACOS_X64_PTB_URL=${MACOS_X64_PTB_URL}"
          echo "MACOS_ARM64_PTB_URL=${MACOS_ARM64_PTB_URL}"
        } >> "$GITHUB_ENV"

    - name: Setup dblsqd client
      run: |
        sudo npm install -g dblsqd-cli
        dblsqd login -e "https://api.dblsqd.com/v1/jsonrpc" -u "${DBLSQD_USER}" -p "${DBLSQD_PASS}"
      env:
        DBLSQD_USER: ${{secrets.DBLSQD_USER}}
        DBLSQD_PASS: ${{secrets.DBLSQD_PASS}}

    - name: Link updater with Linux/macOS downloads
      run: |
        set -e
        export TERM=xterm
        bold=$(tput bold)
        normal=$(tput sgr0)
        EXIT_CODE_LINUX=0
        EXIT_CODE_MACOS_x64=0
        EXIT_CODE_MACOS_ARM64=0

        echo "Linking ${bold}Linux PTB${normal} ${PTB_VERSION}..."
        dblsqd push -a mudlet -c public-test-build -r "${PTB_VERSION}" -s mudlet --type "standalone" --attach linux:x86_64 "${LINUX_PTB_URL}" || EXIT_CODE_LINUX=$?
        echo "Linking ${bold}macOS x84 PTB${normal} ${PTB_VERSION}..."
        dblsqd push -a mudlet -c public-test-build -r "${PTB_VERSION}" -s mudlet --type "standalone" --attach mac:x86_64 "${MACOS_ARM64_PTB_URL}" || EXIT_CODE_MACOS_x64=$?
        echo "Linking ${bold}macOS arm64 PTB${normal} ${PTB_VERSION}..."
        dblsqd push -a mudlet -c public-test-build -r "${PTB_VERSION}" -s mudlet --type "standalone" --attach mac:arm "${MACOS_ARM64_PTB_URL}" || EXIT_CODE_MACOS_ARM64=$?

        if [ "${EXIT_CODE_LINUX}" != 0 ] && [ "${EXIT_CODE_MACOS_x64}" != 0 ] && [ "${EXIT_CODE_MACOS_ARM64}" != 0 ]; then
          exit 1
        fi

