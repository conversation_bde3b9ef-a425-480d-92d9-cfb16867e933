name: Generate coder attribution

on:
  schedule:
    - cron: '0 3 * * *'
  workflow_dispatch:
    inputs:
      from:
        description: 'Generate from this release/commit (defaults to the latest release)'
        required: false
        # default is calculated dynamically
      to:
        description: 'Generate until this release/commit (defaults to latest development)'
        required: false
        default: 'HEAD'

jobs:
  generate-attribution:
    runs-on: ubuntu-latest
    if: ${{ github.repository_owner == 'Mudlet' }}

    steps:
    - name: Checkout Mudlet repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Install Lua 5.1.5
      uses: leafo/gh-actions-lua@v11
      with:
        luaVersion: "5.1.5"

    - name: Install Luarocks
      uses: leafo/gh-actions-luarocks@v5

    - name: Install Lua dependencies
      run: |
        luarocks install argparse
        luarocks install lunajson

    - name: Calculate from and to releases
      run: |
        if [[ -z "${{ github.event.inputs.from }}" ]] ; then
            FROM_RELEASE=$(git tag --sort=committerdate | tail -1)
        else
            FROM_RELEASE=${{ github.event.inputs.from }}
        fi

        if [[ -z "${{ github.event.inputs.to }}" ]] ; then
            TO_RELEASE="HEAD"
        else
            TO_RELEASE=${{ github.event.inputs.to }}
        fi

        echo "Generating attributions from $FROM_RELEASE until $TO_RELEASE"

        echo "FROM_RELEASE=$FROM_RELEASE" >> $GITHUB_ENV
        echo "TO_RELEASE=$TO_RELEASE" >> $GITHUB_ENV

    - name: Generate attributions
      run: |
        # get list of authors
        authors=$(git log $FROM_RELEASE..$TO_RELEASE --format="%aN" --reverse | sort -u)
        # trim known bots
        authors=$(echo "$authors" | grep -v "bot" | grep -v "mudlet-machine-account" | grep -v "ImgBotApp" \
          | grep -v "mudlet-machine-account" | grep -v "root")
        # beautify list
        authors=$(echo "$authors" | tr '\n' ',' | sed 's/,$//' | sed 's/,/, /g')
        authors=$(echo "$authors" | sed 's/\(.*\), \(.*\)/\1, and \2/')

        echo "$authors" > authors.txt
        echo "[INFO] Authors from $FROM_RELEASE to $TO_RELEASE are:"
        echo $authors

    - name: Upload authors as txt
      uses: actions/upload-artifact@v4
      with:
        name: authors.txt
        path: authors.txt

