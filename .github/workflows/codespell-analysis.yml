# GitHub Action to automate the identification of common misspellings in text files.
# https://github.com/codespell-project/actions-codespell
# https://github.com/codespell-project/codespell
name: codespell
on:
  push:
    branches: [master, development, release-*]
    tags: [Mudle<PERSON>-*]
  pull_request:
  workflow_dispatch:

jobs:
  codespell:
    name: Check for spelling errors
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: codespell-project/codespell-problem-matcher@v1
      - uses: codespell-project/actions-codespell@master
        with:
          check_filenames: true
          skip: >-
            ./.git,
            ./3rdparty,
            ./translations,
            ./src/fonts,
            ./src/mudlet-lua/lua/utf8_filenames.lua,
            ./src/AnnouncerWindows.cpp, # 'Teh' is a proper last name
            ./squish-tests,
            *.aff,
            *.dic,
            *.ts
          ignore_words_file: ./.github/codespell-wordlist.txt
          builtin: 'clear,code,rare,informal,names'
