# For most projects, this workflow file will not need changing; you simply need
# to commit it to your repository.
#
# You may wish to alter this file to override the set of languages analyzed,
# or to provide custom queries or build logic.
name: "CodeQL"

on:
  push:
    branches: [development, main]
  schedule:
    - cron: '0 7 * * 3'
  workflow_dispatch:

jobs:
  analyze:
    name: ${{matrix.buildname}}
    runs-on: ubuntu-latest

    permissions:
      security-events: write

    strategy:
      fail-fast: false
      matrix:
        include:
          - language: cpp
            buildname: 'CodeQL'
            qt: '5.14.1'
            triplet: x64-linux
            compiler: gcc_64
            gcc_compiler_version: 10
            os: ubuntu-latest

    env:
      BOOST_ROOT: ${{github.workspace}}/3rdparty/boost
      BOOST_URL: https://sourceforge.net/projects/boost/files/boost/1.83.0/boost_1_83_0.tar.bz2/download


    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        # We must fetch at least the immediate parents so that if this is
        # a pull request then we can checkout the head.
        fetch-depth: 2
        submodules: true

    - name: (Windows) Install Qt
      uses: jurplel/install-qt-action@v4
      if: runner.os == 'Windows'
      with:
        version: ${{matrix.qt}}
        dir: ${{runner.workspace}}
        arch: win64_mingw73
        cache: true

    - name: (Linux/macOS) Install Qt
      uses: jurplel/install-qt-action@v4
      if: runner.os == 'Linux' || runner.os == 'macOS'
      with:
        version: ${{matrix.qt}}
        dir: ${{runner.workspace}}
        cache: true

    - name: Restore Boost cache
      uses: actions/cache@v4
      id: cache-boost
      with:
        path: ${{env.BOOST_ROOT}}
        key: boost

    - name: Install Boost
      if: steps.cache-boost.outputs.cache-hit != 'true'
      run: |
        if [ "$OS" == "Windows_NT" ]; then
          # fix up paths to be forward slashes consistently
          BOOST_ROOT=$(echo $BOOST_ROOT | sed 's/\\/\//g')
        fi
        mkdir -p $BOOST_ROOT
        curl --progress-bar --location --output $BOOST_ROOT/download.tar.bz2 $BOOST_URL
        7z -o$BOOST_ROOT x $BOOST_ROOT/download.tar.bz2 -y -bd
        7z -o$BOOST_ROOT x $BOOST_ROOT/download.tar -y -bd
        cd $BOOST_ROOT && cp -r boost_*/* .
        rm -rf boost_*/* download.tar.bz2 download.tar
      shell: bash

    # Initializes the CodeQL tools for scanning.
    - name: Initialize CodeQL
      uses: github/codeql-action/init@v3
      with:
        config-file: ./.github/codeql/codeql-config.yml
        languages: ${{ matrix.language }}
        # If you wish to specify custom queries, you can do so here or in a config file.
        # By default, queries listed here will override any specified in a config file.
        # Prefix the list here with "+" to use these queries and those in the config file.
        queries: security-extended, security-and-quality

    - name: Use CMake 3.30.3
      uses: lukka/get-cmake@v3.30.3

    - name: (Linux) Install non-vcpkg dependencies (1/2)
      if: runner.os == 'Linux'
      run: |
        # dependencies needed for vcpkg specifically
        sudo apt-get install gettext -y

    - name: Restore from cache and run vcpkg
      uses: lukka/run-vcpkg@v7
      env:
        vcpkgResponseFile: ${{github.workspace}}/3rdparty/our-vcpkg-dependencies/vcpkg-${{matrix.triplet}}-dependencies
      with:
        vcpkgArguments: '@${{env.vcpkgResponseFile}}'
        vcpkgDirectory: '${{github.workspace}}/3rdparty/vcpkg'
        appendedCacheKey: ${{hashFiles(env.vcpkgResponseFile)}}-cachekey

    - name: (Linux) Install non-vcpkg dependencies (2/2)
      if: runner.os == 'Linux'
      run: |
        # Install from vcpkg everything we can for cross-platform consistency
        # If not available, use other methods
        sudo apt-get install libsecret-1-dev ccache pkg-config pcregrep luarocks expect libzip-dev libglu1-mesa-dev libpulse-dev g++-${{matrix.gcc_compiler_version}} -y

        # switch to GCC that supports C++20 while retaining support for older OS's
        sudo update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-${{matrix.gcc_compiler_version}} ${{matrix.gcc_compiler_version}}
        sudo update-alternatives --install /usr/bin/g++ g++ /usr/bin/g++-${{matrix.gcc_compiler_version}} ${{matrix.gcc_compiler_version}}
        sudo update-alternatives --set gcc /usr/bin/gcc-${{matrix.gcc_compiler_version}}
        sudo update-alternatives --set g++ /usr/bin/g++-${{matrix.gcc_compiler_version}}

        echo "CCACHE_DIR=${{runner.workspace}}/ccache" >> $GITHUB_ENV

        # Install lua-yajl early to generate translation statistics
        export PATH="${{env.VCPKG_ROOT}}/installed/${{matrix.triplet}}/tools/lua:$PATH"
        # workaround https://github.com/lloyd/yajl/issues/209
        mv ${{env.VCPKG_ROOT}}/installed/${{matrix.triplet}}/lib/libyajl_s.a ${{env.VCPKG_ROOT}}/installed/${{matrix.triplet}}/lib/libyajl.a
        mv ${{env.VCPKG_ROOT}}/installed/${{matrix.triplet}}/debug/lib/libyajl_s.a ${{env.VCPKG_ROOT}}/installed/${{matrix.triplet}}/debug/lib/libyajl.a
        # Rock locations search is hardcoded to -L/usr/local/lib and not adjustable
        export LIBRARY_PATH="${{env.VCPKG_ROOT}}/installed/${{matrix.triplet}}/lib:$LIBRARY_PATH"
        # LUA_INCDIR needs to be passed as well due to https://github.com/luarocks/luarocks/issues/1239
        luarocks install YAJL_INCDIR="${{env.VCPKG_ROOT}}/installed/${{matrix.triplet}}/include" YAJL_LIBDIR="${{env.VCPKG_ROOT}}/installed/${{matrix.triplet}}/lib" LUA_INCDIR="${{env.VCPKG_ROOT}}/installed/${{matrix.triplet}}/include" --local lua-yajl

        # Allow stats generation script to see location of lua-yajl
        eval "$(luarocks path --local --lua-version "5.1")"
        echo "LUA_PATH=$LUA_PATH" >> $GITHUB_ENV
        echo "LUA_CPATH=$LUA_CPATH" >> $GITHUB_ENV

    - name: (Linux/macOS) Set build info
      if: runner.os == 'Linux' || runner.os == 'macOS'
      run: |
        ${{github.workspace}}/CI/travis.validate_deployment.sh
        ${{github.workspace}}/CI/travis.set-build-info.sh

    - name: Build Mudlet
      uses: lukka/run-cmake@v3
      with:
        cmakeListsOrSettingsJson: CMakeListsTxtAdvanced
        cmakeListsTxtPath: '${{github.workspace}}/CMakeLists.txt'
        useVcpkgToolchainFile: true
        buildDirectory: '${{runner.workspace}}/b/ninja'
        cmakeAppendedArgs: >-
          -G Ninja
          -DCMAKE_PREFIX_PATH=${{env.MINGW_BASE_DIR}}
          -DVCPKG_APPLOCAL_DEPS=OFF
      env:
        NINJA_STATUS: '[%f/%t %o/sec] '

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v3
      with:
        category: "/language:${{ matrix.language }}"
        upload: false
        output: sarif-results

    - name: Filter out 3rdparty dependencies
      uses: advanced-security/filter-sarif@v1
      with:
        patterns: |
          -3rdparty/**
        input: sarif-results/${{ matrix.language }}.sarif
        output: sarif-results/${{ matrix.language }}.sarif

    - name: Upload SARIF
      uses: github/codeql-action/upload-sarif@v3
      with:
        sarif_file: sarif-results/${{ matrix.language }}.sarif
