name: DangerJS

on:
  pull_request_target:
    types: [opened, synchronize, reopened, edited]

jobs:
  danger:
    runs-on: ubuntu-latest
    #if: github.event_name  == 'pull_request' # only run pull requests, no really
    steps:
    - uses: actions/checkout@v4
      with:
        ref: ${{github.event.pull_request.head.ref}}
        repository: ${{github.event.pull_request.head.repo.full_name}}
        fetch-depth: 0
    - name: Install yarn dependencies and run danger
      run: |
        yarn add danger --dev
        yarn danger ci
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        DANGER_DISABLE_TRANSPILATION: "true"
