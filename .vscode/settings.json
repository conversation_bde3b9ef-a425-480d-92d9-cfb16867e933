{
  "cmake.configureOnOpen": true,
  "Lua.runtime.version": "Lua 5.1",
  // files to exclude from clang-tidy code analysis
  "C_Cpp.files.exclude": {
    "**/3rdparty": true,
    "**/ui_*.h": true,
    ".cache": true,
  },
  // don't show 3rdparty files in explorer nor search
  "files.exclude": {
    "3rdparty/": true,
    ".cache": true,
  },
  "files.associations": {
    "typeindex": "cpp",
    "typeinfo": "cpp",
    "string": "cpp",
    "complex": "cpp",
    "*.ipp": "cpp",
    "optional": "cpp",
    "ratio": "cpp",
    "system_error": "cpp",
    "array": "cpp",
    "functional": "cpp",
    "tuple": "cpp",
    "type_traits": "cpp",
    "utility": "cpp",
    "variant": "cpp",
    "*.h": "cpp",
    "*.cpp": "cpp",
    "*.c": "c",
    "CMakeLists.txt": "cmake"
  },
  "C_Cpp.default.cppStandard": "c++20",
  "C_Cpp.default.includePath": [
    "${workspaceFolder}/src",
    "${workspaceFolder}/3rdparty"
  ],
  "[lua]": {
    "editor.tabSize": 2
  }
}
