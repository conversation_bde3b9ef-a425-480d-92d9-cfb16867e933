# [![Logo]][Home] IRC framework 

[![Status]][CI]
[![Coverage]][Codecov]
[![BSD]][BSD-3-Clause]

A cross-platform IRC framework written with Qt.

Communi provides a set of tools for enabling IRC connectivity in Qt-based C++ and QML applications.
See the [online reference documentation](https://communi.github.io/doc) for help with getting started.

## Contact

- Website: [communi.github.io](https://communi.github.io)
- Documentation: [communi.github.io/doc](https://communi.github.io/doc)
- Issue tracker: [communi.github.io/issues](https://communi.github.io/issues)
- IRC channel: `#communi` on [irc.libera.chat](ircs://irc.libera.chat/communi)

[Home]:         https://communi.github.io
[Logo]:         https://raw.githubusercontent.com/communi/libcommuni/master/doc/communi.png
[Status]:       https://github.com/communi/libcommuni/workflows/CI/badge.svg
[CI]:           https://github.com/communi/libcommuni/actions
[Coverage]:     https://codecov.io/gh/communi/libcommuni/branch/master/graph/badge.svg
[Codecov]:      https://codecov.io/gh/communi/libcommuni
[BSD]:          https://img.shields.io/badge/license-BSD-yellow.svg
[BSD-3-Clause]: https://opensource.org/licenses/BSD-3-Clause
