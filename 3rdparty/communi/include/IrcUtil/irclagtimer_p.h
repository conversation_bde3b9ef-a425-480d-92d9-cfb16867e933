/*
  Copyright (C) 2008-2020 The Communi Project

  You may use this file under the terms of BSD license as follows:

  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions are met:
    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above copyright
      notice, this list of conditions and the following disclaimer in the
      documentation and/or other materials provided with the distribution.
    * Neither the name of the copyright holder nor the names of its
      contributors may be used to endorse or promote products derived
      from this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
  DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDERS OR CONTRIBUTORS BE LIABLE FOR
  ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/

#ifndef IRCLAGTIMER_P_H
#define IRCLAGTIMER_P_H

#include "irclagtimer.h"
#include "ircfilter.h"
#include <QTimer>

IRC_BEGIN_NAMESPACE

class IrcPongMessage;

class IrcLagTimerPrivate : public QObject,  public IrcMessageFilter
{
    Q_OBJECT
    Q_INTERFACES(IrcMessageFilter)
    Q_DECLARE_PUBLIC(IrcLagTimer)

public:
    IrcLagTimerPrivate();

    bool messageFilter(IrcMessage* msg) override;
    bool processPongReply(IrcPongMessage* msg);

    void _irc_connected();
    void _irc_pingServer();
    void _irc_disconnected();

    void updateTimer();
    void updateLag(qint64 value);

    IrcLagTimer* q_ptr = nullptr;
    IrcConnection* connection = nullptr;
    QTimer timer;
    int interval;
    int pendingPings = 0;
    qint64 lag = -1;
};

IRC_END_NAMESPACE

#endif // IRCLAGTIMER_P_H
