/*
  Copyright (C) 2008-2020 The Communi Project

  You may use this file under the terms of BSD license as follows:

  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions are met:
    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above copyright
      notice, this list of conditions and the following disclaimer in the
      documentation and/or other materials provided with the distribution.
    * Neither the name of the copyright holder nor the names of its
      contributors may be used to endorse or promote products derived
      from this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
  DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDERS OR CONTRIBUTORS BE LIABLE FOR
  ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/

#ifndef IRCFILTER_H
#define IRCFILTER_H

#include <IrcGlobal>
#include <QtCore/qobject.h>

IRC_BEGIN_NAMESPACE

class IrcMessage;
class IrcCommand;

class IRC_CORE_EXPORT IrcMessageFilter
{
public:
    virtual ~IrcMessageFilter() { }
    virtual bool messageFilter(IrcMessage* message) = 0;
};

class IRC_CORE_EXPORT IrcCommandFilter
{
public:
    virtual ~IrcCommandFilter() { }
    virtual bool commandFilter(IrcCommand* command) = 0;
};

IRC_END_NAMESPACE

// TODO: fixme
#ifdef IRC_NAMESPACE
using IRC_NAMESPACE::IrcMessageFilter;
using IRC_NAMESPACE::IrcCommandFilter;
#endif

Q_DECLARE_INTERFACE(IrcMessageFilter, "Communi.IrcMessageFilter")
Q_DECLARE_INTERFACE(IrcCommandFilter, "Communi.IrcCommandFilter")

#endif // IRCFILTER_H
