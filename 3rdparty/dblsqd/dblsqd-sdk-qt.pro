TEMPLATE = lib

INCLUDEPATH += $$PWD

QT += core network widgets

CONFIG += staticlib

SOURCES += \
    $$PWD/dblsqd/release.cpp \
    $$PWD/dblsqd/semver.cpp \
    $$PWD/dblsqd/update_dialog.cpp \
    $$PWD/dblsqd/feed.cpp

HEADERS  += \
    $$PWD/dblsqd/release.h \
    $$PWD/dblsqd/semver.h \
    $$PWD/dblsqd/update_dialog.h \
    $$PWD/dblsqd/feed.h

FORMS    += \
    $$PWD/dblsqd/update_dialog.ui
