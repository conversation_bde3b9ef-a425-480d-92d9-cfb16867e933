# C++ objects and libs

*.slo
*.lo
*.o
*.a
*.la
*.lai
*.so
*.dll
*.dylib

# Qt-es

*.pro.user
*.pro.user.*
moc_*.cpp
qrc_*.cpp
Makefile
*-build-*

edbee-lib-doxydocs
edbee-lib_dll.pro
lib
dll

# cmake ignores
CMakeCache.txt
CMakeFiles
CMakeScripts
Testing
Makefile
cmake_install.cmake
install_manifest.txt
compile_commands.json
CTestTestfile.cmake
cov-int

edbee-test/edbee-test
.vscode

.vs/
edbee-lib/edbee-lib_autogen/
edbee-lib/qslog/
edbee-test/edbee-test_autogen/
edbee-test/edbee-lib/qslog/

build/

.idea/
*.old/
