[{"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=1", "name": "test_utf8.c (1/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=2", "name": "test_utf8.c (2/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=3", "name": "test_utf8.c (3/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=4", "name": "test_utf8.c (4/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=5", "name": "test_utf8.c (5/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=6", "name": "test_utf8.c (6/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=7", "name": "test_utf8.c (7/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=8", "name": "test_utf8.c (8/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=9", "name": "test_utf8.c (9/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=10", "name": "test_utf8.c (10/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=11", "name": "test_utf8.c (11/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=12", "name": "test_utf8.c (12/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=13", "name": "test_utf8.c (13/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=14", "name": "test_utf8.c (14/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=15", "name": "test_utf8.c (15/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=16", "name": "test_utf8.c (16/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=17", "name": "test_utf8.c (17/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=18", "name": "test_utf8.c (18/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=19", "name": "test_utf8.c (19/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=20", "name": "test_utf8.c (20/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=21", "name": "test_utf8.c (21/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=22", "name": "test_utf8.c (22/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=23", "name": "test_utf8.c (23/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=24", "name": "test_utf8.c (24/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=25", "name": "test_utf8.c (25/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=26", "name": "test_utf8.c (26/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=27", "name": "test_utf8.c (27/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=28", "name": "test_utf8.c (28/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=29", "name": "test_utf8.c (29/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=30", "name": "test_utf8.c (30/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=31", "name": "test_utf8.c (31/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=32", "name": "test_utf8.c (32/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=33", "name": "test_utf8.c (33/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=34", "name": "test_utf8.c (34/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=35", "name": "test_utf8.c (35/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=36", "name": "test_utf8.c (36/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=37", "name": "test_utf8.c (37/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=38", "name": "test_utf8.c (38/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=39", "name": "test_utf8.c (39/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=40", "name": "test_utf8.c (40/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=41", "name": "test_utf8.c (41/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=42", "name": "test_utf8.c (42/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=43", "name": "test_utf8.c (43/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=44", "name": "test_utf8.c (44/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=45", "name": "test_utf8.c (45/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=46", "name": "test_utf8.c (46/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=47", "name": "test_utf8.c (47/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=48", "name": "test_utf8.c (48/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=49", "name": "test_utf8.c (49/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=50", "name": "test_utf8.c (50/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=51", "name": "test_utf8.c (51/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=52", "name": "test_utf8.c (52/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=53", "name": "test_utf8.c (53/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=54", "name": "test_utf8.c (54/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=55", "name": "test_utf8.c (55/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=56", "name": "test_utf8.c (56/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=57", "name": "test_utf8.c (57/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=58", "name": "test_utf8.c (58/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=59", "name": "test_utf8.c (59/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=60", "name": "test_utf8.c (60/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=61", "name": "test_utf8.c (61/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=62", "name": "test_utf8.c (62/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=63", "name": "test_utf8.c (63/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=64", "name": "test_utf8.c (64/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=65", "name": "test_utf8.c (65/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=66", "name": "test_utf8.c (66/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=67", "name": "test_utf8.c (67/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=68", "name": "test_utf8.c (68/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=69", "name": "test_utf8.c (69/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=70", "name": "test_utf8.c (70/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=71", "name": "test_utf8.c (71/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=72", "name": "test_utf8.c (72/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=73", "name": "test_utf8.c (73/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=74", "name": "test_utf8.c (74/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=75", "name": "test_utf8.c (75/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=76", "name": "test_utf8.c (76/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=77", "name": "test_utf8.c (77/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=78", "name": "test_utf8.c (78/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=79", "name": "test_utf8.c (79/80)"}, {"include": "tis-ci/test_utf8.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=0", "name": "test_utf8.c (80/80)"}, {"include": "tis-ci/test_regset.config", "name": "test_regset.c FULL"}, {"include": "tis-ci/test_syntax.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=6 -DTIS_TEST_CHOOSE_CURRENT=1", "name": "test_syntax.c (1/6)"}, {"include": "tis-ci/test_syntax.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=6 -DTIS_TEST_CHOOSE_CURRENT=2", "name": "test_syntax.c (2/6)"}, {"include": "tis-ci/test_syntax.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=6 -DTIS_TEST_CHOOSE_CURRENT=3", "name": "test_syntax.c (3/6)"}, {"include": "tis-ci/test_syntax.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=6 -DTIS_TEST_CHOOSE_CURRENT=4", "name": "test_syntax.c (4/6)"}, {"include": "tis-ci/test_syntax.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=6 -DTIS_TEST_CHOOSE_CURRENT=5", "name": "test_syntax.c (5/6)"}, {"include": "tis-ci/test_syntax.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=6 -DTIS_TEST_CHOOSE_CURRENT=0", "name": "test_syntax.c (6/6)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=1", "name": "testu.c (1/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=2", "name": "testu.c (2/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=3", "name": "testu.c (3/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=4", "name": "testu.c (4/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=5", "name": "testu.c (5/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=6", "name": "testu.c (6/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=7", "name": "testu.c (7/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=8", "name": "testu.c (8/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=9", "name": "testu.c (9/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=10", "name": "testu.c (10/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=11", "name": "testu.c (11/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=12", "name": "testu.c (12/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=13", "name": "testu.c (13/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=14", "name": "testu.c (14/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=15", "name": "testu.c (15/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=16", "name": "testu.c (16/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=17", "name": "testu.c (17/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=18", "name": "testu.c (18/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=19", "name": "testu.c (19/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=20", "name": "testu.c (20/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=21", "name": "testu.c (21/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=22", "name": "testu.c (22/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=23", "name": "testu.c (23/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=24", "name": "testu.c (24/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=25", "name": "testu.c (25/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=26", "name": "testu.c (26/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=27", "name": "testu.c (27/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=28", "name": "testu.c (28/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=29", "name": "testu.c (29/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=30", "name": "testu.c (30/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=31", "name": "testu.c (31/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=32", "name": "testu.c (32/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=33", "name": "testu.c (33/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=34", "name": "testu.c (34/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=35", "name": "testu.c (35/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=36", "name": "testu.c (36/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=37", "name": "testu.c (37/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=38", "name": "testu.c (38/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=39", "name": "testu.c (39/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=40", "name": "testu.c (40/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=41", "name": "testu.c (41/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=42", "name": "testu.c (42/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=43", "name": "testu.c (43/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=44", "name": "testu.c (44/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=45", "name": "testu.c (45/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=46", "name": "testu.c (46/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=47", "name": "testu.c (47/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=48", "name": "testu.c (48/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=49", "name": "testu.c (49/50)"}, {"include": "tis-ci/testu.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=0", "name": "testu.c (50/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=1", "name": "testc.c (1/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=2", "name": "testc.c (2/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=3", "name": "testc.c (3/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=4", "name": "testc.c (4/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=5", "name": "testc.c (5/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=6", "name": "testc.c (6/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=7", "name": "testc.c (7/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=8", "name": "testc.c (8/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=9", "name": "testc.c (9/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=10", "name": "testc.c (10/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=11", "name": "testc.c (11/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=12", "name": "testc.c (12/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=13", "name": "testc.c (13/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=14", "name": "testc.c (14/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=15", "name": "testc.c (15/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=16", "name": "testc.c (16/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=17", "name": "testc.c (17/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=18", "name": "testc.c (18/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=19", "name": "testc.c (19/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=20", "name": "testc.c (20/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=21", "name": "testc.c (21/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=22", "name": "testc.c (22/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=23", "name": "testc.c (23/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=24", "name": "testc.c (24/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=25", "name": "testc.c (25/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=26", "name": "testc.c (26/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=27", "name": "testc.c (27/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=28", "name": "testc.c (28/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=29", "name": "testc.c (29/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=30", "name": "testc.c (30/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=31", "name": "testc.c (31/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=32", "name": "testc.c (32/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=33", "name": "testc.c (33/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=34", "name": "testc.c (34/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=35", "name": "testc.c (35/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=36", "name": "testc.c (36/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=37", "name": "testc.c (37/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=38", "name": "testc.c (38/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=39", "name": "testc.c (39/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=40", "name": "testc.c (40/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=41", "name": "testc.c (41/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=42", "name": "testc.c (42/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=43", "name": "testc.c (43/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=44", "name": "testc.c (44/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=45", "name": "testc.c (45/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=46", "name": "testc.c (46/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=47", "name": "testc.c (47/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=48", "name": "testc.c (48/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=49", "name": "testc.c (49/50)"}, {"include": "tis-ci/testc.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=50 -DTIS_TEST_CHOOSE_CURRENT=0", "name": "testc.c (50/50)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=1", "name": "test_back.c (1/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=2", "name": "test_back.c (2/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=3", "name": "test_back.c (3/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=4", "name": "test_back.c (4/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=5", "name": "test_back.c (5/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=6", "name": "test_back.c (6/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=7", "name": "test_back.c (7/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=8", "name": "test_back.c (8/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=9", "name": "test_back.c (9/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=10", "name": "test_back.c (10/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=11", "name": "test_back.c (11/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=12", "name": "test_back.c (12/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=13", "name": "test_back.c (13/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=14", "name": "test_back.c (14/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=15", "name": "test_back.c (15/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=16", "name": "test_back.c (16/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=17", "name": "test_back.c (17/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=18", "name": "test_back.c (18/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=19", "name": "test_back.c (19/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=20", "name": "test_back.c (20/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=21", "name": "test_back.c (21/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=22", "name": "test_back.c (22/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=23", "name": "test_back.c (23/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=24", "name": "test_back.c (24/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=25", "name": "test_back.c (25/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=26", "name": "test_back.c (26/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=27", "name": "test_back.c (27/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=28", "name": "test_back.c (28/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=29", "name": "test_back.c (29/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=30", "name": "test_back.c (30/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=31", "name": "test_back.c (31/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=32", "name": "test_back.c (32/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=33", "name": "test_back.c (33/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=34", "name": "test_back.c (34/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=35", "name": "test_back.c (35/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=36", "name": "test_back.c (36/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=37", "name": "test_back.c (37/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=38", "name": "test_back.c (38/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=39", "name": "test_back.c (39/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=40", "name": "test_back.c (40/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=41", "name": "test_back.c (41/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=42", "name": "test_back.c (42/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=43", "name": "test_back.c (43/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=44", "name": "test_back.c (44/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=45", "name": "test_back.c (45/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=46", "name": "test_back.c (46/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=47", "name": "test_back.c (47/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=48", "name": "test_back.c (48/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=49", "name": "test_back.c (49/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=50", "name": "test_back.c (50/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=51", "name": "test_back.c (51/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=52", "name": "test_back.c (52/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=53", "name": "test_back.c (53/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=54", "name": "test_back.c (54/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=55", "name": "test_back.c (55/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=56", "name": "test_back.c (56/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=57", "name": "test_back.c (57/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=58", "name": "test_back.c (58/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=59", "name": "test_back.c (59/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=60", "name": "test_back.c (60/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=61", "name": "test_back.c (61/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=62", "name": "test_back.c (62/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=63", "name": "test_back.c (63/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=64", "name": "test_back.c (64/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=65", "name": "test_back.c (65/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=66", "name": "test_back.c (66/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=67", "name": "test_back.c (67/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=68", "name": "test_back.c (68/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=69", "name": "test_back.c (69/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=70", "name": "test_back.c (70/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=71", "name": "test_back.c (71/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=72", "name": "test_back.c (72/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=73", "name": "test_back.c (73/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=74", "name": "test_back.c (74/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=75", "name": "test_back.c (75/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=76", "name": "test_back.c (76/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=77", "name": "test_back.c (77/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=78", "name": "test_back.c (78/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=79", "name": "test_back.c (79/80)"}, {"include": "tis-ci/test_back.config", "compilation_cmd": "-DTIS_TEST_CHOOSE_MAX=80 -DTIS_TEST_CHOOSE_CURRENT=0", "name": "test_back.c (80/80)"}]