{
  "compilation_cmd": "-I ../tis-ci -I ../src -D alloca=__builtin_alloca",
  "files": [
    "../test/testc.c",
    "../src/unicode.c",
    "../src/regcomp.c",
    "../src/regenc.c",
    "../src/utf16_be.c",
    "../src/regparse.c",
    "../src/st.c",
    "../src/regexec.c",
    "../src/unicode_unfold_key.c",
    "../src/unicode_fold3_key.c",
    "../src/unicode_fold2_key.c",
    "../src/unicode_fold1_key.c",
    "../src/euc_jp.c",
    "../src/euc_jp_prop.c",
    "../src/ascii.c",
    "../src/regversion.c"
  ],
  "machdep": "gcc_x86_64",
  "main": "main",
  "name": "testc.c FULL",
  "address-alignment": 65536, /* hexadecimal 0x10000 */
  "val-warn-undefined-pointer-comparison": "none"
}
