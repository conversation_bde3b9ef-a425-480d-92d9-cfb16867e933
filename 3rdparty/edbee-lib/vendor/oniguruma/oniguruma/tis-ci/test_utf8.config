{
  "compilation_cmd": "-I ../tis-ci -I ../src -D alloca=__builtin_alloca",
  "files": [
    "../test/test_utf8.c",
    "../tis-ci/stub.c",
    "../src/regcomp.c",
    "../src/regenc.c",
    "../src/utf8.c",
    "../src/regexec.c",
    "../src/ascii.c",
    "../src/regparse.c",
    "../src/st.c",
    "../src/unicode.c",
    "../src/unicode_unfold_key.c",
    "../src/unicode_fold3_key.c",
    "../src/unicode_fold2_key.c",
    "../src/unicode_fold1_key.c",
    "../src/regversion.c"
  ],
  "machdep": "gcc_x86_64",
  "main": "main",
  "name": "test_utf8.c FULL",
  "address-alignment": 65536, /* hexadecimal 0x10000 */
  "val-warn-undefined-pointer-comparison": "none"
}
