{
  "compilation_cmd": "-I ../tis-ci -I ../src -D alloca=__builtin_alloca",
  "files": [
    "../test/test_regset.c",
    "../tis-ci/stub.c",
    "../src/regcomp.c",
    "../src/regenc.c",
    "../src/utf8.c",
    "../src/regexec.c",
    "../src/ascii.c",
    "../src/regparse.c",
    "../src/st.c",
    "../src/regversion.c"
  ],
  "machdep": "gcc_x86_64",
  "main": "main",
  "name": "test_regset.c FULL",
  "address-alignment": 65536, /* hexadecimal 0x10000 */
  "val-warn-undefined-pointer-comparison": "none"
}
