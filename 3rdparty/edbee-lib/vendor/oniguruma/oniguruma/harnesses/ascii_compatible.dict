# First-pass fuzzing dictionary for Onig<PERSON><PERSON> by <PERSON>
"\\o{34}"
"\\123"
"\\x{40}"
"\\C-"
"\\M-\\C-"
"\\X"
"\\p{"
"\\p{^"
"}"
"]"
"]"
")"
")"
"\\n"
"\\r"
"\\R"
"\\W"
"\\w"
"\\s"
"\\S"
"\\d"
"\\O"
"\\X"
"\\b"
"\\y"
"\\Y"
"\\A"
"\\z"
"\\K"
"\\G"
"\\p{Print}"
"\\p{ASCII}"
"\\p{Alnum}"
"{0,2}"
"{3,}"
"{,3}"
"{5}"
"{4,2}"
"??"
"*?"
"+?"
"*+"
"{1,3}+"
"(?>"
"\\B"
"(?y{"
"[abcd1-9]"
"[\\w]"
"[\\W]"
"[\\s]"
"[\\S]"
"[\\w\\d"
"[\\p{Alphabetic}"
"[\\x{03}"
"[a-w&&"
"[^"
"[:graph:]"
"[^:cntrl:]"
"(?i:"
"(?i)"
"(?m:"
"(?x:"
"(?W:"
"(?y-:"
"(?y{w}:"
"(?P:"
"(?#"
"(?:"
"(?="
"(?!"
"(?<="
"(?<!"
"(?>"
"(?<name>"
"(?{"
"(?{....}[x])"
"(?{.}[x]>)"
"(?{{{.}}})"
"(?~"
"(?~a)"
"(?~|a|.*)"
"(?~|(?:a|b))"
"(?~|)"
"(?(.) |.)"
"(?('-n'))"
"(?(n+0))"
"(?(n+1))"
"(?(n-1))"
"(?(<name+0>))"
"(?(<name+1>))"
"(?(<name-1>))"
"(*COUNT[tag]{X})"
"\\1"
"\\2"
"\\k<name>"
"\\k<1>"
"\\k<2>"
"\\k<-1>"
"\\k<-2>"
"\\k<name+0>"
"\\k<name+1>"
"\\k<name-1>"
"\\g<-1>"
"\\g<name>"
"name"
"(?<name>a|b\\g<name>c)"
"(?-i:\\g<name>)"
"\\N{name}"
"\\p{Katakana}"
"\\p{Emoji}"
"ss"
"SS"
