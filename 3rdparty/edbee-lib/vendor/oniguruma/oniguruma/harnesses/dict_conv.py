# -*- coding: utf-8 -*-
# dict_conv.py  (Python3 script)

import sys

ENC_UTF16_BE = 1
ENC_UTF16_LE = 2

def add_char(enc, s, c):
  if enc == ENC_UTF16_BE:
    s += "\\x00"

  s += c
  if enc == ENC_UTF16_LE:
    s += "\\x00"

  return s

def conv(enc, s):
  n = len(s)
  r = ""
  i = 0
  while i < n:
    c = s[i]
    if c == '\\':
      c = s[i+1]
      if c == '\\' or c == '"':
        r = add_char(enc, r, "\\" + c)
        i += 2
        continue
      else:
        raise("Unknown escape {0}".format(s))

    r = add_char(enc, r, c)
    i += 1

  return r

def main(enc):
  print("# This file was generated by dict_conv.py.")
  for line in sys.stdin:
    s = line.strip()
    if s[0] == '#':
      print(s)
      continue

    if s[0] == '"' and s[-1] == '"':
      s = conv(enc, s[1:-1])
      print("\"{0}\"".format(s))
    else:
      raise("Invalid format {0}".format(s))

def usage(argv):
  raise RuntimeError("Usage: python {0} utf16_be/utf16_le".format(argv[0]))


if __name__ == "__main__":
  argv = sys.argv
  argc = len(argv)

  if argc >= 2:
    s = argv[1]
    if s == 'utf16_be':
      enc = ENC_UTF16_BE
    elif s == 'utf16_le':
      enc = ENC_UTF16_LE
    else:
      usage(argv)
  else:
    usage(argv)

  main(enc)
