History

2024/XX/XX: Version 6.9.10

2024/11/18: fix #312: Build failure with GCC 15 (C23)
2024/09/11: Update to Unicode 16.0
2024/06/20: fix #290: retry limit in match == 0 means unlimited
2024/06/15: add new callout (*SKIP) #299
2024/06/05: add new behavior ONIG_SYN_ALLOW_CHAR_TYPE_FOLLOWED_BY_MINUS_IN_CC (#298)
2024/05/28: fix #296: ONIG_SYNTAX_EMACS doesn't support 'shy groups'
2024/05/24: fix #295: Invalid result for empty match with anchors
2024/04/03: fix #293: Literal escaped braces
2024/04/02: fix total call with whole options
2024/04/01: fix #292: ONIG_SYN_CONTEXT_INDEP_REPEAT_OPS not working for ^* pattern

2023/10/14: Version 6.9.9

2023/09/17: Update to Unicode 15.1.0
2023/07/11: Make sure oniguru<PERSON>.pc is removed on distclean
2023/05/20: fix #284: .{0,99} and .* behave differently on short input
2023/03/27: fix call by number (?n), (?+n), (?-n) of ONIG_SYNTAX_PERL_NG
2023/03/27: fix #282: Dynamic library generated failed to support '(?-i)'
2022/12/30: add a new behavior ONIG_SYN_BRE_ANCHOR_AT_EDGE_OF_SUBEXP
2022/11/16: Changed the behavior of the FIND_LONGEST option to follow all alternatives
2022/09/16: Update to Unicode 15.0.0
2022/08/30: add ONIG_OPTION_MATCH_WHOLE_STRING
2022/08/28: fix ctype punct in Unicode encodings
2022/08/28: fix #268: [[:punct:]] isn't matching all expected symbols. [[:punct:]] = \p{PosixPunct} = \p{P} + \p{S}
2022/06/25: Make the behavior of \p{Word} exactly the same as \w for ignorecase
2022/06/24: (?I) invalid results for charcter classes (Issue #264)
2022/06/15: fix negative POSIX bracket bug
2022/06/03: Build tests with cmake+msvc

2022/04/29: Version 6.9.8

2022/04/11: implement whole option: (?C)
2022/04/07: implement whole option: (?L)
2022/04/04: implement whole option: (?I)
2022/03/15: fix: Insufficient backreference matching for the same name groups
2022/02/22: fix #250: ONIG_ESCAPE_REGEX_T_COLLISION doesn't work
2021/10/17: Update to Unicode 14.0
2021/08/29: fix: use CMAKE_INSTALL_LIBDIR as install lib dir, in some
            platforms, lib could be installed in lib64 dir but the .pc file
            is installed in lib dir.
2021/08/22: fix: Stack overflow for some very long patterns
2021/08/22: fix: Issue 37442 in oss-fuzz: Undefined-shift
2021/08/05: fix #239: CMake build fails to detect alloca on some platforms
            such as FreeBSD
2021/08/04: CMake: Make documentation and examples optional
2021/05/20: fix #235: 6.9.7 can't compile with Visual Studio 2005

2021/04/15: Version 6.9.7 revised 1

2021/04/14: fix: replace UChar to OnigUChar in oniguruma.h

2021/04/14: Version 6.9.7

2021/03/31: Release Candidate 1 for Version 6.9.7

2021/03/23: fix Issue 32340, 32345, 32355 in oss-fuzz
2021/03/12: fix invalid optimization info for if-pattern (?(cond)...)
2021/02/21: NEW API: ONIG_OPTION_CALLBACK_EACH_MATCH
2021/02/02: fix Issue 30144 in oss-fuzz: Timeout
2021/01/18: NEW API: ONIG_SYNTAX_PYTHON
2020/12/20: fix Issue 28795 in oss-fuzz: Timeout
2020/12/13: fix Issue 28554 in oss-fuzz: Timeout, check very inefficient patterns at tune_tree(NODE_CALL)
2020/12/04: fix Issue 28259 in oss-fuzz: Timeout
2020/12/03: fix invalid reduction of nested quantifiers (?:<expr>+?)* and (?:<expr>+?)+
2020/12/01: fix Issue 28104 in oss-fuzz: Timeout
2020/11/28: NEW API: ONIG_OPTION_IGNORECASE_IS_ASCII
2020/11/07: fix Issue 27015 in oss-fuzz: Timeout

2020/11/05: Version 6.9.6

2020/11/01: fix Issue 26798 in oss-fuzz: Timeout
2020/10/27: fix Issue 26675 in oss-fuzz: Timeout

2020/10/21: Release Candidate 4 for Version 6.9.6

2020/10/20: #221: revert cbe9f8b and 8155473: Out-of-bounds write in #207 (Issues found with Coverity) is fake

2020/10/16: Release Candidate 3 for Version 6.9.6

2020/10/15: fix #220: autotools not building DLL using msys2 and mingw64 on windows 10
2020/10/12: fix #219: Binary incompatibilty between 6.9.5_rev1 -> 6.9.2_rc2: reg_number_of_names

2020/10/09: Release Candidate 2 for Version 6.9.6

2020/10/09: fix #216: build fails on Windows

2020/10/07: Release Candidate 1 for Version 6.9.6

2020/09/30: add configure option --enable-binary-compatible-posix-api
2020/09/24: fix: Issue 25893 in oss-fuzz: Stack-buffer-overflow
2020/09/22: fix Issues found with Coverity (Issue #207)
2020/08/27: fix Issue #204: define uint32_t and uint64_t for Visual Studio older than 2010
2020/08/04: fix Issue 24544 in oss-fuzz: Timeout
2020/07/21: add USE_CHECK_VALIDITY_OF_STRING_IN_TREE (fix Issue 24276 in oss-fuzz: Undefined-shift)
2020/07/20: fix: Issue 24268 in oss-fuzz: Timeout
2020/07/17: fix: Issue 24112 in oss-fuzz: Undefined-shift
2020/07/14: fix: Issue 24066 in oss-fuzz: Timeout
2020/07/05: fix: Incomplete application of ONIG_OPTION_NOTBOL to \A
2020/07/05: fix: Incomplete application of ONIG_OPTION_NOT_END_STRING to \Z (Issue #192)
2020/07/05: fix: Incomplete application of ONIG_OPTION_NOTEOL to \z
2020/07/05: fix: Incomplete application of ONIG_OPTION_NOTEOL to \Z
2020/07/01: add ONIG_OPTION_NOT_END_STRING (Issue #198)
2020/06/28: add ONIG_OPTION_NOT_BEGIN_POSITION (Issue #198)
2020/06/28: add ONIG_OPTION_NOT_BEGIN_STRING
2020/06/28: fix: Issue 23754 in oss-fuzz: Timeout
2020/06/21: fix: Issue 23525 in oss-fuzz: Timeout
2020/06/15: fix: Issue 23311 in oss-fuzz: Timeout
2020/06/03: fix: Issue 22925 in oss-fuzz: Index-out-of-bounds
2020/06/03: fix: Issue 22917 in oss-fuzz: Out-of-memory
2020/06/02: fix: Issue 22916 in oss-fuzz: Timeout
2020/05/29: fix: Issue 22744 in oss-fuzz: Integer-overflow
2020/05/28: fix: Issue 22658 in oss-fuzz: check backref with level
2020/05/28: fix: Issue 22533 in oss-fuzz: memory leak
2020/05/23: fix: Issue 22393 in oss-fuzz: Integer-overflow
2020/05/13: fix: Issue 22154 in oss-fuzz: When the option FIND_LONGEST is specified, match_at() returns ONIG_MISMATCH unless there is no need to search any more.
2020/05/06: Add SOVERSION info to library when using cmake
2020/05/04: fix: 22008 in oss-fuzz
2020/05/04: fix: 21998 in oss-fuzz
2020/05/03: fix: 21944, 21977 in oss-fuzz

2020/04/26: Version 6.9.5 revised 1

2020/04/24: fix #192: Unexpected regex match

2020/04/20: Version 6.9.5

2020/04/12: Release Candidate 2 for Version 6.9.5
2020/04/09: fix a problem (found by oss-fuzz test on my PC)
2020/04/05: Release Candidate 1 for Version 6.9.5
2020/03/30: remove src/*.py and src/*.sh from distribution files
2020/03/27: NEW: Code point sequence notation \x{HHHH ...}, \o{OOOO ...}
2020/03/24: NEW API: maximum nesting level of subexp call
2020/03/22: #165: change enable-posix-api default from YES to NO
2020/03/15: update Unicode version to 13.0.0
2020/03/10: add test_back.c
2020/03/08: tune output of debug in print_optimize_info()
2020/03/02: fix #186: Allow regset search to succeed at end of string
2020/02/13: NEW API: retry-limit-in-search functions
2020/01/20: add ONIG_SYN_VARIABLE_LEN_LOOK_BEHIND flag
2019/12/27: add USE_REGSET switch
2019/12/20: remove OPTIMIZE_STR_CASE_FOLD
2019/12/13: add test/test_syntax.c
2019/12/13: add ONIG_SYN_ISOLATED_OPTION_CONTINUE_BRANCH flag


2019/11/29: Version 6.9.4

2019/11/22: Release Candidate 3 for Version 6.9.4
2019/11/20: fix a problem found by libFuzzer test
2019/11/14: Release Candidate 2 for Version 6.9.4
2019/11/12: fix integer overflow by nested quantifier
2019/11/11: fix CVE-2019-19012: Integer overflow related to reg->dmax in search_in_range()
2019/11/07: fix CVE-2019-19203: heap-buffer-overflow in gb18030_mbc_enc_len()
2019/11/06: fix CVE-2019-19204: heap-buffer-overflow in fetch_interval_quantifier()
2019/11/06: add HAVE_INTTYPES_H into config.h.windows.in and config.h.win{32,64}
2019/11/06: add HAVE_STDINT_H into config.h.win{32,64}
2019/11/05: Release Candidate 1 for Version 6.9.4
2019/10/31: Update Unicode Emoji version to 12.1 (Nothing data changed)
2019/10/29: implement USE_REPEAT_AND_EMPTY_CHECK_LOCAL_VAR configuration
2019/10/18: re-implement case fold conversion
2019/10/04: fix #156: Heap buffer overflow in match_at() with case-insensitive match
2019/09/30: NEW API: add onig_regset_replace()
2019/09/30: change Unicode VERSION value format
2019/09/20: NEW API: add regset functions
2019/09/20: add data ensure check before peek string value in OP_PUSH_IF_PEEK_NEXT
2019/09/20: fix loose code in encode-harness.c
2019/08/13: fix heap-buffer-overflow
2019/08/13: Add a macro to disable direct threading in the match engine (PR#149)

2019/08/06: Version 6.9.3 (secirity fix release)

2019/07/30: add ONIG_SYN_ALLOW_INVALID_CODE_END_OF_RANGE_IN_CC
2019/07/29: add STK_PREC_READ_START/END stack type
2019/07/29: Fix #147: Stack Exhaustion Problem caused by some parsing functions
2019/07/11: add a dictionary file for libfuzzer
2019/07/07: add harnesses directory
2019/07/05-2019/07/29: fix many problems found by libfuzzer programs
2019/06/27: deprecate onig_new_deluxe()
2019/06/27: Fix CVE-2019-13224: don't allow different encodings for onig_new_deluxe()
2019/06/27: Fix CVE-2019-13225: problem in converting if-then-else pattern

2019/05/07: Version 6.9.2 (same as Release Candidate 3)

2019/04/23: Release Candidate 3 for 6.9.2
2019/04/23: add doc/SYNTAX.md into distribution file
2019/04/09: Release Candidate 2 for 6.9.2
2019/04/09: fix #139: UAF in match_at()
2019/04/01: Release Candidate 1 for 6.9.2
2019/04/01: update Unicode version to 12.1.0 (draft)
2019/03/29: allow {n,m} (n>m) as possessive interval
2019/03/25: add ONIG_SYN_OP2_OPTION_ONIGURUMA
2019/03/22: add new options ONIG_OPTION_TEXT_SEGMENT_EXTENDED_GRAPHEME_CLUSTER and
            ONIG_OPTION_TEXT_SEGMENT_WORD
2019/03/21: PR #137: fix cross-compilation
2019/03/20: update Unicode version to 12.0.0
2019/03/17: add doc/SYNTAX.md
2019/03/13: {n,m}+ and {n,m}? are possessive and reluctant range operator
            in Perl syntax
2019/03/04: fix #132: don't execute testp if ENABLE_POSIX_API == no
2019/02/28: re-implement bytecode by using Operation struct
2019/02/26: fix #130: Build error on UWP with VS2017
2019/02/03: PR #128: regerror/toascii: do not attempt to serialize NULL pointer
2019/01/30: Build breaks without autoreconf #73
2019/01/02: fix #127: Windows VS 2008 build errors
2018/12/19: fix #126: Unable to compile when USE_CALLOUT is not defined

2018/12/11: Version 6.9.1

2018/10/08: use ENC_FLAG_SKIP_OFFSET_XXX values
2018/10/06: UTF-8 supports code range from 0x0000 to 0x10FFFF
            (https://tools.ietf.org/html/rfc3629)
2018/10/05: speed improvement
2018/10/03: use OPTIMIZE_STR_CASE_FOLD_FAST
2018/10/01: convert CRLF line endings to LF
2018/09/27: set SIZEOF_SIZE_T for windows platforms
2018/09/22: use Sunday quick search algorithm instead of Boyer-Moor-Horspool
2018/09/20: introduce threaded code into match_at()
2018/09/17: remove HAVE_STRINGS_H
2018/09/16: remove HAVE_PROTOTYPES and HAVE_STDARG_PROTOTYPES
2018/09/14: add a command line option '-gc' for make_unicode_property_data.py.
2018/09/08: remove AC_HEADER_STDC
2018/09/06: remove AC_OUTPUT macro call
2018/09/06: remove AC_FUNC_MEMCMP, AC_HEADER_TIME, AC_C_CONST, HAVE__SETJMP and
            HAVE_STRING_H
2018/09/05: remove HAVE_LIMITS_H, HAVE_FLOAT_H and HAVE_STDLIB_H

2018/09/03: Version 6.9.0

2018/08/24: add Unicode Emoji properties
2018/08/24: update Unicode version 11.0.0
2018/08/21: support gperf 3.1 instead of 3.0.4
2018/08/07: add ENABLE_POSIX_API switch into src/Makefile.windows
2018/08/02: add make_win.bat and src/config.h.windows.in
2018/06/25: add ENABLE_POSIX_API option into CMakeLists.txt
2018/06/04: add .travis.yml (for TravisCI)

2018/04/17: Version 6.8.2

2018/04/13: add doc/CALLOUTS.API.ja
2018/04/10: add doc/CALLOUTS.API
2018/04/10: fix #87: Read unknown address in onig_error_code_to_str()
2018/04/06: fix #86: typedef StateCheckNumType is unused
2018/04/02: update automake 1.16.1
2018/03/30: fix #84: stack-buffer-overflow in mbc_enc_len
2018/03/28: PR #83: Improve CMake build
2018/03/21: switch uses of UChar to OnigUChar in oniguruma.h (#80)

2018/03/19: Version 6.8.1

2018/03/19: update LTVERSION from 4:0:0 to 5:0:0
2018/03/19: add flag, sb_range etc.. into OnigEncodingType
2018/03/19: move regex structure from oniguruma.h to regint.h
2018/03/19: ONIGENC_CTYPE_XXX to be enum (Issue #33)

2018/03/16: Version 6.8.0

2018/03/12: add doc/CALLOUTS.BUILTIN for builtin callouts
2018/03/08: allow abbreviated notation for callouts (?(*name)..|..) (?(?{...})..|..)
2018/03/02: NEW API: move onigenc_strdup() from regenc.h to oniguruma.h
2018/02/21: remove all USE_COMBINATION_EXPLOSION_CHECK
2018/02/15: fix #78: bad definition of PV_()
2018/02/14: add configure option --enable-posix-api (for #77)
2018/02/08: implement callouts of name
2018/02/01: implement callouts of contents
2018/01/30: define ONIGURUMA_VERSION_INT
2018/01/29: enable USE_TRY_IN_MATCH_LIMIT by default
2018/01/29: NEW API: onig_search_with_param() onig_match_with_param()
2018/01/26: remove include windows.h from oniguruma.h

2018/01/26: Version 6.7.1

2018/01/25: disable USE_TRY_IN_MATCH_LIMIT by default
2018/01/24: implement mechanism of try-in-match-limit
2018/01/24: #76: rename EXPORT to ONIGURUMA_EXPORT
2018/01/15: #73: update for automake 1.15.1
2018/01/14: #74: update description of README
2018/01/10: #72: Correct spelling and grammar in FAQ (English)
2017/12/25: remove USE_COMBINATION_EXPLOSION_CHECK codes

2017/12/11: Version 6.7.0

2017/12/08: Disable \N and \O on ONIG_SYNTAX_RUBY
2017/12/08: add ONIG_SYNTAX_ONIGURUMA (default syntax)
2017/12/05: restructure StackType
2017/11/13: implement subexp calls (?R), (?&name), (?-n), (?+n) for Perl syntax
2017/09/25: use string pool of gperf for Unicode Property lookup function
2017/09/16: fix #70: an empty greedy regex and a word boundary (.*\b) fails
2017/09/13: remove a stack type STK_POS
2017/09/08: fix #69: add a declaration of onig_end()
2017/09/07: fix #68: Compilation failure in out-of-source build
2017/09/03: [new] hexadecimal codepoint \uHHHH

2017/08/30: Version 6.6.1

2017/08/29: fix definition of \X to (?>\O(?:\Y\O)*)

2017/08/28: Version 6.6.0

2017/08/26: fix #67: can't compile with Visual Studio 2005
2017/08/24: rename Absent clear to Range clear
2017/08/21: [new] Extended Grapheme Cluster \X and boundary \y, \Y
2017/08/17: fix: invalid index(ctype) value assigned to Unicode Block properties
2017/08/16: --enable-crnl-as-line-terminator to be deprecated
2017/08/15: [new] ASCII only mode options (?WDSP)
2017/08/14: [new] ONIG_OPTION_XXXX_IS_ASCII  options
2017/08/11: disable OP_CCLASS_NODE
2017/08/11: [spec] Absent clear restore previous range value at backtrack
2017/08/07: optimize for simple one char repetition in Absent expression
2017/08/07: fix: invalid impl. for reluctant repetition in Absent expression
2017/08/04: remove compile switch USE_NAMED_GROUP

2017/08/03: Version 6.5.0

2017/07/30: [new] support Absent clear (Absent functions)
2017/07/25: abolish configure option: --enable-combination-explosion-check
2017/07/23: [new] support Absent functions (?~...)
2017/07/14: fix #65: SIZEOF_SIZE_T doesn't exist on certain architecutres
2017/07/11: [new] support \O (true anychar)
2017/07/10: [new] support \K (keep)
2017/07/10: add new node type: NODE_GIMMICK
2017/07/07: [new] support \N (no newline)
2017/07/05: [new] support \R (general newline)
2017/07/05: [new] support if-then-else syntax
2017/07/04: [new] support backref validity checker

2017/07/03: Version 6.4.0

2017/06/30: fix memory leaks
2017/06/29: fix memory leaks
2017/06/28: change encoding of doc/XXXX.ja from EUC-JP to UTF-8
2017/06/28: update doc/RE, and doc/RE.ja
2017/06/26: fix fatal bug of endless repeat check on Windows
2017/06/26: PR #62 : add check for return values
2017/06/23: [new] support call zero (\g{0})
2017/06/23: [new] support relative call by positive number
2017/06/23: [new] support relative back-reference by positive number
2017/06/15: fix #60 : check value type
2017/06/02: change output format for ONIG_DEBUG_COMPILE and ONIG_DEBUG_MATCH

2017/05/29: Version 6.3.0

2017/05/24: fix #60 : invalid state(CCS_VALUE) in parse_char_class()
2017/05/24: fix #59 : access to invalid address by reg->dmax value
2017/05/23: fix invalid increment of start position in onig_scan()
2017/05/23: fix #58 : access to invalid address by reg->dmin value
2017/05/23: fix #57 : DATA_ENSURE() check must be before data access
2017/05/22: fix #56 : return invalid result for codepoint 0xFFFFFFFF
2017/05/19: [new] add \o{17777777777} syntax.
2017/05/19: fix #55 : Byte value expressed in octal must be smaller than 256

2017/04/08: Version 6.2.0

2017/03/15: fix: size in xmemcpy in stack_double (PR #51)
2017/02/21: Initialize return value
2017/01/03: NEW API: add onig_set_capture_num_limit()
2017/01/03: change MemNumType from short int to int
2016/12/13: fix: [0-9-a] was not allowed as [0-9\-a]
2016/12/13: fix: illegal capture after recursive call
2016/12/13: fix: problem with optimization of \z
2016/12/13: fix: .* optimization
2016/12/13: Set a limit of parser recursion
2016/12/12: fix; that warnings are not shown properly
2016/12/12: fix: /[a-c#]+\W/ =~ "def#" fails when encoding is UTF-16/32
2016/12/12: fix: /[\x{0}-X]/i doesn't match properly when UTF-16/32 is used.

2016/12/11: Version 6.1.3

2016/12/11: fix: Syntax error: redirection unexpected (expecting word) #35

2016/11/07: Version 6.1.2

2016/10/25: allow word bound, word begin and word end in look-behind.
2016/10/19: add ONIG_OPTION_CHECK_VALIDITY_OF_STRING option.
2016/10/16: fix use after free node.
2016/10/10: fix memory leaks after parsing regexp error.
2016/09/22: implement many of is_valid_mbc_string().

2016/09/02: Version 6.1.1

2016/08/31: fix segfault /W.?{888}{888}{888}\x00/ (found by libfuzzer)
2016/08/31: fix error unmatched close parenthesis for %{(.*?)} #23

2016/08/29: Version 6.1.0

2016/08/28: add contributed/libfuzzer-onig.cpp  (thanks hannob)
2016/08/28: update LTVERSION 4:0:0
2016/08/28: NEW API: onigenc_is_valid_mbc_string().
2016/08/27: add is_valid_mbc_string() member into OnigEncodingType.
2016/08/27: fix out of bounds read.
2016/08/26: fix out of bounds read.
2016/08/25: disable USE_INVALID_CODE_SCHEME.
2016/08/24: fix out of bounds read.
2016/08/23: doc/RE improved.
2016/08/22: add onig_scan() into doc/API.
2016/08/22: fix bug: Out of bounds read in onig_strcpy() #17
2016/08/21: fix bug: infinite loop of backreference and group.
2016/08/21: fix out of bounds read in mbc_to_code() #16
2016/08/18: doc/RE refinements.
2016/08/16: add onig_scan() (NEW API)
2016/08/16: reimplement match stack allocation for case too many repeat
            and too many captures in regexp.
2016/08/15: number of captures <= 32767 for bytecode representation.
2016/07/17: don't use int_map_backward for thread-safe.
2016/07/04: fix case of enclosed option in look-behind.
2016/07/04: fix ignore case in look-behind.
2016/05/23: fix memory leak in onig_unicode_define_user_property()
2016/05/20: declare variables at the top of scope.  (thanks nmaya)

2016/05/09: Version 6.0.0

2016/05/05: add NEW API: onig_unicode_define_user_property()
2016/05/04: update Unicode data to 8.0.0
2016/05/02: change OnigCodePoint type to unsigned int.
2016/05/02: add doc/UNICODE_PROPERTIES.
2016/04/19: add error code ONIGERR_FAIL_TO_INITIALIZE.
2016/04/18: add make_win64/32.bat.
2016/04/18: fix bug of uninitialized regex_t value on error.
2016/04/16: reimplement Unicode case folding.
2016/04/11: update LTVERSION = 3.0.0
2016/04/05: remove all THREAD_ macro.
2016/04/05: add init member into OnigEncoding. (add onig_initialize())
2016/03/28: remove state member of regex.
2016/03/25: move source files into src/
2016/03/23: rename configre.in to configure.ac.
2015/11/17: fix memory leak. (thanks pigzang)
2015/07/13: change mail address.

2014/12/12: Version 5.9.6

2013/11/27: [impl] add onigenc_end_unicode().  (thanks Takenori Imoto)
2013/11/27: [impl] add onig_add_end_call().    (thanks Takenori Imoto)

2013/10/21: Version 5.9.5

2013/10/21: [impl] escape warnings for -Wall. (regparse.c)
2013/10/21: [bug] fixes an issue on Windows x64. (thanks Anatoliy Belsky)
                  The issue was discovered in PHP, see https://bugs.php.net/64769.
2013/10/21: [impl] remove unused variable. (regcomp.c)

2013/04/04: Version 5.9.4

2013/04/04: [dev]  remove Makefile.in from git repository.
2013/04/04: [dist] add oniguruma.pc.in file. (for pkg-config)
                   (thanks Giulio Paci)

2012/10/26: Version 5.9.3

2012/10/15: remove warnings "test: =: unary operator expected" in ./configure.
            (thanks t_okazaki)
2012/10/15: fix print_tree ENCLOSE_OPTION bug. (thanks Suraj N. Kurapati)

2010/01/09: Version 5.9.2

2010/01/05: [bug]  fix utf16be_code_to_mbc() and utf16le_code_to_mbc().
2008/09/16: [bug]  fix memory leaks in parse_exp().
2008/08/01: [bug]  fix memory leaks.
2008/06/17: [bug]  invalid type of argument was used
                   in onig_st_lookup_strend().
2008/06/16: [bug]  invalid CaseFoldMap entry in ISO-8859-5. 0xdf -> 0xde
2008/02/19: [new]  add: onig_reg_init().
2008/02/19: [new]  add: onig_free_body().
2008/02/19: [new]  add: onig_new_without_alloc().
2008/02/19: [API]  rename onig_alloc_init() to onig_reg_init(),
                   and argument type changed.
2008/01/31: [impl] move UTF16_IS_SURROGATE_XXX() to regenc.h.
2008/01/30: [bug]  (thanks akr)
                   fix euctw_islead().
2008/01/23: [bug]  update enc/koi8.c.

2007/12/22: Version 5.9.1

2007/12/21: [impl] add sprint_byte().
2007/11/28: [bug]  (thanks Andy Armstrong)
                   don't overwrite error code in fetch_name().
2007/11/12: [bug]  utf8 mbc length of code 0xfe, 0xff are not 1,
2007/10/23: [spec] onig_enc_len() takes three arguments. (not used)
2007/10/15: [impl] (thanks Rui Hirokawa)
                   add check HAVE_STDARG_H.
2007/09/07: [API]  rename enc_len() to onig_enc_len() in oniguruma.h.
2007/09/04: [API]  remove ONIGENC_ERR_XXXXX.
2007/09/03: [API]  add error ONIGERR_INVALID_CODE_POINT_VALUE.
2007/09/03: [impl] change error message to "invaid code point value"
                   for ONIGERR_INVALID_WIDE_CHAR_VALUE.
2007/09/03: [bug]  xxx_code_to_mbclen() should return
                   ONIGERR_INVALID_WIDE_CHAR_VALUE for invalid code point.
                   ex. /[\x{7fffffff}]/ for ASCII encoding.
2007/08/28: [impl] remove "warning: no previous declaration ...".
2007/08/21: [impl] remove warnings in enc/mktable.c.
2007/08/20: [impl] remove "warning: unused parameter"
2007/08/20: [impl] remove "warning: comparison between signed and unsigned".
2007/08/06: [impl] remove clear_not_flag_cclass().
2007/08/03: [bug]  fix the case of undefined USE_NAMED_GROUP.
2007/08/02: [spec] add backref by number.
2007/08/01: [API]  add OnigCtype.
2007/07/27: [spec] add USE_CASE_FOLD_IS_APPLIED_INSIDE_NEGATIVE_CCLASS.
2007/07/24: [impl] define PLATFORM_UNALIGNED_WORD_ACCESS.
2007/07/23: [dist] fix doc/FAQ.ja.

2007/07/14: Version 5.9.0

2007/07/13: [bug]  add check into onig_reduce_nested_quantifier().
2007/06/26: [spec] (thanks K.Takata)
                   ONIG_OPTION_SINGLELINE: '$' -> '\Z'  (as Perl)
2007/06/26: [dist] (thanks K.Takata)
                   fix documents API and API.ja.
2007/06/19: [impl] remove IS_NOT_NULL() check before onig_node_free().
2007/06/18: [bug]  (thanks KUBO Takehiro)
                   WORD_ALIGNMENT_SIZE must be sizeof(OnigCodePoint).
2007/06/18: [impl] rename CClassNode flags.
2007/06/18: [bug]  initialization miss.
2007/06/13: [impl] change node type reference NXXXX.
2007/06/11: [impl] add node type bit.
2007/06/11: [spec] allow anchor in enclosed repeater. /(\z)*/
2007/06/11: [impl] rename node types.
2007/06/08: [impl] remove OP_SET_OPTION_PUSH and OP_SET_OPTION from match_at().
2007/06/07: [impl] use xvsnprintf().
2007/06/06: [tune] don't set qn->next_head_exact for string first byte is zero.
2007/06/06: [impl] remove unused variables.

2007/06/04: Version 5.8.0

2007/06/04: [impl] add #ifndef vsnprintf into regint.h.
2007/05/31: [dist] add configure option '--enable-crnl-as-line-terminator'.
2007/05/30: [dist] add sample/crnl.c.
2007/05/30: [bug]  should check USE_CRNL_AS_LINE_TERMINATOR case
                   in onig_search().
2007/05/29: [impl] move USE_CRNL_AS_LINE_TERMINATOR into regenc.h.
2007/05/29: [impl] should check USE_NEWLINE_AT_END_OF_STRING_HAS_EMPTY_LINE
                   in forward_search_range() and backward_search_range().

2007/04/27: Version 5.7.0

2007/04/20: [spec] add config USE_MATCH_RANGE_IS_COMPLETE_RANGE.
2007/04/20: [impl] refactoring in match_at().

2007/04/12: Version 5.6.1

2007/04/12: [bug]  must not use UChar in oniguruma.h.
2007/04/09: [impl] change STATE_CHECK_BUFF_MAX_SIZE value from 0x8000
                   to 0x4000. [ruby-core:10883]

2007/04/04: Version 5.6.0  (mourning for Hideo Takamatsu)

2007/04/03: [spec] add new notation (?'name'), \k'name', \g'name'.
2007/04/03: [impl] remove unused variable.
2007/03/26: [impl] add 'void' to function declarations.

2007/03/06: Version 5.5.3

2007/03/06: [bug]  add #include <malloc.h> for bcc32.
                   (In bcc32, alloca() is declared in malloc.h.)
2007/03/02: [bug]  invalid optimization for semi-end-buf in onig_search().
                   ex. /\n\Z/.match("aaaaaaaaaa\n")
2007/03/02: [impl] move range > start check position in end_buf process.

2007/01/09: Version 5.5.2

2007/01/09: [impl] rename USE_EXTERNAL_LOWER_CASE_CONV_TABLE.
2007/01/05: [tune] select_opt_exact_info() didn't work for empty info.
                   ex. /.a/ make MAP info instead of EXACT info.
2006/12/28: [impl] add print_enc_string() for ONIG_DEBUG mode.

2006/12/22: Version 5.5.1

2006/12/22: [impl] rename ADD_PAD_TO_SHORT_BYTE_STRING
                 . to USE_PAD_TO_SHORT_BYTE_CHAR.
2006/12/21: [spec] should check too short multibyte char in parse_exp().
                   add ADD_PAD_TO_SHORT_BYTE_STRING.
                   ex. /\x00/ in UTF16 should be error.

2006/12/06: Version 5.5.0

2006/12/05: [bug]  should add unfold-1 codes from folded code into
                   onigenc_unicode_get_case_fold_codes_by_str().
                   (ex. "S" -> "s" -> 0x017f)
2006/12/05: [new]  add flag ONIGENC_CASE_FOLD_TURKISH_AZERI and
                   USE_UNICODE_CASE_FOLD_TURKISH_AZERI. (disabled in default)
2006/12/04: [spec] remove ONIGENC_CASE_FOLD_FULL.
2006/11/30: [impl] remove unnecessary check in xxx_mbc_case_fold().

2006/11/29: Version 5.4.0

2006/11/28: [spec] INTERNAL_ONIGENC_CASE_FOLD_MULTI_CHAR is enabled in
                   default case fold status.
2006/11/28: [spec] rename ONIGENC_CASE_FOLD_MULTI_CHAR to
                   INTERNAL_ONIGENC_CASE_FOLD_MULTI_CHAR.
2006/11/28: [impl] remove USE_UNICODE_CASE_FOLD_MULTI_CHAR.
2006/11/28: [impl] remove Fold[123]Table and add FoldTable.
2006/11/27: [impl] change tool/unicode_fc.rb to see CaseFolding.txt.
2006/11/24: [bug]  should call callback for to[j] <-> to[k] in
                   onigenc_unicode_apply_all_case_fold().

2006/11/22: Version 5.3.0

2006/11/22: [dist] add index_ja.html.
2006/11/22: [impl] undef ONIG_ESCAPE_UCHAR_COLLISION in regint.h and regenc.h.
2006/11/21: [bug]  invalid array access.
2006/11/21: [impl] escape UChar collision from config.h.
2006/11/20: [new]  add Hiragana/Katakana properties into Shift_JIS.
2006/11/20: [impl] fix CR_Katakana[] values in EUC-JP.
2006/11/17: [impl] declare strend hash table functions in regint.h.
2006/11/17: [impl] move property list functions to regenc.c.
2006/11/17: [new]  add Hiragana/Katakana properties into EUC-JP.
2006/11/15: [impl] remove NOT_RUBY from AM_CFLAGS.

2006/11/14: Version 5.2.0

2006/11/14: [impl] remove program codes for Ruby.
2006/11/14: [impl] reduce program codes for Ruby.
2006/11/10: [bug]  0x24, 0x2b, 0x3c, 0x3d, 0x3e, 0x5e, 0x60, 0x7c, 0x7e
                   should be [:punct:].
2006/11/09: [new]  (thanks Byte)
                   add new character encoding CP1251.
2006/11/08: [impl] rename QUALIFIER -> QUANTIFIER.

2006/11/07: Version 5.1.0

2006/11/07: [dist] remove test.rb, testconv.rb and testconvu.rb.
2006/11/07: [bug]  get_case_fold_codes_by_str() should handle 'Ss' and 'sS'
                   combination for ess-tsett.
2006/11/07: [impl] apply_all_case_fold() doesn't need to return all
                   case character combination for multi-character folding.
                   (ONIGENC_CASE_FOLD_MULTI_CHAR)
2006/11/07: [bug]  (thanks Byte)
                   add { 0xa3, 0xb3 } to CaseFoldMap[] for KOI8-R.
2006/11/06: [spec] change ONIG_OPTION_FIND_LONGEST to search all of
                   the string range.
                   add USE_FIND_LONGEST_SEARCH_ALL_OF_RANGE.
2006/11/02: [impl] re-implement expand_case_fold_string() for
                   ONIGENC_CASE_FOLD_MULTI_CHAR.
2006/10/30: [impl] add NSTR_DONT_GET_OPTINFO flag.
2006/10/30: [impl] (thanks K.Takata)
                   add THREAD_SYSTEM_INIT and THREAD_SYSTEM_END.
2006/10/30: [bug]  (thanks Wolfgang Nadasi-Donner)
                   invalid offset value was used in STATE_CHECK_BUFF_INIT().
2006/10/27: [tune] speed up ONIGENC_MBC_CASE_FOLD() for UTF-16, UTF-32.
                   (ASCII code check)
2006/10/27: [tune] (thanks Kornelius Kalnbach)
                   String#scan for long string needs long time compare with
                   old Ruby
                   by initialization time for combination explosion check
                   ex. ("test " * 100_000).scan(/\w*\s?/)
                   change STATE_CHECK_BUFF_MAX_SIZE from 0x8000000 to 0x8000.
                   reduce initialization area of state_check_buff.
2006/10/25: [impl] add DISABLE_CASE_FOLD_MULTI_CHAR().

2006/10/23: Version 5.0.1

2006/10/23: [bug]  should fold string in expand_case_fold_string().
2006/10/23: [bug]  (thanks Km)
                   too many case fold/unfold expansion problem.
                   don't expand and set ambig flag to the string node.
                   (except ONIGENC_CASE_FOLD_MULTI_CHAR).
2006/10/23: [bug]  (thanks K.Takata)
                   invalid \p{Alnum}, \p{ASCII}, [:alnum:], [:ascii:].
                   fix OnigEncAsciiCtypeTable[] etc...
2006/10/23: [spec] (thanks K.Takata)
                   add [:word:] POSIX bracket.
2006/10/23: [bug]  (thanks K.Takata)
                   \p{Word} doesn't work.
2006/10/20: [impl] don't expand for AMBIG_FLAG string in
                   expand_case_fold_string().

2006/10/19: Version 5.0.0

2006/10/18: [bug]  ONIGENC_GET_CASE_FOLD_CODES_MAX_NUM should be 13.
2006/10/18: [impl] remove unused functions.
2006/10/18: [dist] update documents.
2006/10/18: [API]  move OnigMetaCharTableType to OnigSyntaxType.
2006/10/18: [dev]  add too/unicode_fc.rb, unicode_pc.rb.
2006/10/18: [dist] remove MANIFEST-RUBY from distribution.
2006/10/18: [bug]  return duplicated code in
                   onigenc_unicode_get_case_fold_codes_by_str().
2006/10/18  [API]  remove ONIG_SYN_OP2_CHAR_PROPERTY_PREFIX_IS.
2006/10/18: [dev]  add tool/19.
2006/10/18: [dist] remove target 19 from Makefile.am.
2006/10/17: [dist] add enc/unicode.c to target 19 of win32/Makefile.
2006/10/17: [impl] change type for escape VC++ warning.
2006/10/17: [API]  rename ONIGENC_CASE_FOLD_NONE to ONIGENC_CASE_FOLD_MIN.
2006/10/17: [dist] remove INSTALL-RUBY from distribution.
2006/10/17: [dist] update LTVERSION to "2:0:0".
2006/10/17: [impl] remove warnings for [make CFLAGS="-g -O2 -Wall"]
                   in the case USE_UNICODE_PROPERTIES and
                   USE_UNICODE_CASE_FOLD_MULTI_CHAR are undefined.
2006/10/17: [impl] remove warnings for [make CFLAGS="-g -O2 -Wall"].
2006/10/17: [impl] re-implement onigenc_unicode_apply_all_case_fold().
                   multi-char by case folded char-class is treated as
                   caseless-string (ambig flag on).
                   enable OP_EXACT1_IC and OP_EXACTN_IC.
2006/10/16: [bug]  unfold expand for 1->2, 1->3 folding in
                   onigenc_unicode_apply_all_case_fold().
                   add CaseFoldExpand_12[], CaseFoldExpand_13[].
2006/10/16: [bug]  (thanks Akinori Musha)
                   first argument of rb_warn() should be format string.
2006/10/16: [impl] add msa.state_check_buff_size initialization
                   in onig_search().
2006/10/16: [spec] re-implement Unicode Caseless Match codes.
2006/10/10: [bug]  should call onig_st_free_table() in
                   onig_free_shared_cclass_table().
2006/10/10: [impl] remove OnigCompCaseFoldCodes.
2006/10/10: [impl] remove onigenc_ascii_is_mbc_ambiguous() and
                   onigenc_mbn_is_mbc_ambiguous().
2006/10/10: [API]  remove is_mbc_ambiguous() member from OnigEncodingType.
2006/10/10: [API]  rename onig_set_default_ambig_flag() to
                   onig_set_default_case_fold_flag(),
                   onig_get_default_ambig_flag() to
                   onig_get_default_case_fold_flag(),
                   onig_get_ambig_flag() to onig_get_case_fold_flag().
2006/10/10: [API]  rename ambig_flag to case_fold_flag.
2006/10/10: [API]  rename OnigAmbigType to OnigCaseFoldType.
2006/10/10: [impl] rename ONIGENC_IS_CODE_SB_WORD() to IS_CODE_SB_WORD()
                   and move to regint.h.
2006/10/10: [impl] remove OP_WORD_SB and OP_WORD_MB.
2006/10/10: [impl] remove OP_EXACT1_IC and OP_EXACTN_IC from match_at().
2006/10/10: [impl] should free new_str in expand_case_fold_string().
2006/10/06: [dist] add test entries to sample/encode.c.
2006/10/06: [impl] re-implement caseless match (case-fold).
2006/10/06: [impl] expand string node by case fold variations.
                   add expand_case_fold_string().
2006/10/05: [spec] rename OnigCompAmbigCodeItem to OnigCaseFoldCodeItem.
2006/10/05: [spec] add apply_all_case_fold() and get_case_fold_codes_by_str()
                   to OnigEncodingType.
2006/10/05: [spec] remove ambig_flag, get_all_pair_ambig_codes() and
                   get_all_comp_ambig_codes() member from OnigEncodingType.
2006/10/03: [impl] rename mbc_to_normalize() to mbc_case_fold().
2006/10/03: [spec] rename ONIGENC_AMBIGUOUS_MATCH_XXX
                   to ONIGENC_CASE_FOLD_XXX.
                   rename ONIGENC_CASE_FOLD_COMPOUND
                   to ONIGENC_CASE_FOLD_MULTI_CHAR.
2006/10/02: [impl] remove all ONIG_RUBY_M17N part.
2006/09/29: [impl] initialize state_check_buff_size in STATE_CHECK_BUFF_INIT().
                   make valgrind happy.
2006/09/22: [impl] remove parse time ctype values (CTYPE_WORD etc...)
2006/09/22: [ruby] enable USE_BACKREF_AT_LEVEL for Ruby mode.
2006/09/22: [spec] (thanks Allan Odgaard)
                   allow upper case letter as the first character
                   of group name.
                   fetch_name() and fetch_name_with_level()
2006/09/21: [impl] convert to ascii for parameter string in
                   onig_error_code_to_str().
                   add enc member into OnigErrorInfo.
2006/09/21: [dist] update documents for Unicode Property.
2006/09/21: [new]  add Unicode Properties. (enc/unicode.c)
                   Any, Assigned, C, Cc, L, Lm, Arabic, Greek etc...
2006/09/21: [impl] add USE_UNICODE_PROPERTIES into regenc.h.
2006/09/21: [impl] remove USE_UNICODE_FULL_RANGE_CTYPE.
2006/09/20: [impl] change ONIGENC_CTYPE_XXXX to sequential values.
                   add BIT_CTYPE_XXXX bit flags to regenc.h.
                   update XXXX_CtypeTable[] for BIT_CTYPE_ALNUM.
2006/09/19: [memo] move from CVS to Subversion (1.3.2).
2006/09/19: [impl] (thanks KOYAMA Tetsuji)
                   HAVE_STDARG_PROTOTYPES was not defined in Mac OS X
                   by Xcode 2.4(gcc 4.0.1) problem. [php-dev 1312] etc...
2006/09/15: [bug]  (thanks Allan Odgaard)
                   out of range access in bm_search_notrev().
                   (p < s)
2006/09/13: [impl] add ONIGENC_CTYPE_ENC_EXT flag.
2006/09/13: [spec] remove 'Is' prefix check for property name
                   from fetch_char_property_to_ctype().
2006/09/13: [API]  add property_name_to_ctype member to OnigEncodingType.
2006/09/12: [spec][ruby] add ONIG_SYN_OP2_ESC_P_BRACE_CHAR_PROPERTY and
                   ONIG_SYN_OP2_ESC_P_BRACE_CIRCUMFLEX_NOT to OnigSyntaxRuby.

2006/09/08: Version 4.4.2

2006/09/08: [test] success in ruby 1.9.0 (2006-08-22) [i686-linux].
2006/09/08: [bug]  (thanks K.Takata)
                   out of range access in bm_search_notrev().
2006/09/04: [spec] (thanks K.Takata)
                   allow look-behind in negative look-behind.
                   ex. /(?<!(?<=a)b|c)d/

2006/08/29: Version 4.4.1

2006/08/29: [test] success in ruby 1.9.0 (2006-08-22) [i686-linux].
2006/08/29: [dist] (thanks Seiji Masugata)
                    add configure option --enable-combination-explosion-check

2006/08/25: Version 4.4.0

2006/08/25: [test] success in ruby 1.9.0 (2006-08-22) [i686-linux].
2006/08/25: [impl] add_state_check_num() should be enclosed in
                   ifdef USE_COMBINATION_EXPLOSION_CHECK.
2006/08/23: [spec] config USE_COMBINATION_EXPLOSION_CHECK is enabled
                   in Ruby mode only.
2006/08/22: [impl] remove last line comma in enum OpCode.
2006/08/22: [impl] remove OP_STATE_CHECK_ANYCHAR_STAR_PEEK_NEXT and
                   OP_STATE_CHECK_ANYCHAR_ML_STAR_PEEK_NEXT.
2006/08/22: [impl] remove OP_BACKREF3.

2006/08/21: Version 4.3.1

2006/08/21: [test] success in ruby 1.9.0 (2006-07-28) [i686-linux].
2006/08/21: [impl] change stack type values
                   and re-define STK_MASK_TO_VOID_TARGET etc...
2006/08/21: [impl] set repeat_range[].upper to 0x7fffffff as infinite.
2006/08/21: [impl] add STATE_CHECK_BUFF_MALLOC_THRESHOLD_SIZE.
2006/08/21: [impl] reduce (?:a*){n,m}, (?:a+){n,m} => (?:a*){n,n}, (?:a+){n,n}
2006/09/21: [impl] reduce (a*){n,m}, (a+){n,m} => (a*){n,n}, (a+){n,n}
                   if backreference is not used.
2006/08/17: [bug]  should check scan_env.num_call > 0 for backrefed pattern
                   in combination explosion check.

2006/08/17: Version 4.3.0

2006/08/17: [test] success in ruby 1.9.0 (2006-07-28) [i686-linux].
2006/08/17: [new]  add config USE_COMBINATION_EXPLOSION_CHECK.
                   check /(.+)*/, /(\s*foo\s*)*/ etc...
            [API]  add num_comb_exp_check member in regex_t.
            [dist] change LTVERSION value to "1:0:0" in configure.in.
2006/08/15: [bug]  OP_REPEAT_INC process in match_at().
                   should check repeat-count >= range-upper and
                   range-upper may be infinite.

2006/08/11: Version 4.2.3

2006/08/11: [test] success in ruby 1.9.0 (2006-07-28) [i686-linux].
2006/08/10: [impl] remove double call in set_qualifier().
2006/08/10: [impl] remove by_number member in QualifierNode.
2006/08/09: [impl] remove a comma at the end of enum ReduceType
                   for escape warning on Mac OS X.
2006/08/07: [impl] remove warning in regcomp.c.
2006/08/07: [spec] move definition of USE_BACKREF_AT_LEVEL into NOT_RUBY.

2006/08/03: Version 4.2.2

2006/08/03: [test] success in ruby 1.9.0 (2006-07-28) [i686-linux].
2006/08/03: [bug]  (thanks Hiroyuki Yamamoto)
                   segmentation fault in regexec(). (POSIX API)
2006/08/02: [bug]  combination of \G in look-ahead/look-behind and other
                   anchors(\A, \z, \Z) cause invalid result.
                   ex. /(?!\G)a\z/.match("ba")
                   start arg. of MATCH_ARG_INIT() should be original
                   arg. of onig_search().

2006/07/31: Version 4.2.1

2006/07/31: [test] success in ruby 1.9.0 (2006-07-28) [i686-linux].
2006/07/31: [bug] (thanks Kimura Minoru)
                   re-implement bm_search_notrev().
2006/07/31: [impl] bm_search_notrev() refactoring.
2006/07/31: [bug]  (thanks Kimura Minoru)
                   fix incomplete multibyte string in exact info.
2006/07/31: [impl] (thanks Seiji Masugata)
                   remove cast in va_init_list() for Intel C Compiler.

2006/07/18: Version 4.2.0

2006/07/18: [test] success in ruby 1.9.0 (2006-03-01) [i686-linux].
2006/07/18: [new]  (thanks Wolfgang Nadasi-Donner)
                   add back reference with nest level.
                   \k<name+n>, \k<name-n>
2006/07/11: [impl] change long to unsigned long for ONIG_OPTION_XXX
                   and ONIG_SYN_XXX number literals.

2006/07/03: Version 4.1.2

2006/07/03: [test] success in ruby 1.9.0 (2006-03-01) [i686-linux].
2006/07/03: [spec] (thanks Wolfgang Nadasi-Donner)
                   allow \G in look-behind.
                   add ANCHOR_BEGIN_POSITION flag in setup_tree().
2006/06/12: [impl] (thanks matz)
                    fix cast from char* to const char*
                    in onig_snprintf_with_pattern().
                    fix cast from char* to const char*
                    for PopularQStr[] and ReduceQStr[].

2006/05/22: Version 4.1.1

2006/05/22: [test] success in ruby 1.9.0 (2006-03-01) [i686-linux].
2006/05/22: [impl] add position string argument to STACK_BASE_CHECK().
2006/05/22: [bug]  (thanks NARUSE, Yui)
                   add STK_NULL_CHECK_END to IS_TO_VOID_TARGET().
                   ex. core dump in
                   /(?<pare>\(([^\(\)]++|\g<pare>)*+\))/.match('((a))')

2006/05/15: Version 4.1.0

2006/05/15: [test] success in ruby 1.9.0 (2006-03-01) [i686-linux].
2006/05/15: [impl] thread atomic changes for onig_end() and
                   onig_free_node_list().
2006/05/15: [test] success in ruby 1.9.0 (2006-03-01) [i686-linux].
2005/05/15: [dist] update API, API.ja, FAQ, FAQ.ja.
2006/05/15: [spec] remove onig_recompile(), onig_recompile_deluxe()
                   and re_recompile_pattern().
                   add config USE_RECOMPILE_API.
2006/05/15: [impl] improved thread safe implementation of onig_search()
                   and onig_match().

2006/05/11: Version 4.0.4

2006/05/11: [test] success in ruby 1.9.0 (2006-03-01) [i686-linux].
2006/05/11: [bug]  (thanks Yuji Kaneda)
                   dead-lock in onig_end().
2006/05/11: [dist] update index.html.

2006/05/08: Version 4.0.3

2006/05/08: [test] success in ruby 1.9.0 (2006-03-01) [i686-linux].
2006/05/08: [bug]  (thanks Allan Odgaard)
                   Segmentation fault in backward search.
                   ex. /^\t.*$/
2006/04/18: [dist] update index.html.
2006/04/05: [dist] update index.html.
2006/03/24: [dist] update doc/RE, doc/RE.ja.

2006/03/23: Version 4.0.2

2006/03/22: [test] success in ruby 1.9.0 (2006-03-01) [i686-linux].
2006/03/22: [impl] add both of ONIG_OPTION_DONT_CAPTURE_GROUP
                   and ONIG_OPTION_CAPTURE_GROUP check.
2006/03/22: [spec] add error code ONIGERR_INVALID_COMBINATION_OF_OPTIONS.
2006/03/22: [impl] remove USE_NAMED_GROUP condition from
                   ONIG_OPTION_DONT_CAPTURE_GROUP check in parse_effect().
2006/03/22: [new]  add API onig_noname_group_capture_is_active().
2006/03/01: [spec] rename regex object type from regex_t to OnigRegexType.
                   add typedef OnigRegexType regex_t
                   unless ONIG_ESCAPE_REGEX_T_COLLISION is defined.
2006/02/27: [spec] change ONIG_MAX_MULTI_BYTE_RANGES_NUM from 1000
                   to 10000.  (for docdiff program)
2006/02/17: [dist] change COPYING year 2005 -> 2006.

2006/02/07: Version 4.0.1

2006/02/07: [test] success in ruby 1.9.0 (2005-11-28) [i686-linux].
2006/02/07: [bug]  memory leaks in onig_free_shared_cclass_table().
2006/02/03: [ruby] add -m 0644 option to install command in "make 19".
2006/02/03: [impl] rename ANCHOR_ANYCHAR_STAR_PL to ANCHOR_ANYCHAR_STAR_ML.
                   change from IS_POSIXLINE() to IS_MULTILINE()
                   for ANCHOR_ANYCHAR_START/_ML decision
                   in optimize_node_left().
2006/01/26: [dist] update index.html for Oniguruma 2.5.3.
2006/01/25: [dist] update URL in index.html.

2006/01/24: Version 4.0.0

2006/01/24: [test] success in ruby 1.9.0 (2005-11-28) [i386-cygwin].
2006/01/24: [test] success in ruby 1.9.0 (2005-11-28) [i686-linux].
2006/01/24: [dist] remove warnings from sample/encode.c.
2006/01/24: [dist] change install description in README(.ja).
2006/01/24: [dist] remove re.c.XXX.patch from distribution and CVS.
2006/01/24: [dist] --- support shared library ---
                   use GNU libtool/automake.
                   change configure.in and add Makefile.am, sample/Makefile.am.
                   add AUTHORS file.
2006/01/24: [dist] test programs return exit code -1 when test fails.
2006/01/24: [bug] (thanks KIMURA Koichi)
                   invalid syntax definition in ONIG_SYNTAX_GREP.
                   ONIG_SYN_OP_BRACE_INTERVAL
                   -> ONIG_SYN_OP_ESC_BRACE_INTERVAL
2006/01/23: [dist] fix configure.in for onig-config.
2006/01/19: [new]  add new config USE_UNICODE_ALL_LINE_TERMINATORS.
                   (U+000d, U+0085, U+2028, U+2029)
2005/12/29: [dist] change pmatch array size to 25 in testconv.rb.
2005/12/26: [dist] fix name in test.rb.
2005/12/26: [dist] update index.html for 2.5.1.

2005/11/29: Version 3.9.1

2005/11/29: [test] success in ruby 1.9.0 (2005-11-28) [i686-linux].
2005/11/24: [test] success in ruby 1.9.0 (2005-08-09) [i686-linux].
2005/11/21: [test] success in ruby 1.9.0 (2005-11-20) [i386-cygwin].
2005/11/21: [bug]  (thanks Allan Odgaard)
                   utf-8 character comments in extended mode leads
                   invalid result.
                   ex. /(?x)(?<= # <any-utf-8 multibyte char>o\n~) /
                   fix onigenc_unicode_is_code_ctype() and
                   utf8_is_code_ctype().
2005/11/20: [bug]  (thanks MATSUMOTO Satoshi) (thanks Isao Sonobe)
                   begin-line anchor and BM search optimization leads
                   invalid result in UTF-16/32.
                   fix in set_optimize_exact_info().

2005/11/20: Version 3.9.0

2005/11/20: [test] success in ruby 1.9.0 (2005-11-20) [i386-cygwin].
2005/11/20: [test] success in ruby 1.9.0 (2005-10-18) [i386-cygwin].
2005/11/20: [new]  add new config USE_CRNL_AS_LINE_TERMINATOR.
                   (!!! NO SUPPORT experimental option !!!)
2005/11/15: [bug]  (thanks Allan Odgaard)
                   tok->escape was not cleared in fetch_token_in_cc().
                   ex. [\s&&[^\n]] makes wrong result.
2005/10/18: [impl] (thanks nobu)
                   change sjis_mbc_enc_len()
                   and node_new_cclass_by_codepoint_range() scope to static.
2005/09/05: [dist] remove link to MultiFind.
2005/09/01: [dist] add link to yagrep.

2005/08/23: Version 3.8.9

2005/08/23: [test] success in ruby 1.9.0 (2005-08-09) [i686-linux].
2005/08/23: [inst] fix Makefile.in for make ctest/ptest.

2005/08/23: Version 3.8.8

2005/08/23: [test] success in ruby 1.9.0 (2005-08-09) [i686-linux].
2005/08/23: [impl] split is_code_in_cc() from onig_is_code_in_cc().
2005/08/23: [impl] should check DATA_ENSURE() at OP_CCLASS_NODE in match_at().
2005/08/23: [impl] (thanks akr)
                   add ONIG_OPTION_MAXBIT for escape conflict with
                   Ruby's option.
2005/08/22: [impl] escape GCC 4.0 warnings for testc.c.
2005/08/22: [bug]  (thanks nobu, matz) [ruby-dev:26840]
                   UTF-8 0xFE, 0xFF handling bug in code_is_in_cclass_node().
                   abort on /\S*/ =~ "\xfe"
2005/08/22: [impl] escape GCC 4.0 warnings for sample/*.c.
2005/08/22: [impl] fix testconvu.rb.
2005/08/22: [impl] escape GCC 4.0 warnings.

2005/08/09: Version 3.8.7

2005/08/09: [test] success in ruby 1.9.0 (2005-08-09) [i686-linux].
2005/08/09: [bug]  (thanks Allan Odgaard)
                   should not call enc_len() for s == range
                   in onig_search().
2005/08/01: [dist] add mkdir $prefix, mkdir $exec_prefix to make install.

2005/07/27: Version 3.8.6

2005/07/27: [test] success in ruby 1.9.0 (2005-07-26) [i686-linux].
2005/07/27: [impl] update onig-config.in.
2005/07/26: [new]  (thanks Yen-Ju Chen)
                   add Oniguruma configuration check program.
                   (onig-config.in)

2005/07/14: Version 3.8.5

2005/07/14: [test] success in ruby 1.9.0 (2005-07-14) [i686-linux].
2005/07/11: [test] success in ruby 1.9.0 (2005-07-04) [i686-linux].
2005/07/11: [bug]  (thanks nobu) [ruby-dev:26505]
                   invalid handling for /\c\x/ and /\C-\x/.
                   fix fetch_escaped_value().
2005/07/05: [impl] (thanks Alexey Zakhlestine)
                   escape GCC 4.0 warnings.

2005/07/01: Version 3.8.4

2005/07/01: [test] success in ruby 1.9.0 (2005-07-01) [i686-linux].
2005/06/30: [test] success in ruby 1.9.0 (2005-06-28) [i686-linux].
2005/06/30: [dist] add GB 18030 test to sample/encode.c.
2005/06/30: [impl] escape warning of gb18030_left_adjust_char_head().
2005/06/30: [new]  (contributed by KUBO Takehiro)
                   add new character encoding ONIG_ENCODING_GB18030.
2005/06/30: [bug]  invalid ctype check for multibyte encodings.
                   ("graph", "print")
                   fix onigenc_mb2/4_is_code_ctype(),
                   eucjp_is_code_ctype() and sjis_is_code_ctype().
2005/06/30: [bug]  invalid conversion from code point to mbc in
                   onigenc_mb4_code_to_mbc().

2005/06/28: Version 3.8.3

2005/06/28: [test] success in ruby 1.9.0 (2005-06-28) [i686-linux].
2005/06/27: [test] success in ruby 1.9.0 (2005-05-31) [i686-linux].
2005/06/27: [bug]  (thanks Wolfgang Nadasi-Donner)
                   invalid check for never ending recursion.
                   lower zero quantifier should be treated as
                   a non-recursive call alternative.
                   ex. /(?<bal>[^()]*(\(\g<bal>\)[^()]*)*)/
2005/06/15: [impl] add divide_ambig_string_node_sub().
2005/06/15: [dist] add a test to sample/encode.c.
2005/06/10: [new]  add ONIG_SYNTAX_PERL_NG. (Perl + named group)

2005/06/01: Version 3.8.2

2005/06/01: [test] success in ruby 1.9.0 (2005-05-31) [i686-linux].
2005/05/31: [dist] add doc/FAQ and doc/FAQ.ja.
2005/05/31: [impl] minor change in node_new().
2005/05/30: [test] success in ruby 1.9.0 (2005-05-11) [i686-linux].
2005/05/30: [bug]  (thanks Allan Odgaard)
                   FreeNodeList null check should be on thread-atomic
                   in node_new().

2005/05/11: Version 3.8.1

2005/05/11: [test] success in ruby 1.9.0 (2005-05-11) [i386-mswin32].
2005/05/11: [dist] update win32/Makefile (make 19).
2005/05/11: [test] success in ruby 1.9.0 (2005-05-11) [i686-linux].
2005/05/06: [test] success in ruby 1.9.0 (2005-05-06) [i686-linux].
2005/05/06: [impl] (thanks nobu) [ruby-core:4815]
                   add #ifdef USE_VARIABLE_META_CHARS to goto label.
2005/04/25: [test] success in ruby 1.9.0 (2005-04-25) [i686-linux].
2005/04/25: [impl] change DEFAULT_WARN_FUNCTION and DEFAULT_VERB_WARN_FUNCTION
                   to onig_rb_warn() and onig_rb_warning().

2005/04/15: Version 3.8.0

2005/04/15: [test] success in ruby 1.9.0 (2005-04-14) [i686-linux].
2005/04/01: [test] success in ruby 1.9.0 (2005-03-24) [i686-linux].
2005/04/01: [impl] (thanks Joe Orton)
                   (thanks Moriyoshi Koizumi)
                   many const-ification to many *.[ch] files.

2005/03/25: Version 3.7.2

2005/03/25: [test] success in ruby 1.9.0 (2005-03-24) [i686-linux].
2005/03/23: [test] success in ruby 1.9.0 (2005-03-20) [i686-linux].
2005/03/23: [test] success in ruby 1.9.0 (2005-03-08) [i686-linux].
2005/03/23: [new]  add ONIG_SYNTAX_ASIS.
2005/03/23: [new]  add ONIG_SYN_OP2_INEFFECTIVE_ESCAPE.
2005/03/09: [spec] rename MBCTYPE_XXX to RE_MBCTYPE_XXX. (GNU API)
2005/03/08: [test] success in ruby 1.9.0 (2005-03-08) [i686-linux].
2005/03/08: [impl] (thanks matz) [ruby-dev:25783]
                   should not allocate memory for key data in st.c.
                   move st_*_strend() functions from st.c. fixed some
	           potential memory leaks.
                   (imported from Ruby 1.9 2005-03-08)

2005/03/07: Version 3.7.1

2005/03/07: [test] success in ruby 1.9.0 (2005-03-07) [i686-linux].
2005/03/07: [impl] (thanks Rui Hirokawa)
                   add ONIG_ESCAPE_UCHAR_COLLISION.
                   rename UChar to OnigUChar in oniguruma.h.
2005/03/07: [impl] remove declarations for Ruby in oniggnu.h.
2005/03/05: [bug]  ANCHOR_ANYCHAR_STAR didn't work in onig_search().
2005/03/01: [dist] remove oniggnu.h from MANIFEST-RUBY.
                   remove oniggnu.h from make 19.
2005/03/01: [bug]  (thanks matz) [ruby-dev:25778]
                   uninitialized member (OptEnv.backrefed_status)
                   was used.

2005/02/19: Version 3.7.0

2005/02/19: [test] success in ruby 1.9.0 (2005-02-19) [i386-cygwin].
2005/02/19: [new]  (thanks Minero Aoki)
                   add onig_region_set().
2005/02/19: [API]  change onig_region_init() to extern.
2005/02/19: [dist] remove reggnu.c from MANIFEST-RUBY.
                   remove reggnu.c from make 19.
2005/02/19: [dist] update doc/API and doc/API.ja.
2005/02/19: [test] success in ruby 1.9.0 (2005-02-19) [i386-cygwin].
2005/02/19: [impl] (thanks Alexey Zakhlestine)
                   change UChar* to const UChar* in oniguruma.h,
                   regenc.h and regparse.h.
2005/02/13: [impl] change UChar* to const UChar* in oniguruma.h and
                   onigposix.h and st.h.
2005/02/12: [test] success in ruby 1.9.0 (2005-02-11) [i386-cygwin].
2005/02/12: [bug]  (thanks nobu) [ruby-dev:25676]
                   type_cclass_hash() fix overrun.
2005/02/09: [test] success in ruby 1.9.0 (2005-02-09) [i686-linux].
2005/02/09: [spec] add RE_OPTION_FIND_NOT_EMPTY etc.. to oniggnu.h.
2005/02/09: [dist] remove hash.c.patch.
2005/02/07: [impl] remove re_mbctab, mbctab_ascii etc...
                   (USE_COMPATIBILITY_FOR_RUBY_EXTENSION_LIBRARY)

2005/02/04: Version 3.6.0

2005/02/04: [test] success in ruby 1.9.0 (2005-02-04) [i686-linux].
2005/02/01: [bug]  add key_free() call to st_free_table().
2005/02/01: [new]  add onig_get_default_ambig_flag() and
                   onig_set_default_ambig_flag().
2005/02/01: [dist] update MANIFEST-RUBY.
2005/01/31: [test] success in ruby 1.9.0 (2005-01-29) [i686-linux].
2005/01/31: [spec] remove ONIGENC_AMBIGUOUS_MATCH_COMPOUND
                   from ONIGENC_AMBIGUOUS_MATCH_DEFAULT.
2005/01/31: [dist] update Makefile.in (make 19).
2005/01/29: [memo] (thanks Kazuo Saito)
                   Oniguruma 3.5.4 was merged to Ruby 1.9.0.
2005/01/28: [impl] (thanks UK-taniyama)
                   add extern "C" { } directive to oniguruma.h, oniggnu.h
                   and onigposix.h for C++.
2005/01/25: [impl] remove nested function call for xxx_code_to_mbclen().
                   (euc_kr.c, euc_tw.c, big5.c)

2005/01/19: Version 3.5.4

2005/01/19: [test] success in ruby 1.9.0 (2005-01-05) [i686-linux].
2005/01/19: [bug]  (thanks Isao Sonobe)
                   callback function argument name_end of onig_foreach_name()
                   was wrong.
                   name key of name table should be null terminated for
                   character encoding length.
                   add strdup_with_null(), rename onig_strdup() to k_strdup().
                   use e->name_len in i_names().
2005/01/17: [impl] (thanks UK-taniyama)
                   add HAVE_SYS_TYPES_H to config.h.in.

2005/01/13: Version 3.5.3

2005/01/13: [test] success in ruby 1.9.0 (2005-01-05) [i686-linux].
2005/01/13: [bug]  ignore case match bug.
                   ex. /s+/iu.match("SSSSS") ==> [4..5]
                   fix OP_EXACT1_IC, OP_EXACTN_IC process.
2005/01/13: [bug]  (thanks Isao Sonobe)
                   ignore case match bug.
                   ex. /is/iu.match("ss") fail.
                   fix str_lower_case_match() etc.

2005/01/05: Version 3.5.2

2005/01/05: [test] success in ruby 1.9.0 (2005-01-05) [i686-linux].
2005/01/05: [test] success in ruby 1.9.0 (2004-12-16) [i686-linux].
2005/01/05: [bug]  (thanks Isao Sonobe)
                   ignore case match bug.
                   ex. /s+/iu.match("sssss") ==> [4..5]
                   fix OP_EXACT1_IC, OP_EXACTN_IC process.
2005/01/05: [bug]  (thanks Isao Sonobe)
                   group name table should be renumbered.
                   add onig_renumber_name_table().
2004/12/24: [dist] remove file onigcmpt200.h.

2004/12/17: Version 3.5.1

2004/12/17: [dist] add INSTALL-RUBY to archive.
2004/12/16: [test] success in ruby 1.9.0 (2004-12-16) [i686-linux].
2004/12/16: [dist] update hash.c.patch.
2004/12/15: [bug]  (thanks matz)
                   char > 127 should be cast to unsigned char. (utf8.c)
2004/12/13: [impl] add HAVE_PROTOTYPES and HAVE_STDARG_PROTOTYPES definition
                   to oniguruma.h in the case __cplusplus.
2004/12/06: [dist] update doc/RE and doc/RE.ja.
2004/12/03: [impl] (thanks nobu)
                   st.h fix prototype for C++.

2004/12/03: Version 3.5.0

2004/12/02: [test] success in ruby 1.9.0 (2004-12-02) [i686-linux].
2004/12/01: [test] success in ruby 1.9.0 (2004-12-01) [i386-mswin32].
2004/12/01: [dist] add make targets 19 and 19up to win32/Makefile.
2004/12/01: [test] success in ruby 1.9.0 (2004-12-01) [i386-cygwin].
2004/12/01: [test] success in ruby 1.9.0 (2004-12-01) [i686-linux].
2004/12/01: [impl] double cast for escape warning in Cygwin.
                  (HashDataType* )((void* )(&e)) in regparse.c
2004/12/01: [test] success in ruby 1.9.0 (2004-11-30) [i686-linux].
2004/12/01: [tune] change implementation of clear_opt_map_info().
                   (which was 10-16% cost in gprof result for my test program)
2004/12/01: [dist] remove regex.c from distribution files.
2004/11/30: [memo] remove targets 16 and 18 from Makefile.in.
2004/11/30: [test] success in ruby 1.9.0 (2004-11-30) [i686-linux].
2004/11/30: [inst] add "cp -p st.[ch] st.[ch].ruby_orig" to "make 19".
2004/11/30: [tune] map_position_value() return 20 if code is 0
                   and minimum enclen > 1.
2004/11/30: [test] success in ruby 1.9.0 (2004-11-29) [i686-linux].
2004/11/30: [impl] minor changes for multi-thread in regexec.c and regcomp.c.
2004/11/30: [impl] change THREAD_PASS_LIMIT_COUNT value from 10 to 8.
2004/11/30: [impl] add THREAD_ATOMIC_XXX to FreeNodeList access in regparse.c
2004/11/29: [impl] add USE_MULTI_THREAD_SYSTEM.
2004/11/29: [memo] add hash.c.patch to CVS.
2004/11/29: [dist] change mail address to 'sndgk393 AT ...'
2004/11/29: [dist] add -s option (silent mode) to test.rb.
2004/11/29: [tune] change THRESHOLD_RANGE_NUM_FOR_SHARE_CCLASS value
                   from 20 to 8.
2004/11/29: [inst] add make target "19up".
2004/11/29: [dist] change Oniguruma Home Page URL.
2004/11/29: [impl] remove onig_is_in_code_range_array().
2004/11/29: [dist] fix doc/RE and RE.ja (character types).
2004/11/26: [dist] fix win32/Makefile.
2004/11/26: [dist] fix doc/RE and RE.ja (multibyte character types).
2004/11/26: [impl] add onig_free_shared_cclass_table().
2004/11/26: [impl] move definition USE_UNICODE_FULL_RANGE_CTYPE to regenc.h.
2004/11/26: [impl] add opcode OP_CCLASS_NODE.
2004/11/26: [impl] move definition of CClassNode to regint.h.
2004/11/26: [impl] add type PointerType in regint.h.
2004/11/25: [impl] remove ONIGENC_CTYPE_MOD_NOT.
2004/11/25: [impl] rename onig_node_new_cclass_by_codepoint_range to
                   node_new_cclass_by_codepoint_range.
2004/11/25: [impl] remove get_type_cc_node method from OnigEncodingType.
2004/11/25: [impl] move implementation of shared char-class from enc/*.c
                   to regparse.c.
2004/11/25: [dist] add hash.c.patch for Ruby 1.9 hash.c change.
2004/11/22: [impl] change utf8_get_type_node().
2004/11/22: [impl] add ONIGENC_CTYPE_MOD_NOT.
2004/11/22: [bug]  (thanks MIYAMUKO Katsuyuki)
                   ruby make test fail in HP-UX B.11.23 ia64.
                   should use tok->u.code instead of tok->u.c in
                   the case of TK_CODE_POINT.
2004/11/19: [bug]  (thanks Yoshida Masato)
                   invalid multibyte code causes segmentation fault.
                   ex.  /[\xFF-\xFF]/u
2004/11/19: [bug]  (thanks Yoshida Masato)
                   illegal check in char-class range in UTF-8.
                   ex.  s = "[\xC2\xA0-\xC3\xBE]"
                        p(Regexp.new(s, nil, "u") =~ "\xC3\xBE")
2004/11/18: [impl] add onig_node_new_cclass_by_codepoint_range().
2004/11/18: [impl] remove OnigCodePointRange type. (use OnigCodePoint[].)
2004/11/17: [bug]  (thanks nobu)
                   abort in "a".gsub(/a\Z/, "")
                   fix ONIGENC_STEP_BACK() argument in onig_search().
2004/11/16: [impl] add key2 member to st_table_entry in st.[ch].
                   change API of st for non-null terminated string key.
2004/11/16: [impl] add get_type_cc_node method to OnigEncodingType.
2004/11/15: [impl] add st.h and st.c from Ruby 1.9.
                   use st-hash always.
2004/11/12: [impl] change member 'not' of CClassNode to 'flags'.
                   add flags FLAG_CCLASS_NOT and FLAG_CCLASS_SHARE.
2004/11/12: [impl] add onig_is_in_code_range_array() to enc/unicode.c.
2004/11/12: [impl] fix CRWord in enc/unicode.c and MBWord in enc/utf8.c.
2004/11/11: [bug]  fix enc/utf8.c.
                   size 0 array initializer was compile error in VC++.
2004/11/09: [inst] (thanks Hiroki YAGITA)
                   change installed file mode to 0644.
2004/11/09: [bug]  (thanks UK-taniyama)
                   wrong definitions GET_RELADDR_INC(), GET_ABSADDR_INC()
                   etc... (NOT PLATFORM_UNALIGNED_WORD_ACCESS)
2004/11/09: [impl] type cast in regexec() for remove compile time warning.
                   (WIN32, regposix.c)
2004/11/08: [spec] fix Unicode character types.
                   0x00ad (soft hyphen) should be [:cntrl:] and [:space:] type.
                   [0x0009..0x000d], 0x0085 should be [:print:] type.
                   0x00ad should not be [:punct:] type.
2004/11/08: [inst] fix Makefile.in. (for make ctest/ptest/testcu)
2004/11/06: [impl] (thanks Kazuo Saito)
                   too many alternatives pattern causes core dump.
                   change implementation of onig_node_free().
2004/11/05: [spec] rename ONIGERR_END_PATTERN_AT_BACKSLASH to
                   ONIGERR_END_PATTERN_AT_ESCAPE.
2004/11/05: [impl] (thanks matz)
                   escape compile time warnings for x86-64 Linux.
                   StackIndex type int -> long
2004/11/05: [memo] (thanks Kazuo Saito)
                   Oniguruma 3.4.0 was merged to Ruby 1.9.0.

2004/10/30: Version 3.4.0

2004/10/30: [test] success in ruby 1.9.0 (2004-09-24) [i686-linux].
2004/10/30: [new]  add hexadecimal digit char type. (\h, \H)
                   syntax: ONIG_SYN_OP2_ESC_H_XDIGIT
2004/10/30: [bug]  (thanks Guy Decoux)
                   reluctant infinite repeat bug.
                   ex. /^[a-z]{2,}?$/.match("aaa") fail.
                   fix OP_REPEAT_INC_NG process in match_at().

2004/10/18: Version 3.3.1

2004/10/18: [test] success in ruby 1.9.0 (2004-09-24) [i686-linux].
2004/10/18: [impl] (thanks Imai Yasumasa)
                   enclose #include <sys/types.h> by #ifndef __BORLANDC__.
2004/10/18: [bug]  (thanks Imai Yasumasa)
                   memory access violation in select_opt_exact_info().
2004/09/25: [dist] fix doc/API and doc/API.ja.
2004/09/25: [bug]  fix OP_SEMI_END_BUF process in match_at() for
                   the case USE_NEWLINE_AT_END_OF_STRING_HAS_EMPTY_LINE
                   is not defined.

2004/09/17: Version 3.3.0

2004/09/17: [dist] add COPYING to program source files.
2004/09/17: [test] success in ruby 1.9.0 (2004-07-23) [i686-linux].
2004/09/17: [bug]  (thanks Isao Sonobe)
                   memory access violations in xxx_mbc_enc_len(),
                   and xxx_mbc_to_normalize() and
                   xxx_left_adjust_char_head().
                   add string range check in match_at() and onig_search().
2004/09/08: [dist] change mail address format.(kosako AT sofnec ...)

2004/09/04: Version 3.2.9

2004/09/04: [test] success in ruby 1.9.0 (2004-07-23) [i686-linux].
2004/09/04: [bug]  (thanks Bob Kerstetter and Richard Koch)
                   search fail in ignore case mode.
                   fix str_lower_case_match().
2004/09/04: [inst] (thanks Isao Sonobe)
                   clear sample directory in 'make clean'.
2004/09/04: [bug]  fix ONIGENC_AMBIGUOUS_MATCH_COMPOUND/ASCII/NONASCII
                   meanings in XXXXX_mbc_to_normalize() and
                   XXXXX_is_mbc_ambiguous().
2004/08/28: [bug]  fix ONIGENC_AMBIGUOUS_MATCH_COMPOUND/ASCII/NONASCII
                   meanings in iso_8859_XX_mbc_to_normalize() and
                   iso_8859_XX_is_mbc_ambiguous().

2004/08/24: Version 3.2.8

2004/08/24: [test] success in ruby 1.9.0 (2004-07-23) [i686-linux].
2004/08/24: [spec] add ONIG_SYN_FIXED_INTERVAL_IS_GREEDY_ONLY.
                   /a{n}?/ == /(?:a{n})?/
2004/08/24: [dist] fix doc/RE and doc/RE.ja.
2004/08/24: [bug]  (thanks starfish)
                   memory leak in set_optimize_exact_info().

2004/08/21: Version 3.2.7

2004/08/21: [test] success in ruby 1.8.2 (2004-07-28) [i686-linux].
                   (1.8.2 preview2)
2004/08/21: [test] success in ruby 1.9.0 (2004-07-23) [i686-linux].
2004/08/21: [bug]  (thanks Isao Sonobe) (thanks kage)
                   memory access violation in bm_search_notrev().
                   (forgotten to merge from 2.X)

2004/07/24: Version 3.2.6

2004/07/24: [test] success in ruby 1.9.0 (2004-07-23) [i686-linux].
2004/07/24: [test] success in ruby 1.8.2 (2004-07-16) [i686-linux].
2004/07/24: [bug]  fix warnings for regexec.c. (gcc 2.91.66)
2004/07/24: [memo] change version control system from Subversion
                   to CVS 1.11.17.
2004/07/20: [bug]  (thanks Isao Sonobe)
                   illegal result in negative character class in ignore case
                   mode. fix pair-ambig-codes process in parse_exp().
                   ex. /[^a]/i.match("A")
2004/07/20: [bug]  (thanks Isao Sonobe)
                   undefined bytecode error happens in UTF-16BE etc..
                   compile_length_cclass_node() was not consistent with
                   compile_cclass_node().

2004/07/01: Version 3.2.5

2004/07/01: [test] success in ruby 1.8.2 (2004-06-23) [i686-linux].
2004/07/01: [new]  add onig_get_syntax_{op,op2,behavior,options}.
2004/07/01: [bug]  (thanks Isao Sonobe)
                   invalid result in onig_capture_tree_traverse().
                   fix make_capture_history_tree().

2004/06/29: Version 3.2.4

2004/06/29: [test] success in ruby 1.8.2 (2004-06-23) [i686-linux].
2004/06/29: [new]  (thanks Isao Sonobe)
                   add onig_number_of_captures().

2004/06/25: Version 3.2.3

2004/06/25: [test] success in ruby 1.8.2 (2004-06-23) [i686-linux].
2004/06/25: [bug]  (thanks Isao Sonobe)
                   invalid result in onig_capture_tree_traverse().
                   fix make_capture_history_tree().

2004/06/24: Version 3.2.2

2004/06/24: [test] success in ruby 1.8.0 (2003-08-08) [i386-cygwin].
2004/06/24: [test] success in ruby 1.8.0 (2003-08-08) [i386-mswin32].
2004/06/24: [test] success in ruby 1.8.2 (2004-06-23) [i686-linux].
2004/06/24: [new]  (thanks Isao Sonobe)
                   add onig_number_of_capture_histories().
2004/06/24: [bug]  (thanks Isao Sonobe)
                   invalid char position match in UTF-16 and UTF-32.
                   add onigenc_always_false_is_allowed_reverse_match().

2004/06/17: Version 3.2.1

2004/06/17: [test] success in ruby 1.8.0 (2003-08-08) [i386-cygwin].
2004/06/17: [test] success in ruby 1.8.0 (2003-08-08) [i386-mswin32].
2004/06/17: [test] success in ruby 1.8.2 (2004-05-18) [i686-linux].
2004/06/17: [impl] should not use OP_REPEAT for (...)? even if target size
                   is long.
2004/06/17: [bug]  (thanks nobu) [ruby-dev:23703]
                   should use STACK_AT() instead of stkp in OP_REPEAT_INC.
                   add IN_VAR_REPEAT flag in setup_tree().
2004/06/16: [impl] change select_opt_exact_info() to use ByteValTable[].
2004/06/16: [impl] change map_position_value() table values.
2004/06/14: [impl] (thanks John Carter)
                   RelAddrType, AbsAddrType and LengthType change
                   from short int to int type for the very long string match.
2004/06/14: [bug]  (thanks Greg A. Woods)
                   fix nmatch argument of regexec() is smaller than
                   reg->num_mem + 1 case. (POSIX API)
2004/06/14: [spec] (thanks Greg A. Woods)
                   set pmatch to NULL if nmatch is 0 in regexec(). (POSIX API)

2004/06/10: Version 3.2.0

2004/06/10: [test] success in ruby 1.8.0 (2003-08-08) [i386-cygwin].
2004/06/10: [test] success in ruby 1.9.0 (2004-05-27) [i386-mswin32].
2004/06/10: [test] success in ruby 1.8.2 (2004-05-18) [i686-linux].
2004/06/10: [dist] add README.ja.
2004/06/10: [new]  add onig_copy_encoding().
2004/06/10: [API]  add encoding argument to onig_set_meta_char().
                   add meta_char_table member to OnigEncodingType.
2004/06/08: [dist] add doc/API.ja.
2004/06/07: [API]  add num_of_elements member to OnigCompileInfo.
2004/05/29: [memo] (thanks Kazuo Saito)
                   Oniguruma 3.1.0 was merged to Ruby 1.9.0.
2004/05/26: [impl] rename NST_SIMPLE_REPEAT to NST_STOP_BT_SIMPLE_REPEAT.
2004/05/26: [impl] doesn't need to check that target's simple repeat-ness
                   for EFFECT_MEMORY type node in setup_tree().

2004/05/25: Version 3.1.0

2004/05/25: [test] success in ruby 1.8.0 (2003-08-08) [i386-mswin32].
2004/05/25: [test] success in ruby 1.8.0 (2003-08-08) [i386-cygwin].
2004/05/25: [test] success in ruby 1.9.0 (2004-05-23) [i686-linux].
2004/05/25: [test] success in ruby 1.8.2 (2004-05-18) [i686-linux].
2004/05/25: [bug]  (thanks Masahiro Sakai) [ruby-dev:23560]
                   ruby -ruri -ve 'URI::ABS_URI =~
                                    "http://example.org/Andr\xC3\xA9"'
                   nested STK_REPEAT type stack can't backtrack repeat_stk[].
                   add OP_REPEAT_INC_SG and OP_REPEAT_INC_NG_SG.
2004/05/25: [new]  support UTF-32LE. (ONIG_ENCODING_UTF32_LE)
2004/05/25: [new]  support UTF-32BE. (ONIG_ENCODING_UTF32_BE)
2004/05/24: [impl] divide enc/utf16.c to utf16_be.c and utf16_le.c.
2004/05/24: [impl] add enc/unicode.c.
2004/05/24: [API]  change calling sequences of onig_new_deluxe() and
                   onig_recompile_deluxe().
                   define OnigCompileInfo type.
2004/05/21: [impl] perform ensure process for rb_trap_exec() in match_at().
                   add onig_exec_trap() and CHECK_INTERRUPT_IN_MATCH_AT.
2004/05/21: [impl] add regex status check to onig_match().
2004/05/21: [new]  add onig_get_capture_tree() and
                   onig_capture_tree_traverse().
2004/05/20: [spec] (thanks Isao Sonobe)
                   capture history return capture data tree.
                   (see sample/listcap.c)
2004/05/19: [bug]  (thanks Simon Strandgaard)
                   Control-C does not work in matching process on Ruby.
                   add calling of CHECK_INTERRUPT into match_at().
                   ex. /<(?:[^">]+|"[^"]*")+>/.match('<META http-equiv= \
                       "Content-Type content="text/html; charset=iso-8859-1">')
2004/05/19: [bug]  (thanks Simon Strandgaard)
                   define virtual codepoint values for invalid encoding
                   byte 0xfe and 0xff in UTF-8.
                   ex. /\w+/u.match("%a\xffb\xfec%") ==> "a"
2004/05/19: [spec] (thanks Simon Strandgaard)
                   too big backref number should be treated as a sequence of
                   an octal char and number digits.
                   ex. /b\3777\c/.match("b\3777\c")
2004/05/17: [spec] rename encoding names "UTF-16 BE" and "UTF-16 LE"
                   to "UTF-16BE" and "UTF-16LE".
2004/05/17: [impl] move ismbchar() and mbclen() from oniguruma.h to oniggnu.h.
2004/05/17: [impl] rename onigenc_single_byte_is_allowed_reverse_match() to
                   onigenc_always_true_is_allowed_reverse_match().

2004/05/14: Version 3.0.0

2004/05/14: [test] success in ruby 1.8.0 (2003-08-08) [i386-cygwin].
2004/05/14: [test] success in ruby 1.9.0 (2004-05-14) [i686-linux].
2004/05/14: [test] success in ruby 1.8.0 (2003-08-08) [i386-mswin32].
                   (* need to edit parse.y:
                       register int c; ---> int c; in yylex())
2004/05/14: [impl] add regext.c.
2004/05/14: [spec] KOI8 is not included in library archive by default setup.
2004/05/14: [impl] implementation changes are completed for all encoding files.
2004/05/12: [impl] add divide_ambig_string_node().
                   ambiguous string is divided and normalized before
                   optimization and compilation process.
2004/05/11: [dist] remove INSTALL-RUBY from distribution.
2004/04/28: [memo] (thanks Kazuo Saito)
                   Oniguruma 2.2.8 was merged to Ruby 1.9.0.
2004/04/26: [spec] change value DEFAULT_MATCH_STACK_LIMIT_SIZE = 0 : unlimited
2004/04/26: [new]  add onig_get_match_stack_limit_size() and
                   onig_set_match_stack_limit_size().
2004/04/26: [bug]  add error check to re.c.181.patch and re.c.168.patch.
2004/04/23: [impl] remove ctype_support_level from OnigEncodingType.
2004/04/22: [spec] allow the range from single byte char to multibyte char in
                   character class for implementation reason.
                   ex. /[a-\xbb\xcc]/ in EUC-JP encoding.
2004/04/21: [impl] remove max_enc_len_by_first_byte() from OnigEncodingType.
2004/04/20: [new]  add onig_copyright().
2004/04/20: [impl] add regversion.c.
2004/04/15: [new]  add onig_get_ambig_flag().
2004/04/14: [bug]  (thanks Isao Sonobe)
                   undefined bytecode error happens if ONIG_OPTION_FIND_LONGEST
                   is set.
                   should finish matching process if find-condition
                   is fail at OP_END in match_at().
2004/04/12: [impl] add ambig_flag to regex_t.
2004/04/09: [impl] move onig_set_meta_char() to regsyntax.c.
2004/04/09: [bug]  (thanks HIROSE Masaaki) fix onig_version().
2004/04/08: [impl] add regsyntax.c.
2004/04/07: [new]  support UTF-16 LE. (ONIG_ENCODING_UTF16_LE)
2004/04/05: [impl] add ONIGENC_CTYPE_NEWLINE.
2004/04/05: [memo] (thanks Kazuo Saito)
                   Oniguruma 2.2.6 was merged to Ruby 1.9.0.
2004/04/02: [memo] Version 2.2.6 was released.
2004/03/26: [new]  support UTF-16 BE. (ONIG_ENCODING_UTF16_BE)
2004/03/25: [spec] support non 8-bit encodings.
2004/03/16: [memo] 2.X branch for 8-bit encodings only.

2004/03/16: Version 2.2.5

2004/03/16: [test] success in ruby 1.8.0 (2003-08-08) [i386-mswin32].
2004/03/16: [test] success in ruby 1.9.0 (2004-02-24) [i686-linux].
2004/03/16: [impl] add property name to error message of
                   ONIGERR_INVALID_CHAR_PROPERTY_NAME.
2004/03/16: [spec] allow prefix 'Is' for \p{...} in ONIG_SYNTAX_PERL.
                   add syntax op. ONIG_SYN_OP2_CHAR_PROPERTY_PREFIX_IS.
2004/03/15: [dist] add sample/syntax.c.
2004/03/15: [spec] support NOT op. in char property. \p{^...}, \P{^...}.
                   add syntax op. ONIG_SYN_OP2_ESC_P_BRACE_CIRCUMFLEX_NOT.
2004/03/15: [spec] rename ONIG_SYN_OP2_ESC_P_CHAR_PROPERTY to
                   ONIG_SYN_OP2_ESC_P_BRACE_CHAR_PROPERTY.
2004/03/10: [impl] move ONIGERR_XXX from regenc.h to oniguruma.h,
                   rename ONIGERR_XXX to ONIGENCERR_XXX in regenc.h.
2004/03/08: [impl] (thanks eban)
                   replace defined(__CYGWIN__) to defined(__GNUC__).
2004/03/08: [bug]  (thanks eban) [ruby-dev:23172]
                   need to separate initialization for bcc32.
2004/03/06: [memo] (thanks Kazuo Saito)
                   Oniguruma 2.2.4 was merged to Ruby 1.9.0.
2004/03/05: [API]  change second argument type of onig_set_meta_char()
                   from unsigned int to OnigCodePoint.
2004/03/05: [dist] (thanks Kazuo Saito)
                   add MANIFEST-RUBY.

2004/03/04: Version 2.2.4

2004/03/04: [impl] (thanks Moriyoshi Koizumi)
                   fix many warnings in Win32 VC++ with /W3 option.

2004/03/02: Version 2.2.3

2004/03/02: [bug]  (thanks Isao Sonobe)
                   return invalid capture region value if capture history
                   is used. (OP_MEMORY_END_PUSH_REC bug)
                   ex. /\g<p>(?@<p>\(\g<s>\)){0}(?<s>(?:\g<p>)*|){0}/
                       .match("((())())")
2004/03/02: [impl] (thanks Kazuo Saito)
                   add :nodoc: to onig_stat_print() for RDoc.
2004/03/02: [impl] don't use ONIG_SOURCE_IS_WRAPPED.

2004/02/27: Version 2.2.2

2004/02/27: [impl] fix the position of onig_stat_print().
2004/02/27: [impl] define ONIG_RUBY_DEFINE_GLOBAL_FUNCTION() in regint.h
                   for ignored by RDoc.

2004/02/26: Version 2.2.1

2004/02/26: [bug]  [bugs.php.net:#26677] (thanks behrens)
                   invalid definition at onig_error_code_to_str()
                   in the case of NOT HAVE_STDARG_PROTOTYPES.

2004/02/25: Version 2.2.0

2004/02/25: [test] success in ruby 1.8.0 (2003-08-08) [i386-mswin32].
2004/02/24: [test] success in ruby 1.9.0 (2004-02-24) [i686-linux].
2004/02/24: [bug]  undefined IS_BLANK() and IS_GRAPH() was used in
                   onigenc_is_code_ctype() in the case of Ruby M17N.
2004/02/24: [new]  support ISO-8859-16. (ONIG_ENCODING_ISO_8859_16)
2004/02/24: [bug]  should not fold match for 0xdf in iso8859_6.c.
2004/02/24: [new]  support ISO-8859-14. (ONIG_ENCODING_ISO_8859_14)
2004/02/23: [new]  support ISO-8859-13. (ONIG_ENCODING_ISO_8859_13)
2004/02/23: [new]  support ISO-8859-10. (ONIG_ENCODING_ISO_8859_10)
2004/02/20: [bug]  fix iso_8859_4_mbc_is_case_ambig().
2004/02/20: [new]  support ISO-8859-9. (ONIG_ENCODING_ISO_8859_9)
2004/02/19: [bug]  correct ctype tables for ISO-8859-3, ISO-8859-4,
                   ISO-8859-6, ISO-8859-7, ISO-8859-8, KOI8_R.
2004/02/18: [bug]  wrong replaced name OnigSyntaxGnuOnigex.
2004/02/17: [spec] check capture status for empty infinite loop.
                   [ruby-dev:20224] etc...
                   ex. /(?:\1a|())*/.match("a"),
                       /(?:()|()|()|(x)|()|())*\2b\5/.match("b")
                   add USE_INFINITE_REPEAT_MONOMANIAC_MEM_STATUS_CHECK.
                   add OP_NULL_CHECK_END_MEMST, OP_NULL_CHECK_END_MEMST_PUSH.
                   add stack type STK_NULL_CHECK_END.
2004/02/13: [impl] add OnigEncodingEUC_CN to enc/euc_kr.c.
2004/02/13: [bug]  (thanks Simon Strandgaard)
                   parsing of nested repeat was invalid.
                   ex. /ab{2,3}*/ was /(?:a(?:b{2,3}))*/,
                       should be /a(?:b{2,3}*)/
2004/02/12: [bug]  (thanks Simon Strandgaard)
                   OP_REPEAT_INC_NG process in match_at() is wrong.
                   ex. bad match /a.{0,2}?a/ =~ "0aXXXa0"
2004/02/12: [bug]  (thanks Simon Strandgaard)
                   wrong fetch after (?x) option.  ex. "(?x)\ta .\n+b"
2004/02/12: [bug]  (thanks Simon Strandgaard)
                   [\^] is not a empty char class.
2004/02/09: [new]  add onig_set_syntax_op(), onig_set_syntax_op2(),
                   onig_set_syntax_behavior(), onig_set_syntax_options().
2004/02/06: [dist] add a new target 'site' to Makefile.in.
2004/02/06: [dist] add index.html.
2004/02/03: [bug]  oniggnu.h was not installed by 'make install'.

2004/02/02: Version 2.1.0

2004/02/02: [test] success in ruby 1.9.0 (2004-02-02) [i686-linux].
2004/02/02: [test] success in ruby 1.8.0 (2003-08-08) [i386-cygwin].
2004/02/02: [test] success in ruby 1.8.0 (2003-08-08) [i386-mswin32].
2004/02/02: [new]  support ISO-8859-11. (ONIG_ENCODING_ISO_8859_11)
2004/02/02: [new]  support ISO-8859-5. (ONIG_ENCODING_ISO_8859_5)
2004/02/02: [impl] should check single byte encoding or not in and_cclass()
                   and or_cclass().
2004/01/30: [dist] add oniggnu.h.
2004/01/30: [bug]  ISO-8859-7 0xb7 (middle dot) is Punct type.
2004/01/30: [new]  support ISO-8859-8. (ONIG_ENCODING_ISO_8859_8)
2004/01/29: [new]  support ISO-8859-7. (ONIG_ENCODING_ISO_8859_7)
2004/01/29: [new]  support ISO-8859-6. (ONIG_ENCODING_ISO_8859_6)
2004/01/28: [new]  support KOI8-R. (ONIG_ENCODING_KOI8_R)
2004/01/28: [new]  support KOI8. (ONIG_ENCODING_KOI8)
2004/01/27: [dist] rename enc/isotable.c to enc/mktable.c.
2004/01/27: [new]  support ISO-8859-4. (ONIG_ENCODING_ISO_8859_4)
2004/01/26: [new]  support ISO-8859-3. (ONIG_ENCODING_ISO_8859_3)
2004/01/26: [bug]  EncISO_8859_{1,15}_CtypeTable[256] was wrong.
                   (0x80 - 0xff is not ASCII)
2004/01/23: [new]  support ISO-8859-2. (ONIG_ENCODING_ISO_8859_2)
2004/01/23: [dist] add enc/isotable.c.
2004/01/22: [new]  support EUC-TW. (ONIG_ENCODING_EUC_TW)
2004/01/22: [bug]  definition of GET_ALIGNMENT_PAD_SIZE() and
                   ALIGNMENT_RIGHT() was wrong.
                   type casting should be unsigned int, not int.
2004/01/22: [impl] add defined(__x86_64) || defined(__x86_64__)
                   to unaligned word access condition. (AMD64 ?)
2004/01/21: [dist] rename enc/eucjp.c to enc/euc_jp.c.
2004/01/21: [new]  support EUC-KR. (ONIG_ENCODING_EUC_KR)
2004/01/20: [test] success in ruby 1.8.0 (2003-08-08) [i386-cygwin].
2004/01/20: [dist] change Makefile.in.
2004/01/20: [spec] add \p{...}, \P{...} in char class.
2004/01/20: [new]  character property operators \p{...}, \P{...}.
                   supported in ONIG_SYNTAX_JAVA and ONIG_SYNTAX_PERL.
2004/01/19: [spec] allow /a{,n}/ as /a{0,n}/. (but don't allow /a{,}/)
2004/01/19: [dist] rename onigcomp200.h to onigcmpt200.h.
2004/01/19: [dist] update re.c.168.patch. svn add re.c.181.patch.
2004/01/16: [dist] update sample/*.c for new API.
2004/01/16: [dist] add onigcomp200.h. (for old API compatibility)
2004/01/16: [dist] update documents API, RE and RE.ja.
2004/01/16: [spec] change prefix REG_ -> ONIG_, regex_ onig_,
                   ENC_ -> ONIGENC, enc_ -> onigenc_.
2004/01/15: [impl] rename ENC_IS_MBC_E_WORD() to ENC_IS_MBC_WORD().
                   rename ENC_CTYPE_SUPPORT_LEVEL_SB_ONLY to
                   ENC_CTYPE_SUPPORT_LEVEL_SB.
2004/01/14: [impl] rename UNALIGNED_WORD_ACCESS to
                   PLATFORM_UNALIGNED_WORD_ACCESS.
2004/01/14: [impl] change MATCH_STACK_LIMIT_SIZE value from 200000 to 500000.
2004/01/13: [impl] remove ENC_CODE_TO_MBC_FIRST(enc,code) in regenc.h.
                   remove code_to_mbc_first member in RegCharEncodingType.
2004/01/13: [impl] remove head byte bitset information in cclass->mbuf.
2003/12/26: [impl] change macro name ismb_xxxx() in enc/*.c for
                   escape conflict.

2003/12/24: Version 2.0.0

2003/12/24: [spec] ignore case option is effective to numbered char.
                   ex. /\x61/i =~ "A"
2003/12/24: [test] success in ruby 1.8.1 (2003-12-24) [i686-linux].
2003/12/24: [test] success in ruby 1.8.0 (2003-08-08) [i386-cygwin].
2003/12/24: [test] success in ruby 1.8.0 (2003-08-08) [i386-mswin32].
2003/12/24: [test] success in regex.c compile test on ruby-m17n.
                   (but can't make miniruby because re.c patch fail.)
2003/12/24: [bug]  (thanks H.Miyamoto) /[\W]/ was wrong in 1.9.5.
2003/12/22: [spec] implement fold match on UTF-8 encoding.
2003/12/19: [impl] add ctype_support_level and ctype_add_codes() member to
                   RegCharEncoding type.
2003/12/19: [impl] add add_ctype_to_cc() in regparse.c.
2003/12/19: [impl] add enc_is_code_ctype() in REG_RUBY_M17N case.
2003/12/19: [impl] change ENC_CODE_TO_MBC() interface.
2003/12/18: [new]  implement fold match. (variable number of char
                   match in ignore case mode.)
                   ex. German alphabet ess-tsett(U+00DF) match "SS" and "ss".
2003/12/17: [impl] refactoring of encoding system.
2003/12/17: [impl] add enc_init() in regenc.c.
2003/12/17: [new]  support Big5. (REG_ENCODING_BIG5)
2003/12/16: [impl] change CodePoint from unsigned int to unsigned long.
2003/12/16: [new]  support ISO 8859-15. (REG_ENCODING_ISO_8859_15)
2003/12/16: [impl] change P_() macro definition condition for Win32.
2003/12/16: [dist] add sample/encode.c
2003/12/16: [new]  support ISO 8859-1. (REG_ENCODING_ISO_8859_1)
2003/12/15: [impl] rename IS_ENC_XXXX to ENC_IS_XXXX.
2003/12/15: [impl] rename RegDefaultCharEncoding to EncDefaultCharEncoding.
2003/12/15: [impl] divide encoding files. (enc/ascii.c, enc/utf8.c etc...)
2003/12/15: [bug]  unexpected infinite loop in regex_snprintf_with_pattern().
                   change local var. type char* to UChar*.
2003/12/15: [impl] remove REG_MBLEN_TABLE[].
2003/12/15: [spec] rename function prefix regex_get_prev_char_head(),
                   regex_get_left_adjust_char_head() and
                   regex_get_right_adjust_char_head() to enc_xxxxxx().
2003/12/15: [impl] rename function prefixes in regenc.h from regex_ to enc_.
2003/12/12: [impl] remove USE_SBMB_CLASS.
2003/12/12: [impl] rename mb -> mbc, mblen() to enc_len().
2003/12/12: [impl] rename WCINT to CodePoint.
2003/12/11: [impl] delete IS_XXXX() ctype macros from regint.h.
2003/12/11: [impl] add enc->wc_is_ctype() and RegAsciiCtypeTable[256].
2003/12/11: [impl] remove RegAsciiCaseAmbigTable.
2003/12/10: [impl] use ENC_TO_LOWER() for ignore case comparison.
2003/12/08: [impl] *** re-defined RegCharEncoding in oniguruma.h. ***
2003/12/08: [impl] add USE_POSIX_REGION_OPTION to regint.h.
2003/12/08: [impl] add IS_ENC_WORD() to regenc.h.
2003/12/05: [impl] rename IS_CODE_XXXX() to IS_ENC_XXXX().
2003/12/05: [impl] delete IS_CODE_WORD() from regenc.h.
2003/12/04: [spec] rename REG_SYN_OP_BACK_REF to REG_SYN_OP_DECIMAL_BACKREF.
2003/12/04: [spec] add (REG_SYN_OP_ESC_W_WORD | REG_SYN_OP_ESC_B_WORD_BOUND |
                   REG_SYN_OP_ESC_LTGT_WORD_BEGIN_END | REG_SYN_OP_BACK_REF)
                   to RegSyntaxGrep.
2003/12/04: [spec] remove REG_ENCODING_DEFAULT and REGCODE_DEFAULT.
2003/12/04: [spec] move declarations of regex_get_default_encoding() and
                   regex_set_default_encoding() from oniguruma.h to regenc.h.
2003/12/03: [new]  add regex_get_default_encoding() and
                   regex_set_default_encoding().
2003/12/03: [spec] REG_ENCODING_DEFAULT meaning is changed.
                   (current default value, not initial default value.)
2003/12/03: [spec] REGCODE_XXX is obsoleted. use REG_ENCODING_XXX.
2003/12/02: [memo] alias svnst='svn status | grep -v "^\?"'
2003/12/02: [spec] move regex_set_default_trans_table() declaration
                   from oniguruma.h to regenc.h. (obsoleted API)
2003/12/02: [impl] move variables RegDefaultCharEncoding, DefaultTransTable and
                   AmbiguityTable to regenc.c.
2003/12/01: [impl] add regex_continuous_sbmb() to regenc.c.
2003/12/01: [dist] add regenc.h and regenc.c.
2003/11/18: [dist] change testconv.rb.
2003/11/18: [bug]  (thanks Masaru Tsuda)
                   memory leak in parse_subexp().
2003/11/18: [bug]  (thanks Masaru Tsuda)
                   memory leak in names_clear() and parse_char_class().
2003/11/17: [bug]  memory leak in parse_char_class().
2003/11/17: [bug]  (thanks Masaru Tsuda)
                   OptExactInfo length should not over OPT_EXACT_MAXLEN.
                   (concat_opt_exact_info_str())

2003/11/12: Version 1.9.5

2003/11/12: [test] success in ruby 1.8.0 (2003-08-08) [i386-cygwin].
2003/11/12: [test] success in ruby 1.8.1 (2003-11-11) [i686-linux].
2003/11/12: [spec] add definition of REG_INEFFECTIVE_META_CHAR.
2003/11/11: [dist] add a sample program sample/sql.c.
2003/11/11: [new]  add variable meta character.
                   regex_set_meta_char()
2003/11/11: [spec] add syntax op. REG_SYN_OP_VARIABLE_META_CHARS.
2003/11/11: [spec] rename REG_SYN_OP_ESC_CAPITAL_Q_QUOTE to
                   REG_SYN_OP2_ESC_CAPITAL_Q_QUOTE,
                   REG_SYN_OP_QMARK_GROUP_EFFECT to
                   REG_SYN_OP2_QMARK_GROUP_EFFECT.
2003/11/06: [impl] define THREAD_PASS as rb_thread_schedule() in Ruby mode.
2003/11/05: [spec] add syntax behavior REG_SYN_WARN_REDUNDANT_NESTED_REPEAT.
2003/11/05: [spec] rename REG_SYN_WARN_FOR_CC_OP_NOT_ESCAPED to
                   REG_SYN_WARN_CC_OP_NOT_ESCAPED.
2003/11/04: [new]  add regex_set_warn_func() and regex_set_verb_warn_func().
2003/10/30: [new]  add regex_name_to_backref_number().
                   (for multiplex definition name, see sample/names.c)
2003/10/30: [spec] add name_end and reg argument to callback function of
                   regex_foreach_name().  (see sample/names.c)
2003/10/29: [spec] add syntax behavior REG_SYN_ALLOW_MULTIPLEX_DEFINITION_NAME.
                   add error code REGERR_MULTIPLEX_DEFINED_NAME.
2003/10/14: [dist] modify sample/simple.c.
2003/10/03: [bug]  (thanks nobu)  [ruby-dev:21472]
                   sub-anchor of optimization map info was wrong
                   in concat_left_node_opt_info().
                   ex. /^(x?y)/ = "xy" fail.

2003/09/17: Version 1.9.4

2003/09/17: [spec] change specification of char-class range in ignore case mode
                   follows with Ruby 1.8(2003-09-17).
                   ex. /[H-c]/i ==> (H-Z, 0x5b-0x60, a-c)/i
                                ==> H-Z, h-z, 0x5b-0x60, a-c, A-C
2003/09/16: [bug]  (thanks Guy Decoux)
                   remove env->option == option check in parse_effect().
                   change env->option for dynamic option in parse_exp().
                   (ex. bad match /(?i)(?-i)a/ =~ "A")
2003/09/12: [spec] rename REG_SYN_ALLOW_RANGE_OP_IN_CC to
                   REG_SYN_ALLOW_DOUBLE_RANGE_OP_IN_CC,
                   REG_SYN_ESCAPE_IN_CC to REG_SYN_BACKSLASH_ESCAPE_IN_CC.
2003/09/11: [bug]  change to IS_SYNTAX_OP2 at REG_SYN_OP2_ESC_GNU_BUF_ANCHOR.
2003/09/09: [spec] rename REG_SYN_OP2_ESC_M_BAR_META to
                   REG_SYN_OP2_ESC_CAPITAL_M_BAR_META,
                   REG_SYN_OP_ESC_Q_QUOTE to REG_SYN_OP_ESC_CAPITAL_Q_QUOTE,
                   REG_SYN_OP_ESC_SUBEXP to REG_SYN_OP_ESC_LPAREN_SUBEXP,
                   REG_SYN_OP_ESC_BUF_ANCHOR to REG_SYN_OP_ESC_AZ_BUF_ANCHOR,
                   REG_SYN_OP_ESC_GNU_BUF_ANCHOR to
                   REG_SYN_OP2_ESC_GNU_BUF_ANCHOR,
                   REG_SYN_OP_ESC_CONTROL_CHAR to REG_SYN_OP_ESC_CONTROL_CHARS,
                   REG_SYN_OP_ESC_WORD to REG_SYN_OP_ESC_W_WORD,
                   REG_SYN_OP_ESC_WORD_BEGIN_END to
                   REG_SYN_OP_ESC_LTGT_WORD_BEGIN_END,
                   REG_SYN_OP_ESC_WORD_BOUND to REG_SYN_OP_ESC_B_WORD_BOUND,
                   REG_SYN_OP_ESC_WHITE_SPACE to REG_SYN_OP_ESC_S_WHITE_SPACE,
                   REG_SYN_OP_ESC_DIGIT to REG_SYN_OP_ESC_D_DIGIT,
                   REG_SYN_OP_CC to REG_SYN_OP_BRACKET_CC,
                   REG_SYN_OP2_CCLASS_SET to REG_SYN_OP2_CCLASS_SET_OP,
                   REG_SYN_CONTEXT_INDEP_OPS to
                   REG_SYN_CONTEXT_INDEP_REPEAT_OPS,
                   REG_SYN_CONTEXT_INVALID_REPEAT_OPS to
                   REG_SYN_CONTEXT_INVALID_REPEAT_OPS.
                   add REG_SYN_OP_ESC_CAPITAL_G_BEGIN_ANCHOR.
2003/09/08: [spec] rename REG_SYN_OP_ANYCHAR to REG_SYN_OP_DOT_ANYCHAR,
                   REG_SYN_OP_0INF to REG_SYN_OP_ASTERISK_ZERO_INF,
                   REG_SYN_OP_ESC_0INF to REG_SYN_OP_ESC_ASTERISK_ZERO_INF,
                   REG_SYN_OP_1INF to REG_SYN_OP_PLUS_ONE_INF,
                   REG_SYN_OP_ESC_1INF to REG_SYN_OP_ESC_PLUS_ONE_INF,
                   REG_SYN_OP_0INF to REG_SYN_OP_QMARK_ZERO_ONE,
                   REG_SYN_OP_ESC_0INF to REG_SYN_OP_ESC_QMARK_ZERO_ONE,
                   REG_SYN_OP_INTERVAL to REG_SYN_OP_BRACE_INTERVAL,
                   REG_SYN_OP_ESC_INTERVAL to REG_SYN_OP_ESC_BRACE_INTERVAL,
                   REG_SYN_OP_SUBEXP to REG_SYN_OP_LPAREN_SUBEXP,
                   REG_SYN_OP_ALT to REG_SYN_OP_VBAR_ALT,
                   REG_SYN_OP_ESC_ALT to REG_SYN_OP_ESC_VBAR_ALT,
                   REG_SYN_OP_NON_GREEDY to REG_SYN_OP_QMARK_NON_GREEDY,
                   REG_SYN_OP_SUBEXP_EFFECT to REG_SYN_OP_QMARK_GROUP_EFFECT,
                   REG_SYN_OP2_POSSESSIVE_{REPEAT,INTERVAL} to
                   REG_SYN_OP2_PLUS_POSSESSIVE_{REPEAT,INTERVAL},
                   REG_SYN_OP2_SUBEXP_CALL to REG_SYN_OP2_ESC_G_SUBEXP_CALL,
                   REG_SYN_OP2_NAMED_GROUP to REG_SYN_OP2_QMARK_LT_NAMED_GROUP
                   and REG_SYN_OP2_ESC_K_NAMED_BACKREF.
2003/09/02: [tune] call reduce_nested_qualifier() after disabling capture for
                   no-name group in noname_disable_map().
                   ex. /(a+)*(?<name>...)/
2003/09/02: [impl] include <stdio.h> is forgotten to erase in regcomp.c.
2003/09/01: [dist] update doc/RE and doc/RE.ja.
2003/08/26: [bug]  (thanks Guy Decoux)
                   should not double free node at the case TK_CC_CC_OPEN
                   in parse_char_class().

2003/08/19: Version 1.9.3

2003/08/19: [inst] change re.c.180.patch.
2003/08/19: [impl] rename 'list of captures' to 'capture history'.
2003/08/19: [dist] add doc/RE.ja. (Japanese)
2003/08/19: [new]  add regex_copy_syntax().
2003/08/19: [spec] rename REG_SYN_OP2_ATMARK_LIST_OF_CAPTURES to
                   REG_SYN_OP2_ATMARK_CAPTURE_HISTORY.
2003/08/18: [spec] (thanks nobu)
                   don't use IMPORT in oniguruma.h and onigposix.h.
2003/08/18: [impl] (thanks nobu) change error output to stdout in testconv.rb.
2003/08/18: [inst] (thanks nobu) lacked $(srcdir) in Makefile.in.
2003/08/18: [bug]  REG_MBLEN_TABLE[SJIS][0xFD-0xFF] should be 1.
2003/08/18: [bug]  (thanks nobu) mbctab_sjis[0x80] should be 0.
2003/08/18: [bug]  (thanks nobu)
                   single/multi-byte decision was wrong in parse_char_class().
                   add regex_wc2mblen().
                   should not set fetched to 1 in TK_RAW_BYTE case.
2003/08/18: [bug]  should update BitSet in the case inc_n >= 0
                   in add_wc_range_to_buf().
2003/08/13: [bug]  change re.c.180.patch for fix rb_reg_to_s() in re.c.
2003/08/11: [bug]  should clear region->list in regex_region_resize().

2003/08/08: Version 1.9.2

2003/08/08: [test] success in ruby 1.8.0 (2003-08-08) on Windows 2000
                   VC++ 6.0 and Cygwin.
2003/08/08: [impl] don't define macro vsnprintf for WIN32 platform,
                   because definition is added in win32\win32.h.
2003/08/08: [test] success in ruby 1.8.0 and ruby 1.6.8(2003-08-03) on Linux.
2003/08/08: [dist] change re.c.180.patch and re.c.168.patch.
2003/08/08: [new]  (thanks akr)
                   implemented list of captures. (?@...), (?@<name>...)
2003/08/07: [dist] add sample/listcap.c.
2003/08/06: [bug]  OP_MEMORY_END_PUSH_REC case in match_at().
                   renewal of mem_start_stk[] should be after
                   STACK_PUSH_MEM_END() call.
2003/07/29: [new]  add regex_get_encoding(), regex_get_options() and
                   regex_get_syntax().
2003/07/25: [spec] (thanks akr)
                   change group(...) to shy-group(?:...) if named group is
                   used in the pattern.
                   add REG_SYN_CAPTURE_ONLY_NAMED_GROUP.
2003/07/24: [spec] rename REG_OPTION_CAPTURE_ONLY_NAMED_GROUP to
                   REG_OPTION_DONT_CAPTURE_GROUP.
                   add REG_OPTION_CAPTURE_GROUP.
2003/07/17: [spec] rename REG_SYN_OP2_NAMED_SUBEXP to REG_SYN_OP2_NAMED_GROUP.
2003/07/17: [spec] add REGERR_EMPTY_GROUP_NAME.
2003/07/17: [spec] rename REGERR_INVALID_SUBEXP_NAME
                       to REGERR_INVALID_CHAR_IN_GROUP_NAME.
2003/07/17: [spec] restrict usable chars of group name to alphabet, digit,
                   '_' or multibyte-char in fetch_name(). [ruby-dev:20706]
2003/07/16: [impl] minor change of sample/names.c.
2003/07/14: [impl] rename USE_NAMED_SUBEXP to USE_NAMED_GROUP.
2003/07/14: [bug]  add fetch_name() for USE_NAMED_SUBEXP off case.
2003/07/14: [API]  add regex_number_of_names().
2003/07/08: [impl] change error message for undefined group number call.
                       'undefined group reference: /(a)\g<2>/'
                   --> 'undefined group <2> reference: /(a)\g<2>/'
2003/07/08: [dist] modify doc/RE.
2003/07/07: [impl] OP_SET_OPTION is not needed in compiled code.
                   add IS_DYNAMIC_OPTION() to regint.h.
2003/07/07: [spec] called group should not ignore outside option (?i:...).
                   ex. /(?i:(?<n>(a)\2)){0}\g<n>/.match("aA")
                   add opcode OP_BACKREFN_IC and OP_BACKREF_MULTI_IC.
                   set option status to effect memory in optimize_node_left().
2003/07/07: [impl] add opcode OP_ANYCHAR_ML, OP_ANYCHAR_ML_STAR and
                   OP_ANYCHAR_ML_START_PEEK_NEXT.
2003/07/07: [bug]  (thanks nobu) REG_MBLEN_TABLE[SJIS][0x80] should be 1.
2003/07/07: [spec] rename REG_SYN_OP_QUOTE to REG_SYN_OP_ESC_Q_QUOTE.

2003/07/04: Version 1.9.1

2003/07/04: [new]  add REG_OPTION_CAPTURE_ONLY_NAMED_GROUP. (thanks .NET)
2003/07/04: [spec] check mbuf member in the case of
                   REG_SYN_NOT_NEWLINE_IN_NEGATIVE_CC in parse_char_class().
2003/07/04: [spec] typo REG_SYN_WARN_FOR_CC_OP_NOT_ESCAPEED.
                   should be REG_SYN_WARN_FOR_CC_OP_NOT_ESCAPED.
2003/07/04: [bug]  conflict values on REG_SYN_WARN_FOR_CC_OP_NOT_ESCAPEED and
                   REG_SYN_NOT_NEWLINE_IN_NEGATIVE_CC.  (thanks nobu)
2003/07/03: [spec] add REG_SYN_OP_ESC_CONTROL_CHAR flag.
2003/07/03: [spec] remove REG_SYN_OP_ESC_OCTAL3 and REG_SYN_OP_ESC_X_HEX2
                   flag from RegSyntaxGnuRegex.
2003/07/03: [spec] remove REG_SYN_OP_NON_GREEDY flag from RegSyntaxGnuRegex.
2003/07/02: [dist] fix doc/RE.
2003/07/01: [impl] add config flag USE_VARIABLE_SYNTAX.
                   (turn off variable syntax on Ruby)
2003/07/01: [spec] add syntax behavior REG_SYN_DIFFERENT_LEN_ALT_LOOK_BEHIND.
2003/06/30: [spec] allow different length top-level alternatives
                   in look-behind.    ex. (?<=abc|abcd), (?<!a|bc)
2003/06/26: [spec] add option REG_OPTION_NEGATE_SINGLELINE.
2003/06/26: [spec] should default on REG_OPTION_SINGLELINE
                   for REG_SYNTAX_PERL and REG_SYNTAX_JAVA.
2003/06/26: [impl] add options member to RegStntaxType.
2003/06/26: [spec] don't change the meaning of '\Z' for REG_OPTION_SINGLELINE.
2003/06/25: [dist] don't use option REG_NEWLINE for sample/posix.c.
2003/06/25: [dist] modify testconv.rb.
                   should match and convert double quoted string data.
                   ex. x(/\ca/, "\001", 0, 1)
2003/06/25: [impl] add REG_SYN_OP2_ESC_CAPITAL_C_BAR_CONTROL and
                   REG_SYN_OP2_ESC_M_BAR_META.
2003/06/25: [impl] add REG_SYN_OP_ESC_OCTAL3 and REG_SYN_OP_ESC_X_HEX2.
2003/06/24: [impl] add REG_SYN_OP2_ESC_V_VTAB. (\v is VTAB)
2003/06/24: [bug]  should invert REG_OPTION_SINGLELINE flag
                   in REG_SYN_OP2_OPTION_PERL.
2003/06/24: [impl] add REG_SYN_OP2_OPTION_PERL and REG_SYN_OP2_OPTION_RUBY.
                   meaning of (?m) and (?s) are depend on syntax.

2003/06/20: Version 1.9.0

2003/06/20: [spec] \Q...\E is not effective on REG_SYNTAX_RUBY. (thanks akr)
2003/06/19: [inst] rename regex.h to oniguruma.h.
2003/06/18: [impl] change REG_EXTERN setting condition. (__CYGWIN__)
2003/06/18: [bug]  return wrong result UTF-8 case in regex_mb2wc().
2003/06/18: [impl] add REG_SYN_OP2_POSSESSIVE_INTERVAL.  a{n,m}+
2003/06/18: [new]  add REG_SYNTAX_JAVA.
2003/06/18: [spec] add REG_SYN_OP_QUOTE.
2003/06/18: [spec] add op2 member to RegSyntaxType.
                   rename some REG_SYN_OP_XXX to REG_SYN_OP2.
2003/06/16: [new]  Perl-like quotation operator \Q, \E.
2003/06/16: [spec] should not control ignore case mode by escaped char.
                   ex. /\J/i =~ "j", /[\J]/i =~ "j"    (same as Perl)
2003/06/13: [bug]  modify onigposix.h.
2003/06/13: [bug]  should use -DIMPORT for link with DLL in win32/Makefile.
2003/06/13: [dist] add sample/names.c
2003/06/12: [bug]  range should be from - 1 in not_wc_range_buf().
2003/06/12: [spec] should warn for '-' before '&&' operator in char-class.
2003/06/12: [new]  add REG_SYNTAX_PERL.
2003/06/12: [spec] add syntax behavior REG_SYN_WARN_FOR_CC_OP_NOT_ESCAPEED.
2003/06/12: [spec] invalid POSIX bracket should be error.   ex. [[:upper :]]
2003/06/11: [new]  char-class in char-class (as Java(TM)).
2003/06/11: [spec] change AND operator in char-class from &&[..] to &&.
2003/06/04: [spec] {n,m}+ should not be possessive operator.
                   ex. a{3}+ should be (?:a{3})+
2003/06/03: [bug]  should compare strings with min-length in is_not_included().
2003/06/03: [impl] automatic possessivate optimization.  a*b ==> (?>a*)b
                   (thanks Jeffrey E. F. Friedl)
2003/06/02: [impl] remove multibyte-BitSet for OP_CCLASS_MB/OP_CCLASS_MB_NOT.
2003/05/30: [new]  char class intersection operator &&[...] like Java(TM).
                   (thanks akr)
2003/05/30: [bug]  should use bbuf_free() for CClassNode in regex_node_free().
2003/05/29: [bug]  wrong usage of syntax REG_SYN_ALLOW_EMPTY_RANGE_IN_CC.
                   /[d-a]/ should be error.
2003/05/28: [impl] optimize stop-backtrack compiled code.
                   (/(?>a*)/, /(?>\w+)/ etc...)
                   add OP_POP opcode.
2003/05/28: [new]  possessive repeat operator. (?+, *+, ++, {n,m}+)
2003/05/27: [spec] '-' at beginning of char-class should be warn only if
                   it is start of range.  (ex. /[--a]/)
2003/05/27: [spec] should not warn for right bracket at beginning of pattern.
                   ex. /]aaa/
2003/05/27: [spec] change CCEND_ESC_WARN() from VERB_WARNING() to WARNING().
2003/05/27: [spec] /[]aaa/ should be empty char-class error.
                   /[]aaa]/ should be warn for 'without backslash'.
                   (add char_exist_check() in regparse.c)
2003/05/26: [bug]  OP_REPEAT in recursive subexp call.
                   ex. /(?<n>(a|b\g<n>c){3,5})/.match("baaaaca") => "baaaaca"
                       was wrong result. (should be "aaaa")
2003/05/26: [impl] add num_call member to regex_t.
2003/05/26: [impl] add repeat_range member to regex_t.
                   (for delete upper,lower members from StackType.u.repeat)
2003/05/26: [bug]  change print_names() to external regex_print_names().
2003/05/26: [tune] change OP_NULL_CHECK_END process in match_at().
2003/05/26: [spec] change CCEND_ESC_WARN() from WARNING() to VERB_WARNING().
2003/05/26: [spec] remove POSIXLINE option. (?p:...)
                   (be made the same as Ruby.)
2003/05/22: [spec] use OP_NULL_CHECK_XXX only if repeat is infinite.
                   prev. /(?:()|()){0,10}\1\2/ =~ ""  ==> FAIL
                   now   /(?:()|()){0,10}\1\2/ =~ ""  ==> MATCH

2003/05/22: [impl] change target_empty setting condition in setup_tree().
2003/05/19: [impl] avoid zero length repeat optimization.  (thanks matz)
                   /()*/ ==> /()?/, /()+/ ==> /()/ etc...
2003/05/19: [impl] minor changes for gcc -Wall. (-DREG_DEBUG_STATISTICS case)
2003/05/19: [spec] rename regex_foreach_names() to regex_foreach_name().
2003/05/16: [new]  add --with-statistics option to configure.
2003/05/16: [bug]  move RegOpInfo[] definition to regint.h.
2003/05/16: [new]  add regex_version().

2003/05/14: Version 1.8.6

2003/05/14: [bug]  use _vsnprintf() on Win32.
2003/05/14: [spec] define USE_NEWLINE_AT_END_OF_STRING_HAS_EMPTY_LINE.
                   (/\n$/ =~ "\n", /\n\Z/ =~ "\n") [ruby-dev:20125]
2003/05/14: [impl] minor changes for gcc -Wall.
2003/05/14: [impl] add string.h check in AC_CHECK_HEADERS().
2003/05/13: [impl] minor changes for gcc -Wall.
2003/05/13: [impl] add regex_snprintf_with_pattern().
2003/05/13: [spec] add warning for char class meta character without escape
                   in Ruby mode ('[', '-', ']').
2003/05/13: [impl] define WARNING() and VERB_WARNING() in regint.h.
2003/05/13: [bug]  correct is_code_ascii() for /[[:ascii:]]/.
2003/05/12: [dist] add regular expression document (doc/RE).
2003/05/12: [spec] specification of $(END_LINE) was made the same as Ruby 1.8.
                   [ruby-dev:20130]     (thanks matz)
2003/05/12: [memo] shifted to Subversion(version 0.21.0) from CVS.

2003/03/19: Version 1.8.5

2003/03/19: [impl] change REG_EXTERN definition.   (thanks nobu)
2003/03/19: [impl] abbreviation for long error_par in regex_error_code_to_str().
2003/03/18: [dist] change re.c.XXX.patch for GNU regex API changes.
2003/03/18: [spec] change API regex_new(), regex_recompile() and
                   regex_error_code_to_str().
                   change API re_compile_pattern() and re_recompile_pattern().
2003/03/18: [spec] replace REGERR_END_PATTERN_AT_GROUP_{COMMENT|OPTION} to
                   REGERR_END_PATTERN_IN_GROUP.
2003/03/17: [impl] should free err_arg.
2003/03/17: [bug]  mistake(high -> to) in add_wc_range_to_buf().
2003/03/17: [spec] add err_arg argument to regex_new() and regex_recompile().
                   for detail error message.  (thanks akr)

2003/03/12: Version 1.8.4

2003/03/12: [tune] use cached value of effect node in get_min_match_length().
2003/03/12: [bug]  escaped alphabet should be TK_RAW_BYTE
                   in fetch_token() and fetch_token_in_cc().
2003/03/12: [spec] change named backref and subexp call format.
                   backref: \k<name>, call: \g<name>     (thanks akr)
2003/03/11: [inst] add regparse.[ch] in win32/Makefile.
2003/03/11: [bug]  if UNALIGNED_WORD_ACCESS isn't set,
                   then compile error in unset_addr_list_fix().  (thanks knu)
2003/03/10: [impl] divide regcomp.c to regcomp.c, regparse.c and regparse.h.
2003/03/10: [bug]  should handle multi-byte code name in fetch_name().
2003/03/10: [spec] remove REGERR_TABLE_FOR_IGNORE_CASE_IS_NOT_SETTED.
2003/03/10: [spec] support POSIX API option REG_NOSUB.
                   add comp_options member to POSIX API regex_t.

2003/03/10: Version 1.8.3

2003/03/10: [bug]  can not compile with Ruby 1.6.8.
                   (inconsistent st.h with 1.6 and 1.8)
                   use hash table on Ruby 1.8 only.
2003/03/10: [spec] forbid to use '\' in group name.
2003/03/08: [impl] remove check_backref_number().
2003/03/08: [bug]  called group in 0-repeat should not be eliminated from
                   compile code.  ex. /(?*n)(?<n>){0}/   (thanks akr)
                   add is_referred member to QualifierNode.
2003/03/07: [impl] use hash table(st.[ch]) for implementation of name table.
                   (enable on Ruby in default)
2003/03/07: [new]  add regex_foreach_names().
2003/03/06: [impl] add member reg->stack_pop_level.
2003/03/06: [impl] add operator OP_MEMORY_START and member reg->backtrack_mem.
2003/03/06: [bug]  if REG_OPTION_FIND_LONGEST or REG_OPTION_NOT_EMPTY,
                   should handle backtrack of MEM_END.
                   add OP_MEMORY_END_PUSH and OP_MEMORY_END_PUSH_REC.
2003/03/06: [impl] rename OP_MEMORY_END_PUSH to OP_MEMORY_END_MARK.
2003/03/06: [spec] change error messages.
2003/03/06: [tune] add tiny_pop check in STACK_POP.

2003/03/05: Version 1.8.2

2003/03/05: [impl] use cache info in EFFECT_MEMORY case
                   in optimize_node_info().
2003/03/05: [impl] add EFFECT_MEMORY node reference count check
                   in optimize_node_left().
2003/03/05: [impl] add min-len, max-len, char-len cache in EffectNode.
2003/03/05: [spec] allow to call in look behind. ex. /(?<=(?*a))/
2003/03/05: [bug]  forgotten N_ANCHOR case in check_backref_number(),
                   subexp_inf_recursive_check_trav() etc...
2003/03/05: [impl] rename USE_ONIGURUMA_EXTENSION to USE_SBMB_CLASS.
2003/03/04: [impl] add CALL-node info in optimize_node_left().
2003/03/04: [spec] prohibit left recursion of subexp call.   ex. (?<n>|(?*n)a)
                   add subexp_inf_recursive_check_trav().
2003/03/04: [spec] rename REG_SYN_STRICT_CHECK_BACKREF_NUMBER
                   to REG_SYN_STRICT_CHECK_BACKREF
2003/03/03: [bug]  /(?<n>a(?*n)|)/ isn't infinite recursion.
                   fix N_LIST case in subexp_recursive_check(). (thanks akr)
2003/03/03: [bug]  /(?<n>|(?*n))+/ segmentation fault.
                   should re-allocate in unset_addr_list_add(). (thanks akr)

2003/03/01: Version 1.8.1

2003/03/01: [bug]  change STACK_GET_MEM_START() and STACK_PUSH_MEM_END().
2003/03/01: [new]  add reg_name_to_group_numbers() to POSIX API.
2003/03/01: [impl] use OP_MEMORY_END_PUSH in callable subexp compiled code
                   only if subexp is recursive.
2003/03/01: [spec] rename regex_name_to_backrefs() to
                   regex_name_to_group_numbers().
2003/02/28: [impl] use function stack_double() instead of macro.
2003/02/28: [new]  subexp call. (?*name)    (thanks akr)
2003/02/28: [spec] add match stack limit check. (MATCH_STACK_LIMIT_SIZE)
2003/02/28: [impl] check recursive subexp call.
2003/02/28: [impl] add opcode OP_MEMORY_END_PUSH for callable subexp.
2003/02/28: [impl] add opcode OP_CALL, OP_RETURN.
                   add stack type STK_CALL_FRAME, STK_RETURN, STK_MEM_END.
2003/02/26: [spec] add new syntax behavior REG_SYN_STRICT_CHECK_BACKREF_NUMBER.
                   if it is set, then error /(\1)/, /\1(..)/ etc...
2003/02/26: [spec] if backref number is greater than max group number,
                   then return compile error. (REGERR_INVALID_BACKREF_NUMBER)
2003/02/26: [tune] badly implemented N_ALT case in get_min_match_length().
2003/02/26: [dist] auto update testc.c and win32/testc.c in dist target.
2003/02/26: [impl] add -win option to testconv.rb.
2003/02/25: [spec] allow to assign same name to different group.
                   add OP_BACKREF_MULTI.
2003/02/24: [impl] reduce redundant repeat of empty target.
	           ex. /()*/ ==> /()?/, /()+/ ==> /()/, /(?:)+/ ==> //
2003/02/24: [impl] change condition in regex_is_allow_reverse_match().
2003/02/24: [impl] convert i(/../, ...) functions in testconv.rb.
2003/02/24: [impl] change name table struct.

2003/02/22: Version 1.8.0

2003/02/22: [new]  named subexp, named back reference.  (thanks akr)
                   define: (?<name>...), back-ref: \g<name>
2003/02/22: [impl] use str_node_can_be_split().
2003/02/21: [dist] add sample/posix.c
2003/02/21: [spec] rename some error code symbols.
2003/02/21: [spec] max number of multibyte ranges(255) is small.
                   255 --> 1000.   (thanks MoonWolf)
2003/02/20: [new]  supported Basic Regular Expression(BRE) in POSIX API.
                   (REG_EXTENDED option: Extended RE)
2003/02/20: [new]  variable syntax.

2003/02/12: Version 1.7.2

2003/02/12: [bug]  mismatch /\?a/i.match('?A').
                   check raw value in scan_make_node() and scan_backslash().
                   (thanks Nobu)
2003/02/12: [impl] rename 'max_mem' to 'num_mem' in regex_t.
2003/02/12: [impl] rename 'code' to 'enc' in regex_t.
2003/02/12: [spec] remove transtable argument in regex_new and regex_recompile.
                   remove transtable member in regex_t.
2003/02/10: [inst] change backup file suffix name from '.orig' to '.ruby_orig'.
                   (win32/Makefile)
2003/02/10: [spec] number check in scan_char_class() ignore-case mode.
                   ex. /[\x58-\x64]/i
2003/02/10: [impl] don't use OP_MEMORY_END_PUSH (and STK_MEM_END).
2003/02/10: [impl] lift up head_exact value from child qualifier node to parent.
2003/02/10: [tune] change stack type values.
2003/02/10: [dist] add HISTORY.
2003/02/08: [tune] change stack type values.
2003/02/08: [tune] add STACK_BASE_CHECK().
2003/02/08: [tune] add STACK_PUSH_ENSURED().
2003/02/08: [dist] change contents of doc/API.
2003/02/07: [inst] change backup file suffix name from '.orig' to '.ruby_orig'.
2003/02/07: [spec] range in char-class should be same spec. with Ruby
                   in ignore-case mode. (ex. /[A-c]/i == /[a-c]/i)
                   (thanks MoonWolf)
2003/02/07: [spec] [!--] should be allowed.    (thanks MoonWolf)
2003/02/07: [dist] refresh re.c.180.patch for re.c (2003-02-06).

2003/02/07: Version 1.7.1

2003/02/07: [impl] check first byte of string in ignore-case mode.
                   (get_head_exact_node())
2003/02/07: [impl] remove redundant statements in setup_tree().
2003/02/06: [new]  create Win32 DLL.
2003/02/06: [impl] use P_() macro for function prototype.
2003/02/06: [impl] add HAVE_PROTOTYPE, HAVE_STDARG_PROTOTYPES in
                   configure.in and config.h.in.
2003/02/06: [spec] /[0-9-a]/ is allowed as usual char '-' and 'a' in Ruby.
                   add USE_BETTER_COMPATIBILITY_FOR_ORIGINAL_REGEX in
                   regint.h.   (thanks MoonWolf)
2003/02/06: [spec] rename REG_MBCTYPE_XXXX to REG_ENCODING_XXXX in onigposix.h.
2003/02/05: [spec] rename MBCTYPE_XXXX to REG_MBCTYPE_XXXX in onigposix.h.
2003/02/05: [spec] add POSIX API error REG_EONIG_THREAD to onigposix.h.
2003/02/05: [dist] add .cvsignore file.

2003/02/04: Version 1.7

2003/02/04: [bug]  typo miss in regex_region_copy().
2003/02/04: [impl] change THREAD_PASS macro. (regint.h)
2003/02/04: [dist] add API document file doc/API.
2003/02/04: [tune] if sub_anchor has ANCHOR_BEGIN_LINE then
                   set REG_OPTIMIZE_EXACT_BM in set_optimize_exact_info().
2003/02/04: [spec] reimplement regex_clone() and it is obsoleted.
2003/02/04: [bug]  add REGERR_OVER_THREAD_PASS_LIMIT_COUNT
                   to regerror.c regposix.c.
2003/02/03: [bug]  Hankaku-Kana may be second byte in Shift_JIS
                   regex_is_allow_reverse_match().
2003/02/03: [impl] add optimization type REG_OPTIMIZE_EXACT_BM_NOT_REV.
                   remove exact_allow_reverse_match member in regex_t.
2003/02/03: [impl] add exact_allow_reverse_match member in regex_t.
2003/02/03: [impl] compile-search conflict in regex_search() is handled.
2003/02/01: [tune] decrease regex_region_clear() calling from regex_search().
2003/02/01: [tune] remove region argument from match_at().
2003/01/31: [tune] don't use strlen() in regexec() and regcomp().
2003/01/31: [tune] decrease regex_reduce_chain() calling in regex_search().
2003/01/31: [bug]  STRING_CMP() in regexec.c was wrong in ignore-case.
2003/01/31: [impl] convert to lower-case char at string compile time.
                   change SBTRANSCMP() in regexec.c.
2003/01/31: [impl] rename TTRANS() to TOLOWER().
2003/01/30: [bug]  .c.o --> .c.obj in win32\Makefile.
2003/01/30: [impl] add -DNOT_RUBY to Makefile.in.
                   NOT_RUBY is referred in regint.h for escape double
                   including config.h.
2003/01/30: [impl] when string hasn't case ambiguity, don't compile
                   to ignore case opcode.
2003/01/29: [impl] add SJIS, UTF-8 test_sb() test.
2003/01/29: [dist] add INSTALL-RUBY file.
2003/01/28: [test] success in Cygwin, Ruby 1.8.0 (2003-01-27).
2003/01/24: [inst] add rback target to Makefile.in.
2003/01/24: [impl] change SBCMP() -> IS_NEWLINE() in match_at().
2003/01/23: [impl] add encoding arg to scan_xxxx_number().
2003/01/23: [impl] rename WCInt to WCINT.
2003/01/22: [bug]  POSIX API regexec() was not thread safe.
                   remove region member from POSIX regex_t.
            [new]  add search time option REG_OPTION_POSIX_REGION.
                   (region argument is treated as regmatch_t[] type)
                   speed up regexec().
2003/01/22: [memo] start CVS entry in my box.

2003/01/21: Version 1.6

2003/01/21: [test] Mac OS X 10.1, Ruby 1.8.0 (2003-01-20)
2003/01/20: [impl] add UTF-8 check to test.rb.   (thanks UENO Katsuhiro)
2003/01/18: [impl] change REGION_NOTPOS to REG_REGION_NOTPOS in regex.h.
2003/01/17: [dist] add sample/simple.c.
2003/01/17: [inst] add configure option --with-rubydir.
2003/01/17: [bug]  badly implemented POSIX API options.
                   default:     /./ not match "\n", anchor not match "\n"
                   REG_NEWLINE: /./ not match "\n", anchor match "\n"
2003/01/16: [impl] rewrite POSIX API regexec() for speed up.
2003/01/16: [impl] add region member to POSIX regex_t struct.
2003/01/16: [inst] rename library file from 'libregex.a' to 'libonig.a'.
2003/01/15: [dist] add testc.c to distribution file.
2003/01/15: [test] success in 'make rtest/ctest/ptest' on Windows 2000.
2003/01/15: [bug]  change '/' to \' in win32/Makefile.
2003/01/14: [test] success in Ruby make test on Windows 2000.
                   VC++6.0, Ruby 1.6.8 (2003-01-12)
2003/01/14: [inst] change Makefile.in and win32/Makefile.
2003/01/11: [inst] changes for Win32 platform. (regint.h, reggnu.c, regcomp.c)
2003/01/11: [dist] add win32 directory. (config.h, Makefile, testc.c)
2003/01/10: [inst] add onigposix.h to install target. (Makefile.in)
2003/01/10: [bug]  lacked a comma in ESTRING[]. (regposerr.c)
2003/01/10: [bug]  local variable name was wrong. buf -> tbuf (regerror())
2003/01/10: [spec] remove REG_RUBY_M17N case from onigposix.h and regposix.c.

2003/01/09: Version 1.5

2003/01/09: [inst] replace Ruby re.c.XXX.patch files. (166 -> 168, 172 -> 180)
2003/01/09: [new]  implement POSIX API.   (thanks knu)
                   (onigposix.h, regposix.c, regposerr.c)
2003/01/08: [spec] remove REGERR_END_PATTERN_AFTER_BACKSLASH in regex.h.
2003/01/08: [spec] region arg can be NULL in regex_search() and regex_match().

2003/01/08: Version 1.4

2003/01/08: [inst] add test program converter (test.rb -> testc.c).
2003/01/08: [bug]  move GET_WCINT() from regcomp.c to regint.h.
2003/01/07: [inst] add new test script (test.rb).
2002/12/30: [bug]  wrong merge in multibyte mode (alt_merge_opt_exact_info()).
2002/12/28: [inst] add rtest target to Makefile.in.
2002/12/28: [bug]  /\xfe/.match("\xfe") mismatch in multibyte mode.
                   add "raw" flag arg to concat_opt_exact_info_str().
2002/12/25: [bug]  check condition was wrong in alt_merge_opt_map_info().
2002/12/25: [impl] add threshold_len check in regex_search().
2002/12/23: [bug]  prec-read in alternative (/a|(?=z).f/.match("zf") => nil)
2002/12/23: [bug]  \G in alternative (/a|\Gz/.match("bza") => "z").
                   add start member in MatchArg. (regexec.c)
2002/12/21: [impl] **** rewrite all optimization process. ****
2002/12/16: [impl] remove node subtype EFFECT_EMPTY.
2002/12/12: [impl] reconstruct node types. (regcomp.c)
2002/12/11: [impl] add regerror.c
2002/12/10: [bug]  [ruby-dev:19042] (thanks Nobu)
                   anchor(\G etc...) influenced outside of "|". (/a|\Gb/)
2002/11/30: [bug]  [ruby-dev:18966] (thanks Nobu)
                   char-class(\S, [^\s] etc...) optimize map-info was wrong.
2002/11/29: [bug]  infinite loop on NULL-pointer str search (regex_search()).
                   (thanks matz)
2002/11/29: [bug]  change static -> extern (regex_chain_reduce()).
2002/11/29: [bug]  change encoding to RegDefaultCharEncoding
                   in re_recompile_pattern(). (adapt to re.c)
2002/04/24: [spec] USE_ONIGURUMA_EXTENSION is disabled in default.
2002/04/24: [new]  add searching time option: REG_OPTION_NOTBOL/NOTEOL.
                   add searching time option argument to regex_search() and
                   regex_match(). (prepare for POSIX API)
2002/04/20: [impl] divide regex.c file into regcomp.c, regexec.c, reggnu.c
                   and regint.h.
2002/04/09: [impl] move IS_MULTILINE() to outside of loop in OP_ANYCHAR_STAR.
2002/04/08: [impl] don't use OP_REPEAT operator for '??'.
2002/04/06: [impl] reduce redundant nested repeat operators(?,*,+,??,*?,+?).
                   ex. (?:a*)?, (?:a??)* etc..
2002/04/06: [spec] should not warn for /(?:a?)+?/.
2002/04/04: [spec] should allow fixed length alternative and repeat pattern
                   in look-behind. ex. /(?<=(a|b){3})/  (thanks Guy Decoux)
2002/04/02: [spec] should warn for /(?:a+)?/ and /(?:a*)??/.  (thanks akr)

2002/04/01: Version 1.3

2002/04/01: [dist] add COPYING.
2002/03/30: [spec] warn redundant nested repeat operator
                   in Ruby verbose mode.  ex. (?:a*)?
2002/03/30: [spec] nested repeat operator error check should be
                   same with GNU regex.             (thanks Guy Decoux)
2002/03/30: [new]  add \x{hexadecimal-wide-char}.   (thanks matz)
2002/03/27: [bug]  MBCTYPE_XXX symbol values should be same with GNU regex.
2002/03/27: [impl] add THREAD_ATOMIC to regex_clone(), regex_init(), regex_end().
2002/03/25: [spec] if encoding is utf-8, allow combination of singlebyte and
                   multibyte code range in char class.
                   (cancelled 2002/04/01: for M17N compatibility)
2002/03/25: [dist] description of the license condition is added to README.
2002/03/23: [bug]  should set all bits of reg->mem_stats,
                   if REG_OPTION_FIND_LONGEST or REG_OPTION_NOT_EMPTY.
2002/03/23: [new]  add a new option REG_OPTION_NOT_EMPTY.
2002/03/20: [spec] allow incompleted left brace as an usual char.
                   ex. /{/, /({)/, /a{2,3/ etc...
2002/03/20: [impl] serialize integer in bytecode.
                   (switch by UNALIGNED_WORD_ACCESS in regex.c)
2002/03/20: [impl] change re_mbcinit() for REG_RUBY_M17N.
2002/03/19: [impl] word alignment of char class multi-byte code ranges.
2002/03/19: [impl] replace OP_EXACTMB4N with OP_EXACTMB3N.
2002/03/19: [bug]  OP_CCLASS_MB_NOT process in matchAt() is wrong.
2002/03/19: [new]  add re_mbctab[] for Ruby extension library compatibility.
2002/03/19: [spec] allow nested repeat operator, if operator is {n,m} type.
2002/03/19: [new]  add REG_IS_PATTERN_ERROR(ecode) in regex.h
2002/03/18: [spec] /[a-b-c]/ should be error.
2002/03/18: [bug]  /[\w-a]/ should be error.        (thanks Guy Decoux)
2002/03/18: [bug]  /[\]/ should be error.           (thanks Guy Decoux)
2002/03/18: [bug]  /()*/ etc.. should not be error. (thanks Guy Decoux)
2002/03/18: [spec] /a{1}*/ should not be error.     (thanks Guy Decoux)
2002/03/18: [bug]  ab{2}{3} was interpreded to (?:a(?:b{2})){3}
                   (thanks Guy Decoux)
2002/03/18: [bug]  abort /(?i)*a/ etc...            (thanks Guy Decoux)
2002/03/18: [bug]  abort /a|*/,/a|{1}/ etc...       (thanks Guy Decoux)

2002/03/13: Version 1.2

2002/03/13: [test] success in rubicon/builtin/AllBuiltinTests.rb.
                   (thanks rubicon)
2002/03/13: [bug]  OP_EXACTMBN process in matchAt() is wrong.
2002/03/13: [bug]  start argument of BackwardSearchRange() is wrong.
2002/03/12: [spec] change function name style from CamelCase
                   to underline_separation. (includes API)
2002/03/12: [bug]  if pattern has nested null-check, cause infinite loop.
                   correct STACK_NULL_CHECK() macro. (thanks Guy Decoux)
2002/03/11: [bug]  it is wrong that four numbers to continue as
                   an octal value in scanBackSlash(). ex. /\0111/
                   (thanks matz)
2002/03/11: [new]  \k (single-byte word char), \K (multi-byte char).
2002/03/09: [inst] add two targets to Makefile.in (166 and 172).
2002/03/09: [spec] decrease REG_MAX_BACKREF_NUM, REG_MAX_REPEAT_NUM
                   values.
2002/03/08: [spec] allow use of "\A"(begin-buf) in look-behind.
2002/03/08: [impl] add a new opcode OP_PUSH_IF_PEEK_NEXT.
2002/03/08: [impl] add a new opcode OP_ANYCHAR_STAR_PEEK_NEXT.
2002/03/07: [spec] prohibit use of capture group "(...)"
                   in negative look-behind.
2002/03/07: [inst] add configure.in, config.h.in, Makefile.in.
2002/03/07: [impl] call Init_REGEX_STAT() in RegexInit().
2002/03/07: [spec] less length string match with negative look-behind.
                   ex. /(?<!XXX)a/.match("Xa").    (thanks Nobu)
2002/03/06: [impl] expand repeated string, if expanded length <= 100.
                   ex. /(?:abc){10}/
2002/03/06: [new]  add a symbol REG_TRANSTABLE_USE_DEFAULT in regex.h.
2002/03/06: [impl] rename RegDefaultCharCode to RegDefaultCharEncoding.
2002/03/06: [bug]  if pattern has NULL(\000) char, infinite loop happens
                   in ScanMakeNode(). (beware of strchr(). thanks Nobu)
2002/03/06: [bug]  range argument of ForwardSearchRange() is wrong.
                   ex. /\A.a/, /\G.a/ mismatched with "aa". (thanks Nobu)
2002/03/05: [new]  add RegexMatch() API. rename regexMatch() to matchAt().
2002/03/05: [impl] change function definition style.
2002/03/05: [impl] abolish use of macro symbol which name begin with underline.
2002/03/04: [bug]  make up a break-statement in compileTree().
                   (compile error on Mac OS X 10.1.3)

2002/03/04: Version 1.1

2002/03/04: [impl] replace STK_BOTTOM with STK_ALT.
2002/03/02: [impl] add new opcode OP_FINISH and new stack type
                   STK_BOTTOM for (little bit) speed up STACK_POP.
2002/03/02: [impl] add new opcode OP_EXACT1_IC, OP_EXACTN_IC
                   for compile time ignore case check.
                   remove opcode OP_EXACT1_RAW, OP_EXACTN_RAW.
2002/03/02: [impl] add OpTime info to statistical data.
2002/02/28: [bug]  sub_anchor($) in ForwardSearch() and BackwardSearch().
                   ex. /$\x0az/.match("\nz")
2002/02/28: [new]  look-behind (?<=pattern), (?<!pattern).
2002/02/27: [bug]  use StackIndex instead of StackType* for realloc problem.
2002/02/27: [impl] use m17n_codepoint() as mb2wc() in REG_RUBY_M17N.
2002/02/27: [spec] undefined POSIX bracket /[[:xyz:]]/ should be syntax error.
2002/02/26: [bug]  ex. /$*/, /[a-]/, /((?i)a)b/   (thanks matz)

2002/02/25: Version 1.0 (first release)

--
[bug:  bug fix]
[API:  API change/new/delete]
[new:  new feature]
[spec: specification change]
[impl: implementation change]
[tune: tune for speed up]
[inst: changes for installation]
[dist: distribution change]
[test: test]
[dev:  development]
[memo: memo]
--
<create tag>
svn copy file:///home/<USER>/svnreps/svnrep_onig/trunk file:///home/<USER>/svnreps/svnrep_onig/tags/5.0.0 -m "ADD TAG: 5.0.0"

<set ignore files by .cvsignore>
svn propset svn:ignore -F .cvsignore .
svn commit -m "..."


<CVS: show all tags>
cvs history -T

<CVS: add tag>
cvs rtag "VERSION_X_X_X" oniguruma


<Homebrew install autotools>
> brew install autoconf
> brew install automake
> brew install libtool


<GNU Autotools: bootstrap>
* write Makefile.am and configure.in.
> libtoolize  or  glibtoolize
> aclocal
> autoheader
> automake --foreign --add-missing
> autoconf
> configure --with-rubydir=... CFLAGS="-O2 -Wall"


<GNU libtool: version management>

  VERSION = current:revision:age

  current:  interface number (from 0)
  revision: implementation number of same interface (from 0)
  age:      number of supported previous interfaces
            (if current only supported then age == 0)


<add SHA256 checksum>
MacOS X
$ shasum -a 256 -b onig-X.Y.Z.tar.gz > onig-X.Y.Z.tar.gz.sha256

<check SHA256 checksum>
MacOS X
$ shasum -a 256 -c onig-X.Y.Z.tar.gz.sha256

//END
