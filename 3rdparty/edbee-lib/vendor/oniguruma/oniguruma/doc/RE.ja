鬼車 正規表現 Version 6.9.9    2024/06/10

使用文法: ONIG_SYNTAX_ONIGURUMA (既定値)


1. 基本要素

  \       退避修飾 (エスケープ)  正規表現記号の有効/無効の制御
  |       選択子
  (...)   式集合   (グループ)
  [...]   文字集合 (文字クラス)


2. 文字

  \t              水平タブ         (0x09)
  \v              垂直タブ         (0x0B)
  \n              改行             (0x0A)
  \r              復帰             (0x0D)
  \b              後退空白         (0x08)
  \f              改頁             (0x0C)
  \a              鐘               (0x07)
  \e              退避修飾         (0x1B)
  \nnn            八進数表現                 符号化バイト値
  \xHH            十六進数表現               符号化バイト値
  \x{7HHHHHHH}    (1-8桁)  拡張十六進数表現  コードポイント値
  \o{17777777777} (1-11桁) 拡張八進数表現    コードポイント値
  \uHHHH          拡張十六進数表現           コードポイント値
  \cx             制御文字表現               コードポイント値
  \C-x            制御文字表現               コードポイント値
  \M-x            超  (x|0x80)               コードポイント値
  \M-\C-x         超 + 制御文字表現          コードポイント値

  ※ \bは、文字集合内でのみ有効


2.1 コードポイント連続表記

  十六進数表現コードポイント (1-8桁)
  \x{7HHHHHHH 7HHHHHHH ... 7HHHHHHH}

  八進数表現コードポイント (1-11桁)
  \o{17777777777 17777777777 ... 17777777777}


3. 文字種

  .        任意文字 (改行を除く: オプションに依存)

  \w       単語構成文字

           Unicode以外の場合:
             英数字, "_" および 多バイト文字。

           Unicodeの場合:
             General_Category -- (Letter|Mark|Number|Connector_Punctuation)

  \W       非単語構成文字

  \s       空白文字

           Unicode以外の場合:
             \t, \n, \v, \f, \r, \x20

           Unicodeの場合:
             U+0009, U+000A, U+000B, U+000C, U+000D, U+0085(NEL),
             General_Category -- Line_Separator
                              -- Paragraph_Separator
                              -- Space_Separator

  \S       非空白文字

  \d       10進数字

           Unicodeの場合: General_Category -- Decimal_Number

  \D       非10進数字

  \h       16進数字    [0-9a-fA-F]

  \H       非16進数字

  \R       汎改行  (* 文字集合の中では使用できない)
           "\r\n" or \n,\v,\f,\r  (* 但し \r\nから\rにはバックトラックしない)

           Unicodeの場合:
             "\r\n" or \n,\v,\f,\r or U+0085, U+2028, U+2029

  \N       非改行文字  (?-m:.)

  \O       真任意文字  (?m:.)      (* 原作)

  \X       文章区分    \X === (?>\O(?:\Y\O)*)

           この演算子の意味は、オプション (?y{..})の設定によって変化する。

           \Xは照合の開始位置が区分の境界かどうかを確認しない。
           それを確実にしたければ、\y\Xと書けば良い。

           [拡張書記素房-状態のとき] (デフォルト)
             Unicodeの場合:
               参照 [Unicode Standard Annex #29: http://unicode.org/reports/tr29/]

             Unicode以外の場合:  \X === (?>\r\n|\O)

           [単語-状態のとき]
             現在、Unicodeしかサポートしていない。
             参照 [Unicode Standard Annex #29: http://unicode.org/reports/tr29/]


  Character Property

    * \p{property-name}
    * \p{^property-name}    (negative)
    * \P{property-name}     (negative)

    property-name:

     + 全てのエンコーディングで有効
       Alnum, Alpha, Blank, Cntrl, Digit, Graph, Lower,
       Print, Punct, Space, Upper, XDigit, Word, ASCII,

     + EUC-JP, Shift_JISで有効
       Hiragana, Katakana

     + UTF8, UTF16, UTF32で有効
       doc/UNICODE_PROPERTIES参照



4. 量指定子

  欲張り

    ?       一回または零回
    *       零回以上
    +       一回以上
    {n,m}   (n <= m)  n回以上 かつ m回以下
    {n,}    n回以上
    {,n}    零回以上n回以下 ({0,n})
    {n}     n回


  無欲

    ??      零回または一回
    *?      零回以上
    +?      一回以上
    {n,m}?  (n <= m)  n回以上 かつ m回以下
    {n,}?   n回以上
    {,n}?   零回以上n回以下 (== {0,n}?)

        {n}? はONIG_SYNTAX_JAVAとONIG_SYNTAX_PERLでのみ無欲な指定子
        (その場合には、態々そう書く意味はないが)
        デフォルトの文法では、/a{n}?/ === /(?:a{n})?/


  強欲 (欲張りで、繰り返しに成功した後は回数を減らすような後退再試行をしない)

    ?+      一回または零回
    *+      零回以上
    ++      一回以上
    {n,m}   (n > m)  m回以上 かつ n回以下

        {n,m}+, {n,}+, {n}+ は、ONIG_SYNTAX_JAVAとONIG_SYNTAX_PERLでのみ
        強欲な指定子

    例. /a*+/ === /(?>a*)/


5. 錨

  ^       行頭
  $       行末
  \b      単語境界
  \B      非単語境界

  \A      文字列先頭
  \Z      文字列末尾、または文字列末尾の改行の直前
  \z      文字列末尾
  \G      探索開始位置
  \K      保持 (結果の開始位置をこの位置に保つ)


  \y      文章区分 境界
  \Y      文章区分 非境界

          この演算子の意味は、オプション (?y{..})の設定によって変化する。

           [拡張書記素房-状態のとき] (デフォルト)
             Unicodeの場合:
               参照 [Unicode Standard Annex #29: http://unicode.org/reports/tr29/]

             Unicode以外の場合:
               \rと\nの間を除く全ての位置

           [単語-状態のとき]
             現在、Unicodeしかサポートしていない。
             参照 [Unicode Standard Annex #29: http://unicode.org/reports/tr29/]



6. 文字集合

  ^...    否定   (最低優先度演算子)
  x-y     範囲   (xからyまで)
  [...]   集合   (文字集合内文字集合)
  ..&&..  積演算 (^の次に優先度が低い演算子)

     例. [a-w&&[^c-g]z] ==> ([a-w] and ([^c-g] or z)) ==> [abh-w]

  ※ '[', '-', ']'を、文字集合内で通常文字の意味で使用したい場合には、
     これらの文字を'\'で退避修飾しなければならない。


  POSIXブラケット ([:xxxxx:], 否定 [:^xxxxx:])

    Unicode以外の場合:

      alnum    英数字
      alpha    英字
      ascii    0 - 127
      blank    \t, \x20
      cntrl
      digit    0-9
      graph    多バイト文字全部を含む
      lower
      print    多バイト文字全部を含む
      punct
      space    \t, \n, \v, \f, \r, \x20
      upper
      xdigit   0-9, a-f, A-F
      word     英数字, "_" および 多バイト文字

    Unicodeの場合:

      alnum    Alphabetic | Decimal_Number
      alpha    Alphabetic
      ascii    U+0000 - U+007F
      blank    Space_Separator | U+0009
      cntrl    U+0000 - U+001F, U+007F - U+009F
      digit    Decimal_Number
      graph    ^White_Space && ^[[:cntrl:]] && ^Unassigned && ^Surrogate
      lower    Lowercase
      print    [[:graph:]] | Space_Separator
      punct    Punctuation | Symbol
      space    White_Space
      upper    Uppercase
      xdigit   U+0030 - U+0039 | U+0041 - U+0046 | U+0061 - U+0066
               (0-9, a-f, A-F)
      word     Alphabetic | Mark | Decimal_Number | Connector_Punctuation



7. 拡張式集合

  (?#...)           注釈

  (?imxWDSPy-imxWDSP:式)   式オプション

                            i: 大文字小文字照合
                            m: 複数行
                            x: 拡張形式
                            W: wordがASCIIのみ (\w, \p{Word}, [[:word:]])
                               word境界がASCIIのみ (\b)
                            D: digitがASCIIのみ (\d, \p{Digit}, [[:digit:]])
                            S: spaceがASCIIのみ (\s, \p{Space}, [[:space:]])
                            P: POSIXプロパティがASCIIのみ (W,D,Sを全て含んでいる)
                               (alnum, alpha, blank, cntrl, digit, graph,
                                lower, print, punct, space, upper, xdigit, word)

                            y{?}: 文章区分状態
                               このオプションは\X, \y, \Yの意味を変更する。
                               現在このオプションはUnicodeでしかサポートしていない
                               y{g}: 拡張書記素房-状態 (デフォルト)
                               y{w}: 単語-状態
                                     参照 [Unicode Standard Annex #29]

  (?imxWDSPy-imxWDSP)  孤立オプション

                      * これは次の')'またはパターンの終わりまでのグループを形成する
                        /ab(?i)c|def|gh/ == /ab(?i:c|def|gh)/

  * (?i)オプションは、wordタイプ(\w, \p{Word})には有効ではない。但しwordタイプが文字クラスの中で使用された場合は有効になる。しかし、このことはwordタイプを(?W)オプションと共に使用した時にしか気にする必要はないであろう。


  /(?CIL).../, /(?CIL:...)/   全体オプション

                            このオプションは、正規表現全体に影響を及ぼす位置
                            に置かれなければならない

                            C: ONIG_OPTION_DONT_CAPTURE_GROUP
                            I: ONIG_OPTION_IGNORECASE_IS_ASCII
                            L: ONIG_OPTION_FIND_LONGEST

  (式)              捕獲式集合
  (?:式)            非捕獲式集合

  (?=式)            先読み
  (?!式)            否定先読み

  (?<=式)           戻り読み
  (?<!式)           否定戻り読み

                    * 戻り読み、否定戻り読みの式の中では、不在停止演算子
                      (?~|expr)と範囲消去演算子(?~|)を使用することはできない

                    * 戻り読み、否定戻り読みの中では、ignore-caseオプションの
                      対応が制限される。一文字と一文字の間の変換しか対応しない。
                      (Unicodeでの複数文字の変換に対応しない)

  (?>式)            原子的式集合
                    式全体を通過したとき、式の中での後退再試行を行なわない

  (?<name>式), (?'name'式)
                    名前付き捕獲式集合
                    式集合に名前を割り当てる(定義する)。
                    (名前は単語構成文字でなければならない。)

                    名前だけでなく、捕獲式集合と同様に番号も割り当てられる。
                    番号指定が禁止されていない状態 (10. 捕獲式集合 を参照)
                    のときは、名前を使わないで番号でも参照できる。

                    複数の式集合に同じ名前を与えることは許されている。
                    この場合には、この名前を使用した後方参照は可能であるが、
                    部分式呼出しはできない。


  <呼び出し>

  * 内容の呼び出し
  (?{...contents...})           前進中のみの呼び出し
  (?{...contents...}D)          Dは方向指定文字
                                D = 'X': 前進中および後退中
                                    '<': 後退中のみ
                                    '>': 前進中のみ
  (?{...contents...}[tag])      名札付き
  (?{...contents...}[tag]D)

                              * エスケープ文字はcontentsの中で何の機能も持たない
                              * contentsは、'{'文字で始まってはならない

  (?{{{...contents...}}})     contentsの中のn個連続の'}'は、(n+1)個連続の{{{...}}}
                              の中で許される

    tagに許される文字: _ A-Z a-z 0-9 (* 最初の文字: _ A-Z a-z)


  * 名前の呼び出し
  (*name)
  (*name{args...})         引数付き
  (*name[tag])             名札付き
  (*name[tag]{args...})

    nameに許される文字: _ A-Z a-z 0-9 (* 最初の文字: _ A-Z a-z)
    tag に許される文字: _ A-Z a-z 0-9 (* 最初の文字: _ A-Z a-z)



  <不在機能群>

  (?~不在)          不在繰り返し  (*原案 田中哲)
                    これは .*(より正確には\O*)のように動作するが、<不在>に
                    適合する文字列を含まない範囲に制限される。
                    これは(?~|(?:不在)|\O*)の省略表記である。

  (?~|不在|式)      不在式  (* 原作)
                    これは<式>のように動作するが、<不在>に適合する文字列を
                    含まない範囲に制限される。

                    例 (?~|345|\d*)  "12345678"  ==> "12", "1", ""

  (?~|不在)         不在停止 (* 原作)
                    この演算子を通過した後は、対象文字列の適合範囲が
                    <不在>に適合する文字列を含まない範囲に制限される。

  (?~|)             範囲消去
                    不在停止の効果を消して、初期の状態にする。

     * 不在機能の入れ子には対応しておらず、その場合の挙動は不定とする。



  <条件文>

  (?(条件式)成功式|失敗式)    条件式が成功すれば成功式、失敗すれば失敗式を実行する
                             この機能の存在理由は、成功式が失敗しても失敗式には
                             行かないこと。これは他の正規表現で書くことができない。
                             もうひとつは、条件式が後方参照の番号/名前のとき、
                             後方参照値の有効性を調べる(文字列と照合はしない)
                             意味になる。

  (?(条件式)成功式)           条件式が成功すれば成功式を実行する
                             (条件式が通常の式のときには、この構文は不必要だが
                              今のところエラーにはしない。)


                    条件式は後方参照の番号/名前または普通の式を使用できる。
                    条件式が後方参照の場合、成功式と失敗式の両方を省略可能であり、
                    この場合、後方参照値有効性を調べる(成功/失敗)機能のみになる。

  [後方参照値有効性確認器]  (* 原作)
    (?(n)), (?(-n)), (?(+n)), (?(n+level)) ...
    (?(<n>)), (?('-n')), (?(<+n>)) ...
    (?(<name>)), (?('name')), (?(<name+level>)) ...



8. 後方参照

  \n          番号指定参照     (n >= 1)
  \k<n>       番号指定参照     (n >= 1)
  \k'n'       番号指定参照     (n >= 1)
  \k<-n>      相対番号指定参照 (n >= 1)
  \k'-n'      相対番号指定参照 (n >= 1)
  \k<+n>      相対番号指定参照 (n >= 1)
  \k'+n'      相対番号指定参照 (n >= 1)
  \k<name>    名前指定参照
  \k'name'    名前指定参照

  名前指定参照で、その名前が複数の式集合で多重定義されている場合には、
  番号の大きい式集合から優先的に参照される。
  (マッチしないときには番号の小さい式集合が参照される)

  ※ 番号指定参照は、名前付き捕獲式集合が定義され、
     かつ ONIG_OPTION_CAPTURE_GROUPが指定されていない場合には、
     禁止される。(10. 捕獲式集合 を参照)


  ネストレベル付き後方参照

    level: 0, 1, 2, ...

    \k<n+level>     (n >= 1)
    \k<n-level>     (n >= 1)
    \k'n+level'     (n >= 1)
    \k'n-level'     (n >= 1)

    \k<name+level>
    \k<name-level>
    \k'name+level'
    \k'name-level'

    後方参照の位置から相対的な部分式呼出しネストレベルを指定して、そのレベルでの
    捕獲値を参照する。

    例-1.

      /\A(?<a>|.|(?:(?<b>.)\g<a>\k<b+0>))\z/.match("reer")

    例-2.

      r = Regexp.compile(<<'__REGEXP__'.strip, Regexp::EXTENDED)
      (?<element> \g<stag> \g<content>* \g<etag> ){0}
      (?<stag> < \g<name> \s* > ){0}
      (?<name> [a-zA-Z_:]+ ){0}
      (?<content> [^<&]+ (\g<element> | [^<&]+)* ){0}
      (?<etag> </ \k<name+1> >){0}
      \g<element>
      __REGEXP__

      p r.match('<foo>f<bar>bbb</bar>f</foo>').captures



9. 部分式呼出し ("田中哲スペシャル")   (* 原作)

  \g<name>    名前指定呼出し
  \g'name'    名前指定呼出し
  \g<n>       番号指定呼出し    (n >= 1)
  \g'n'       番号指定呼出し    (n >= 1)
  \g<0>       番号指定呼出し(全体呼び出し)
  \g'0'       番号指定呼出し(全体呼び出し)
  \g<-n>      相対番号指定呼出し (n >= 1)
  \g'-n'      相対番号指定呼出し (n >= 1)
  \g<+n>      相対番号指定呼出し (n >= 1)
  \g'+n'      相対番号指定呼出し (n >= 1)

  ※ 最左位置での再帰呼出しは禁止される。
     例. (?<name>a|\g<name>b)   => error
         (?<name>a|b\g<name>c)  => OK

  ※ 番号指定呼出しは、名前付き捕獲式集合が定義され、
     かつ ONIG_OPTION_CAPTURE_GROUPが指定されていない場合には、
     禁止される。 (10. 捕獲式集合 を参照)

  ※ 呼び出された式集合のオプション状態が呼出し側のオプション状態と異なっている
     とき、呼び出された側のオプション状態が有効である。

     例. (?-i:\g<name>)(?i:(?<name>a)){0} は "A" に照合成功する。


10. 捕獲式集合

  捕獲式集合(...)は、以下の条件に応じて振舞が変化する。
  (名前付き捕獲式集合は変化しない)

  case 1. /.../     (名前付き捕獲式集合は不使用、オプションなし)

     (...) は、捕獲式集合として扱われる。

  case 2. /.../g    (名前付き捕獲式集合は不使用、オプション 'g'を指定)

     (...) は、非捕獲式集合として扱われる。

  case 3. /..(?<name>..)../   (名前付き捕獲式集合は使用、オプションなし)

     (...) は、非捕獲式集合として扱われる。
     番号指定参照/呼び出しは不許可。

  case 4. /..(?<name>..)../G  (名前付き捕獲式集合は使用、オプション 'G'を指定)

     (...) は、捕獲式集合として扱われる。
     番号指定参照/呼び出しは許可。

  但し
    g: ONIG_OPTION_DONT_CAPTURE_GROUP
    G: ONIG_OPTION_CAPTURE_GROUP
    ('g'と'G'オプションは、ruby-dev MLで議論された。)

  これらの振舞の意味は、
  名前付き捕獲と名前無し捕獲を同時に使用する必然性のある場面は少ないであろう
  という理由から考えられたものである。


-----------------------------
補記 1. 文法依存オプション

   + ONIG_SYNTAX_ONIGURUMA
     (?m): 終止符記号(.)は改行と照合成功

   + ONIG_SYNTAX_PERL と ONIG_SYNTAX_JAVA
     (?s): 終止符記号(.)は改行と照合成功
     (?m): ^ は改行の直後に照合する、$ は改行の直前に照合する


補記 2. 独自拡張機能

   + 16進数数字、非16進数字  \h, \H
   + 真任意文字              \O
   + 文章区分境界            \y, \Y
   + 後方参照値有効性確認器  (?(...))
   + 名前付き捕獲式集合      (?<name>...), (?'name'...)
   + 名前指定後方参照        \k<name>
   + 部分式呼出し            \g<name>, \g<group-num>
   + 不在式                 (?~|...|...)
   + 不在停止               (?~|...)


補記 3. Perl 5.8.0と比較して存在しない機能

   + \N{name}
   + \l,\u,\L,\U,\C
   + (??{code})

   * \Q...\E
     但しONIG_SYNTAX_PERLとONIG_SYNTAX_JAVAでは有効


補記 4. Ruby 1.8 の日本語化 GNU regex(version 0.12)との違い

   + 文字Property機能追加 (\p{property}, \P{Property})
   + 16進数字タイプ追加 (\h, \H)
   + 戻り読み機能を追加
   + 強欲な繰り返し指定子を追加 (?+, *+, ++)
   + 文字集合の中の演算子を追加 ([...], &&)
     ('[' は、文字集合の中で通常の文字として使用するときには
      退避修飾しなければならない)
   + 名前付き捕獲式集合と、部分式呼出し機能追加
   + 多バイト文字コードが指定されているとき、
     文字集合の中で八進数または十六進数表現の連続は、多バイト符号で表現された
     一個の文字と解釈される
     (例. [\xa1\xa2], [\xa1\xa7-\xa4\xa1])
   + 文字集合の中で、一バイト文字と多バイト文字の範囲指定は許される。
     ex. /[a-あ]/
   + 孤立オプションの有効範囲は、その孤立オプションを含んでいる式集合の
     終わりまでである
     例. (?:(?i)a|b) は (?:(?i:a|b)) と解釈される、(?:(?i:a)|b)ではない
   + 孤立オプションはその前の式に対して透過的ではない
     例. /a(?i)*/ は文法エラーとなる
   + 不完全な繰り返し範囲指定子は通常の文字列として許可される
     例. /{/, /({)/, /a{2,3/
   + 否定的POSIXブラケット [:^xxxx:] を追加
   + POSIXブラケット [:ascii:] を追加
   + 先読みの繰り返しは不許可
     例. /(?=a)*/, /(?!b){5}/
   + 数値で指定された文字に対しても、大文字小文字照合オプションは有効
     例. /\x61/i =~ "A"
   + 繰り返し回数指定で、最低回数の省略(0回)ができる
     /a{,n}/ == /a{0,n}/
     最低回数と最大回数の同時省略は許されない。(/a{,}/)
   + /a{n}?/は無欲な演算子ではない。
     /a{n}?/ == /(?:a{n})?/
   + 無効な後方参照をチェックしてエラーにする。
     /\1/, /(a)\2/
   + 無限繰り返しの中で、長さ零での照合成功は繰り返しを中断させるが、
     このとき、中断すべきかどうかの判定として、捕獲式集合の捕獲状態の
     変化まで考慮している
     /(?:()|())*\1\2/ =~ ""
     /(?:\1a|())*/ =~ "a"

終り
