鬼車インターフェース Version 6.9.10   2024/05/26

#include <oniguruma.h>


# int onig_initialize(OnigEncoding use_encodings[], int num_encodings)

  ライブラリの初期化
  最初に呼び出す必要がある。

  * onig_init() は廃止

  引数
  1 use_encodings:         使用する文字エンコーディングの配列
  2 num_encodings:         文字エンコーディングの数

  戻り値
  正常終了: ONIG_NORMAL == 0
  エラー時: エラーコード < 0


# int onig_error_code_to_str(UChar* err_buf, int err_code, ...)

  エラーメッセージを取得する。

  この関数を、onig_new()の結果に対して呼び出す場合には、onig_new()のpattern引数を
  メモリ解放するよりも前に呼び出さなければならない。

  戻り値
  正常終了: エラーメッセージ文字列のバイト長

  引数
  1 err_buf:              エラーメッセージを格納する領域
                          (必要なサイズ: ONIG_MAX_ERROR_MESSAGE_LEN)
  2 err_code:             エラーコード
  3 err_info (optional):  onig_new()のerr_info


# void onig_set_warn_func(OnigWarnFunc func)

  警告通知関数をセットする。

  警告:
    '[', '-', ']' in character class without escape.
    ']' in pattern without escape.

  引数
  1 func:    警告関数    void (*func)(char* warning_message)


# void onig_set_verb_warn_func(OnigWarnFunc func)

  詳細警告通知関数をセットする。

  詳細警告:
    redundant nested repeat operator.

  引数
  1 func:    詳細警告関数    void (*func)(char* warning_message)


# int onig_new(regex_t** reg, const UChar* pattern, const UChar* pattern_end,
            OnigOptionType option, OnigEncoding enc, OnigSyntaxType* syntax,
            OnigErrorInfo* err_info)

  正規表現オブジェクト(regex)を作成する。

  戻り値
  正常終了: ONIG_NORMAL == 0
  エラー時: エラーコード < 0

  引数
  1 reg:         作成された正規表現オブジェクトを返すアドレス
  2 pattern:     正規表現パターン文字列
  3 pattern_end: 正規表現パターン文字列の終端アドレス(pattern + pattern length)
  4 option:      正規表現コンパイル時オプション

      ONIG_OPTION_NONE               オプションなし
      ONIG_OPTION_SINGLELINE         '^' -> '\A', '$' -> '\Z'
      ONIG_OPTION_MULTILINE          '.'が改行にマッチする
      ONIG_OPTION_IGNORECASE         曖昧マッチ オン
      ONIG_OPTION_EXTEND             パターン拡張形式
      ONIG_OPTION_FIND_LONGEST       最長マッチ
      ONIG_OPTION_FIND_NOT_EMPTY     空マッチを無視
      ONIG_OPTION_NEGATE_SINGLELINE  ONIG_SYNTAX_POSIX_BASIC/POSIX_EXTENDED/PERL/PERL_NG/PYTHON/JAVAでデフォルトで有効なONIG_OPTION_SINGLELINEをクリアする。

      ONIG_OPTION_DONT_CAPTURE_GROUP 名前付き捕獲式集合のみ捕獲
      ONIG_OPTION_CAPTURE_GROUP      名前無し捕獲式集合も捕獲
      ONIG_OPTION_IGNORECASE_IS_ASCII  IGNORECASE((?i))をASCII文字の範囲に制限する
      ONIG_OPTION_WORD_IS_ASCII      wordがASCIIのみ (\w, \p{Word}, [[:word:]])
                                     word boundがASCIIのみ (\b)
      ONIG_OPTION_DIGIT_IS_ASCII     digitがASCIIのみ (\d, \p{Digit}, [[:digit:]])
      ONIG_OPTION_SPACE_IS_ASCII     spaceがASCIIのみ (\s, \p{Space}, [[:space:]])
      ONIG_OPTION_POSIX_IS_ASCII     POSIXプロパティがASCIIのみ
                                     (word, digit, spaceを全て含んでいる)
                                     (alnum, alpha, blank, cntrl, digit, graph,
                                      lower, print, punct, space, upper, xdigit,
                                      word)
      ONIG_OPTION_TEXT_SEGMENT_EXTENDED_GRAPHEME_CLUSTER  拡張書記素房モード
      ONIG_OPTION_TEXT_SEGMENT_WORD                       単語モード

      * ONIG_OPTION_FIND_LONGEST はonig_search()の後方探索では正しく動作しない

  5 enc:        文字エンコーディング

      ONIG_ENCODING_ASCII         ASCII
      ONIG_ENCODING_ISO_8859_1    ISO 8859-1
      ONIG_ENCODING_ISO_8859_2    ISO 8859-2
      ONIG_ENCODING_ISO_8859_3    ISO 8859-3
      ONIG_ENCODING_ISO_8859_4    ISO 8859-4
      ONIG_ENCODING_ISO_8859_5    ISO 8859-5
      ONIG_ENCODING_ISO_8859_6    ISO 8859-6
      ONIG_ENCODING_ISO_8859_7    ISO 8859-7
      ONIG_ENCODING_ISO_8859_8    ISO 8859-8
      ONIG_ENCODING_ISO_8859_9    ISO 8859-9
      ONIG_ENCODING_ISO_8859_10   ISO 8859-10
      ONIG_ENCODING_ISO_8859_11   ISO 8859-11
      ONIG_ENCODING_ISO_8859_13   ISO 8859-13
      ONIG_ENCODING_ISO_8859_14   ISO 8859-14
      ONIG_ENCODING_ISO_8859_15   ISO 8859-15
      ONIG_ENCODING_ISO_8859_16   ISO 8859-16
      ONIG_ENCODING_UTF8          UTF-8
      ONIG_ENCODING_UTF16_BE      UTF-16BE
      ONIG_ENCODING_UTF16_LE      UTF-16LE
      ONIG_ENCODING_UTF32_BE      UTF-32BE
      ONIG_ENCODING_UTF32_LE      UTF-32LE
      ONIG_ENCODING_EUC_JP        EUC-JP
      ONIG_ENCODING_EUC_TW        EUC-TW
      ONIG_ENCODING_EUC_KR        EUC-KR
      ONIG_ENCODING_EUC_CN        EUC-CN
      ONIG_ENCODING_SJIS          Shift_JIS
      ONIG_ENCODING_KOI8_R        KOI8-R
      ONIG_ENCODING_CP1251        CP1251
      ONIG_ENCODING_BIG5          Big5
      ONIG_ENCODING_GB18030       GB18030

      または、ユーザが定義したOnigEncodingTypeデータのアドレス

  6 syntax:     正規表現パターン文法定義

      ONIG_SYNTAX_ASIS              plain text
      ONIG_SYNTAX_POSIX_BASIC       POSIX Basic RE
      ONIG_SYNTAX_POSIX_EXTENDED    POSIX Extended RE
      ONIG_SYNTAX_EMACS             Emacs
      ONIG_SYNTAX_GREP              grep
      ONIG_SYNTAX_GNU_REGEX         GNU regex
      ONIG_SYNTAX_JAVA              Java (Sun java.util.regex)
      ONIG_SYNTAX_PERL              Perl
      ONIG_SYNTAX_PERL_NG           Perl + 名前付き捕獲式集合
      ONIG_SYNTAX_PYTHON            Python
      ONIG_SYNTAX_ONIGURUMA         Oniguruma
      ONIG_SYNTAX_DEFAULT           default (== ONIG_SYNTAX_ONIGURUMA)
                                    onig_set_default_syntax()

      または、ユーザが定義したOnigSyntaxTypeデータのアドレス

  7 err_info: エラー情報を返すためのアドレス
              onig_error_code_to_str()の三番目の引数として使用する



# int onig_new_without_alloc(regex_t* reg, const UChar* pattern,
            const UChar* pattern_end,
            OnigOptionType option, OnigEncoding enc, OnigSyntaxType* syntax,
            OnigErrorInfo* err_info)

  正規表現オブジェクト(regex)を作成する。
  regの領域を内部で割り当てない。

  戻り値
  正常終了: ONIG_NORMAL == 0
  エラー時: エラーコード < 0



# int onig_new_deluxe(regex_t** reg, const UChar* pattern, const UChar* pattern_end,
                      OnigCompileInfo* ci, OnigErrorInfo* einfo)

  この関数は廃止(使用不可)。
  パターンと対象文字列の文字エンコーディングが異なる場合を許さなくなった。

  正規表現オブジェクト(regex)を作成する。
  この関数は、onig_new()のデラックス版。

  戻り値
  正常終了: ONIG_NORMAL == 0
  エラー時: エラーコード < 0

  引数
  1 reg:         作成された正規表現オブジェクトを返すアドレス
  2 pattern:     正規表現パターン文字列
  3 pattern_end: 正規表現パターン文字列の終端アドレス(pattern + pattern length)
  4 ci:          コンパイル情報

    ci->num_of_elements: ciの要素数 (現在の版では: 5)
    ci->pattern_enc:     パターン文字列の文字エンコーディング
    ci->target_enc:      対象文字列の文字エンコーディング
    ci->syntax:          正規表現パターン文法定義
    ci->option:          正規表現コンパイル時オプション
    ci->case_fold_flag:  ONIG_OPTION_IGNORECASEモードでの
                         文字曖昧マッチ指定ビットフラグ

       ONIGENC_CASE_FOLD_MIN:           最小
       ONIGENC_CASE_FOLD_DEFAULT:       最小
                                        onig_set_default_case_fold_flag()

  5 err_info:    エラー情報を返すためのアドレス
                 onig_error_code_to_str()の三番目の引数として使用する


  異なる文字エンコーディングの組み合わせは、以下の場合にのみ許される。

    pattern_enc: ASCII, ISO_8859_1
    target_enc:  UTF16_BE, UTF16_LE, UTF32_BE, UTF32_LE

    pattern_enc: UTF16_BE/LE
    target_enc:  UTF16_LE/BE

    pattern_enc: UTF32_BE/LE
    target_enc:  UTF32_LE/BE


# void onig_free(regex_t* reg)

  正規表現オブジェクトのメモリを解放する。

  引数
  1 reg: 正規表現オブジェクト


# void onig_free_body(regex_t* reg)

  正規表現オブジェクトのメモリを解放する。(reg自身の領域を除いて)

  引数
  1 reg: 正規表現オブジェクト


# OnigMatchParam* onig_new_match_param()

  OnigMatchParamオブジェクトを生成し、onig_initialize_match_param()を使用して
  中身を初期化する。


# void onig_free_match_param(OnigMatchParam* mp)

  OnigMatchParamオブジェクトで使用しているメモリを開放する。

  引数
  1 mp: OnigMatchParamオブジェクト


# void onig_initialize_match_param(OnigMatchParam* mp)

  マッチパラメタ構造体にデフォルト値をセットする。
  マッチパラメタは、onig_match_with_param(), onig_search_with_param()で
  使用される。

  引数
  1 mp: マッチパラメタオブジェクトアドレス


# int onig_set_match_stack_limit_size_of_match_param(OnigMatchParam* mp, unsigned int limit)

  マッチスタックの最大深さをセットする。
  0は、無制限を表す。

  引数
  1 mp: マッチパラメタオブジェクトアドレス
  2 limit: 制限数

  正常終了戻り値: ONIG_NORMAL


# int onig_set_retry_limit_in_match_of_match_param(OnigMatchParam* mp, unsigned long limit)

  一回のマッチでのリトライ数の制限値をセットする。
  ０は無制限を意味する。

  引数
  1 mp: マッチパラメタオブジェクトアドレス
  2 limit: 制限回数

  正常終了戻り値: ONIG_NORMAL


# int onig_set_retry_limit_in_search_of_match_param(OnigMatchParam* mp, unsigned long limit)

  一回の検索でのリトライ数の制限値をセットする。
  ０は無制限を意味する。

  引数
  1 mp: マッチパラメタオブジェクトアドレス
  2 limit: 制限回数

  正常終了戻り値: ONIG_NORMAL


# int onig_set_progress_callout_of_match_param(OnigMatchParam* mp, OnigCalloutFunc f)

  前進時の内容の呼び出し(callouts)で呼び出される関数をセットする。
  もし０(NULL)がセットされると、前進時に呼び出しは起こらない。

  引数
  1 mp: マッチパラメタオブジェクトアドレス
  2 f: 呼び出される関数

  正常終了戻り値: ONIG_NORMAL


# int onig_set_retraction_callout_of_match_param(OnigMatchParam* mp, OnigCalloutFunc f)

  後退時の内容の呼び出し(callouts)で呼び出される関数をセットする。
  もし０(NULL)がセットされると、後退時に呼び出しは起こらない。

  引数
  1 mp: マッチパラメタオブジェクトアドレス
  2 f: 呼び出される関数

  正常終了戻り値: ONIG_NORMAL



# int onig_search(regex_t* reg, const UChar* str, const UChar* end, const UChar* start,
                   const UChar* range, OnigRegion* region, OnigOptionType option)

  正規表現で文字列を検索し、検索結果とマッチ領域を返す。
  正規表現オブジェクトの文字エンコーディングで、検索文字列として不正な文字列を渡してはいけない。

  戻り値
  正常終了: マッチ位置 (p - str >= 0)
  検索失敗: ONIG_MISMATCH (< 0)

    * 若しONIG_OPTION_CALLBACK_EACH_MATCHが使用されると、マッチするものがあってもONIG_MISMATCHが返される。

  引数
  1 reg:    正規表現オブジェクト
  2 str:    検索対象文字列
  3 end:    検索対象文字列の終端アドレス
  4 start:  検索対象文字列の検索先頭位置アドレス
  5 range:  検索対象文字列の検索終了位置アドレス
    前方探索  (start <= 探索される文字列 < range)
    後方探索  (range <= 探索される文字列 <= start)
  6 region: マッチ領域情報(region)  (NULLも許される)
  7 option: 検索時オプション

    ONIG_OPTION_NOTBOL              strの先頭を行頭および文字列先頭と看做さない
    ONIG_OPTION_NOTEOL              endを行末および文字列終端と看做さない
    ONIG_OPTION_NOT_BEGIN_STRING    strの先頭を文字列の先頭と看做さない (\A 失敗)
    ONIG_OPTION_NOT_END_STRING      endを文字列終端と看做さない (\z, \Z 失敗)
    ONIG_OPTION_NOT_BEGIN_POSITION  startを検索開始位置と看做さない (\G 失敗)

    ONIG_OPTION_CALLBACK_EACH_MATCH
      全てのマッチ成功に対してコールバック関数が呼び出される。
      (マッチ開始位置が同じものも含めて)
      ある位置でマッチするものが見つかっても探索が止まることはない。
      呼び出されるコールバック関数は、onig_set_callback_each_match()で与える。
      コールバック関数に渡される引数の中のuser_dataは、
      onig_set_callout_user_data_of_match_param(mp, user_data)で指定する。
      このため、user_dataを指定したい場合には、onig_search()ではなく、
      onig_search_with_param()を使用することになる。
      onig_set_callout_user_data_of_match_param()で指定するuser_dataは、
      calloutで使用されるuser_dataと共用される。

    ONIG_OPTION_MATCH_WHOLE_STRING  マッチした終端の位置がendになることを要求


# int onig_search_with_param(regex_t* reg, const UChar* str, const UChar* end,
                   const UChar* start, const UChar* range, OnigRegion* region,
                   OnigOptionType option, OnigMatchParam* mp)

  正規表現で文字列を検索し、検索結果とマッチ領域を返す。
  正規表現オブジェクトの文字エンコーディングで、検索文字列として不正な文字列を渡してはいけない。

  引数
  1-7:  onig_search()と同じ
  8 mp: マッチパラメタ値 (match_stack_limit, retry_limit_in_match, retry_limit_in_search)


# int onig_match(regex_t* reg, const UChar* str, const UChar* end,
                 const UChar* at, OnigRegion* region, OnigOptionType option)

  文字列の指定位置でマッチングを行い、結果とマッチ領域を返す。
  正規表現オブジェクトの文字エンコーディングで、検索文字列として不正な文字列を渡してはいけない。

  戻り値
  正常終了: マッチしたバイト長 (>= 0)
  not match: ONIG_MISMATCH      ( < 0)

    * 若しONIG_OPTION_CALLBACK_EACH_MATCHが使用されると、マッチするものがあってもONIG_MISMATCHが返される。

  引数
  1 reg:    正規表現オブジェクト
  2 str:    検索対象文字列
  3 end:    検索対象文字列の終端アドレス
  4 at:     検索対象文字列の検索アドレス
  5 region: マッチ領域情報(region)  (NULLも許される)
  6 option: 検索時オプション

    ONIG_OPTION_NOTBOL              strの先頭を行頭および文字列先頭と看做さない
    ONIG_OPTION_NOTEOL              endを行末および文字列終端と看做さない
    ONIG_OPTION_NOT_BEGIN_STRING    strの先頭を文字列の先頭と看做さない (\A 失敗)
    ONIG_OPTION_NOT_END_STRING      endを文字列終端と看做さない (\z, \Z 失敗)
    ONIG_OPTION_NOT_BEGIN_POSITION  startを検索開始位置と看做さない (\G 失敗)
    ONIG_OPTION_CALLBACK_EACH_MATCH 全てのマッチ成功に対してコールバック関数が呼び出される。
    ONIG_OPTION_MATCH_WHOLE_STRING  マッチした終端の位置がendになることを要求


# int onig_match_with_param(regex_t* reg, const UChar* str, const UChar* end,
                            const UChar* at, OnigRegion* region,
                            OnigOptionType option, OnigMatchParam* mp)

  文字列の指定位置でマッチングを行い、結果とマッチ領域を返す。
  正規表現オブジェクトの文字エンコーディングで、検索文字列として不正な文字列を渡してはいけない。

  引数
  1-6:  onig_match()と同じ
  7 mp: マッチパラメタ値 (match_stack_limit, retry_limit_in_match, retry_limit_in_search)


# int onig_scan(regex_t* reg, const UChar* str, const UChar* end,
                OnigRegion* region, OnigOptionType option,
                int (*scan_callback)(int, int, OnigRegion*, void*),
                void* callback_arg)

  正規表現で文字列をスキャンして、マッチングする毎にコールバック関数を呼び出す。
  正規表現オブジェクトの文字エンコーディングで、検索文字列として不正な文字列を渡してはいけない。

  戻り値
  正常終了: マッチ回数 (0回も含める)
  エラー:   エラーコード (< 0)
  中断: コールバック関数が０以外の戻り値を返したとき、その値を戻り値として中断

  引数
  1 reg:    正規表現オブジェクト
  2 str:    検索対象文字列
  3 end:    検索対象文字列の終端アドレス
  4 region: マッチ領域情報(region)  (NULLも許される)
  5 option: 検索時オプション
  6 scan_callback: コールバック関数
  7 callback_arg:  コールバック関数に渡される付加引数値


# int onig_regset_new(OnigRegSet** rset, int n, regex_t* regs[])

  regsetオブジェクトを生成する。
  全ての正規表現オブジェクトは、同じ文字エンコーディングでなければならない。
  全ての正規表現オブジェクトは、ONIG_OPTION_FIND_LONGESTオプションでコンパイルされていてはならない。

  引数
  1 rset: regsetオブジェクトを返すためのアドレス
  2 n:    正規表現の個数
  3 regs: 正規表現オブジェクトの配列

  戻り値
  正常終了: ONIG_NORMAL == 0
  エラー時: エラーコード < 0


# int onig_regset_add(OnigRegSet* set, regex_t* reg)

  regsetオブジェクトに正規表現を追加する。
  正規表現オブジェクトは、regsetと同じ文字エンコーディングでなければならない。
  正規表現オブジェクトは、ONIG_OPTION_FIND_LONGESTオプションでコンパイルされていてはならない。

  引数
  1 set: regsetオブジェクト
  2 reg: 正規表現オブジェクト

  戻り値
  正常終了: ONIG_NORMAL == 0
  エラー時: エラーコード < 0


# int onig_regset_replace(OnigRegSet* set, int at, regex_t* reg)

  regsetの中の一個の正規表現オブジェクトを別のものに変更する。
  若しreg引数の値がNULLであれば、at番目の正規表現オブジェクトを外す。(そして、以降の正規表現オブジェクトのインデックスは変化する)

  引数
  1 set: regsetオブジェクト
  2 at:  変更する場所のインデックス
  3 reg: 正規表現オブジェクト

  戻り値
  正常終了: ONIG_NORMAL == 0
  エラー時: エラーコード < 0


# void onig_regset_free(OnigRegSet* set)

  regsetオブジェクトとその中の正規表現オブジェクトの使用メモリを開放する。
  若し、同一の正規表現オブジェクトを重複して登録していれば、破壊的な状況になる。

  引数
  1 set: regsetオブジェクト


# int onig_regset_number_of_regex(OnigRegSet* set)

  regsetの中の正規表現オブジェクトの個数を返す。

  引数
  1 set: regsetオブジェクト


# regex_t* onig_regset_get_regex(OnigRegSet* set, int at)

  regsetのat番目の正規表現を返す。

  引数
  1 set: regsetオブジェクト
  2 at:  正規表現オブジェクトのインデックス (ゼロ開始)


# OnigRegion* onig_regset_get_region(OnigRegSet* set, int at)

  regsetのat番目の正規表現に対応する領域を返す。

  引数
  1 set: regsetオブジェクト
  2 at:  正規表現オブジェクトのインデックス (ゼロ開始)


# int onig_regset_search(OnigRegSet* set, const OnigUChar* str, const OnigUChar* end, const OnigUChar* start, const OnigUChar* range, OnigRegSetLead lead, OnigOptionType option, int* rmatch_pos)

  regsetによる検索を実行する。

  戻り値
  検索成功: マッチした正規表現オブジェクトのインデックス (ゼロ開始)
  検索失敗: ONIG_MISMATCH (< 0)
  エラー:   エラーコード   (< 0)

  引数
  1 set:    regsetオブジェクト
  2 str:    検索対象文字列
  3 end:    検索対象文字列の終端アドレス
  4 start:  検索対象文字列の検索先頭位置アドレス
  5 range:  検索対象文字列の検索終了位置アドレス
            (start <= 探索される文字列 < range)
  6 lead:   外側のループ要素
    ONIG_REGSET_POSITION_LEAD   (最左位置でマッチした結果を返す)
    ONIG_REGSET_REGEX_LEAD      (最左位置でマッチした結果を返す)
    ONIG_REGSET_PRIORITY_TO_REGEX_ORDER (最初にマッチした正規表現の結果を返す)
  7 option: 検索時オプション
    ONIG_OPTION_NOTBOL              strの先頭を行頭および文字列先頭と看做さない
    ONIG_OPTION_NOTEOL              endを行末および文字列終端と看做さない
    ONIG_OPTION_NOT_BEGIN_STRING    strの先頭を文字列の先頭と看做さない (\A 失敗)
    ONIG_OPTION_NOT_END_STRING      endを文字列終端と看做さない (\z, \Z 失敗)
    ONIG_OPTION_NOT_BEGIN_POSITION  startを検索開始位置と看做さない (\G 失敗)
  8 rmatch_pos: マッチした位置を返すためのアドレス (match_address - str)

  * ONIG_REGSET_POSITION_LEADとONIG_REGSET_REGEX_LEADは同じ結果を返す。
    これらの違いは検索時間にしか現れない。
    ほとんどの場合、ONIG_REGSET_POSITION_LEADのほうが速いと思われる。


# int onig_regset_search_with_param(OnigRegSet* set, const OnigUChar* str, const OnigUChar* end, const OnigUChar* start, const OnigUChar* range,  OnigRegSetLead lead, OnigOptionType option, OnigMatchParam* mps[], int* rmatch_pos)

  regsetとOnigMatchParamオブジェクトによる検索を実行する。

  戻り値
  検索成功: マッチした正規表現オブジェクトのインデックス (ゼロ開始)
  検索失敗: ONIG_MISMATCH (< 0)
  エラー:   エラーコード  (< 0)

  引数
  1 set:    regsetオブジェクト
  2 str:    検索対象文字列
  3 end:    検索対象文字列の終端アドレス
  4 start:  検索対象文字列の検索先頭位置アドレス
  5 range:  検索対象文字列の検索終了位置アドレス
            (start <= 探索される文字列 < range)
  6 lead:   外側のループ要素
    ONIG_REGSET_POSITION_LEAD   (最左位置でマッチした結果を返す)
    ONIG_REGSET_REGEX_LEAD      (最左位置でマッチした結果を返す)
    ONIG_REGSET_PRIORITY_TO_REGEX_ORDER (最初にマッチした正規表現の結果を返す)
  7 option: 検索時オプション
    ONIG_OPTION_NOTBOL              strの先頭を行頭および文字列先頭と看做さない
    ONIG_OPTION_NOTEOL              endを行末および文字列終端と看做さない
    ONIG_OPTION_NOT_BEGIN_STRING    strの先頭を文字列の先頭と看做さない (\A 失敗)
    ONIG_OPTION_NOT_END_STRING      endを文字列終端と看做さない (\z, \Z 失敗)
    ONIG_OPTION_NOT_BEGIN_POSITION  startを検索開始位置と看做さない (\G 失敗)
  8 mps:    OnigMatchParamオブジェクトの配列
  9 rmatch_pos: マッチした位置を返すためのアドレス (match_address - str)


# OnigRegion* onig_region_new(void)

  マッチ領域情報(region)を作成する。


# void onig_region_free(OnigRegion* region, int free_self)

  マッチ領域情報(region)で使用されているメモリを解放する。

  引数
  1 region:    マッチ領域情報オブジェクト
  2 free_self:  [1: region自身を含めて全て解放, 0: region自身は解放しない]


# void onig_region_copy(OnigRegion* to, OnigRegion* from)

  マッチ領域情報(region)を複製する。

  引数
  1 to:   対象領域
  2 from: 元領域


# void onig_region_clear(OnigRegion* region)

  マッチ領域情報(region)の中味をクリアする。

  引数
  1 region: 対象領域


# int onig_region_resize(OnigRegion* region, int n)

  マッチ領域情報(region)の捕獲式集合(グループ)数を変更する。

  戻り値
  正常終了: ONIG_NORMAL == 0
  エラー時: エラーコード < 0

  引数
  1 region: 対象領域
  2 n:      新しいサイズ


# int onig_name_to_group_numbers(regex_t* reg, const UChar* name, const UChar* name_end,
                                  int** num_list)

  指定した名前に対する名前付き捕獲式集合(グループ)の
  グループ番号リストを返す。
  名前付き捕獲式集合は、(?<name>....)によって定義できる。

  戻り値
  正常終了: 指定された名前に対するグループ数
            (例 /(?<x>..)(?<x>..)/  ==>  2)
  名前に対するグループが存在しない: ONIGERR_UNDEFINED_NAME_REFERENCE

  引数
  1 reg:       正規表現オブジェクト
  2 name:      捕獲式集合(グループ)名
  3 name_end:  捕獲式集合(グループ)名の終端アドレス
  4 num_list:  番号リストを返すアドレス


# int onig_name_to_backref_number(regex_t* reg, const UChar* name, const UChar* name_end,
                                  OnigRegion *region)

  指定された名前の後方参照(\k<name>)に対する捕獲式集合(グループ)の番号を返す。
  名前に対して、複数のマッチ領域が有効であれば、その中の最大の番号を返す。
  名前に対する捕獲式集合が一個しかないときには、対応するマッチ領域が有効か
  どうかに関係なく、その番号を返す。(従って、regionにはNULLを渡してもよい。)

  戻り値
  正常終了: 番号
  エラー時: エラーコード < 0

  引数
  1 reg:       正規表現オブジェクト
  2 name:      捕獲式集合(グループ)名
  3 name_end:  捕獲式集合(グループ)名の終端アドレス
  4 region:    search/match結果のマッチ領域


# int onig_foreach_name(regex_t* reg,
          int (*func)(const UChar*, const UChar*, int,int*,regex_t*,void*),
          void* arg)

  全ての名前に対してコールバック関数呼び出しを実行する。

  戻り値
  正常終了: ONIG_NORMAL == 0
  エラー時: コールバック関数の戻り値

  引数
  1 reg:     正規表現オブジェクト
  2 func:    コールバック関数
             func(name, name_end, <number of groups>, <group number's list>,
                  reg, arg);

             funcが0以外の値を返すと、それ以降のコールバックは行なわずに
             終了する。

  3 arg:     funcに対する追加引数


# int onig_number_of_names(regex_t* reg)

  パターン中で定義された名前の数を返す。
  一個の名前の多重定義は一個と看做す。

  引数
  1 reg:    正規表現オブジェクト


# OnigEncoding     onig_get_encoding(regex_t* reg)
# OnigOptionType   onig_get_options(regex_t* reg)
# OnigSyntaxType*  onig_get_syntax(regex_t* reg)

  正規表現オブジェクトに対して、対応する値を返す。

  引数
  1 reg:    正規表現オブジェクト


# OnigCaseFoldType onig_get_case_fold_flag(regex_t* reg)

  正規表現オブジェクトに対して、case_fold_flag値を返す。
  この関数は廃止予定(非推奨)。

  引数
  1 reg:    正規表現オブジェクト


# int onig_number_of_captures(regex_t* reg)

  パターン中で定義された捕獲グループの数を返す。

  引数
  1 reg:    正規表現オブジェクト


# OnigCallbackEachMatchFunc onig_get_callback_each_match(void)

  ONIG_OPTION_CALLBACK_EACH_MATCHに対する現在のコールバック関数を返す。


# int onig_set_callback_each_match(OnigCallbackEachMatchFunc func)

  ONIG_OPTION_CALLBACK_EACH_MATCHに対するコールバック関数をセットする。
  若しNULLがセットされると、コールバックは実行されない。

  戻り値
  正常終了: ONIG_NORMAL == 0

  引数
  1 func: コールバック関数


# int onig_number_of_capture_histories(regex_t* reg)

  パターン中で定義された捕獲履歴(?@...)の数を返す。

  使用する文法で捕獲履歴機能が有効(ONIG_SYN_OP2_ATMARK_CAPTURE_HISTORY)
  でなければ、捕獲履歴機能は使用できない。

  引数
  1 reg:    正規表現オブジェクト


# OnigCaptureTreeNode* onig_get_capture_tree(OnigRegion* region)

  捕獲履歴データのルートノードを返す。

  マッチが失敗している場合には、この値は不定である。

  引数
  1 region: マッチ領域


# int onig_capture_tree_traverse(OnigRegion* region, int at,
                  int(*func)(int,int,int,int,int,void*), void* arg)

  捕獲履歴データ木を巡回してコールバックする。

  戻り値
  正常終了: ONIG_NORMAL == 0
  エラー時: コールバック関数の戻り値

  引数
  1 region:  マッチ領域
  2 at:      コールバックを行なうタイミング

    ONIG_TRAVERSE_CALLBACK_AT_FIRST:
        最初にコールバックして、子ノードを巡回
    ONIG_TRAVERSE_CALLBACK_AT_LAST:
        子ノードを巡回して、コールバック
    ONIG_TRAVERSE_CALLBACK_AT_BOTH:
        最初にコールバックして、子ノードを巡回、最後にもう一度コールバック

  3 func:    コールバック関数
             funcが0以外の値を返すと、それ以降の巡回は行なわずに
             終了する。

             int func(int group, int beg, int end, int level, int at,
                      void* arg)
               group: グループ番号
               beg:   マッチ開始位置
               end    マッチ終了位置
               level: ネストレベル (0から)
               at:    コールバックが呼び出されたタイミング
                      ONIG_TRAVERSE_CALLBACK_AT_FIRST
                      ONIG_TRAVERSE_CALLBACK_AT_LAST
               arg:   追加引数

  4 arg;     funcに対する追加引数


# int onig_noname_group_capture_is_active(regex_t* reg)

  名前なし式集合の捕獲機能が有効かどうかを返す。

  戻り値
  有効: 1
  無効: 0

  引数
  1 reg:    正規表現オブジェクト


  オプションのONIG_OPTION_DONT_CAPTURE_GROUPがON --> 無効

  パターンが名前つき式集合を使用している
  AND 使用文法で、ONIG_SYN_CAPTURE_ONLY_NAMED_GROUPがON
  AND オプションのONIG_OPTION_CAPTURE_GROUPがOFF
  --> 無効

  上記以外の場合 --> 有効


# UChar* onigenc_get_prev_char_head(OnigEncoding enc, const UChar* start, const UChar* s)

  文字一個分前の文字列位置を返す。

  引数
  1 enc:   文字エンコーディング
  2 start: 文字列の先頭アドレス
  3 s:     文字列中の位置


# UChar* onigenc_get_left_adjust_char_head(OnigEncoding enc,
                                           const UChar* start, const UChar* s)

  文字の先頭バイト位置になるように左側に調整したアドレスを返す。

  引数
  1 enc:   文字エンコーディング
  2 start: 文字列の先頭アドレス
  3 s:     文字列中の位置


# UChar* onigenc_get_right_adjust_char_head(OnigEncoding enc,
                                            const UChar* start, const UChar* s)

  文字の先頭バイト位置になるように右側に調整したアドレスを返す。

  引数
  1 enc:   文字エンコーディング
  2 start: 文字列の先頭アドレス
  3 s:     文字列中の位置


# int onigenc_strlen(OnigEncoding enc, const UChar* s, const UChar* end)

  文字列の文字数を返す。


# int onigenc_strlen_null(OnigEncoding enc, const UChar* s)

  文字列の文字数を返す。
  文字エンコーディングに対して、不正な文字列を渡してはいけない。


# int onigenc_str_bytelen_null(OnigEncoding enc, const UChar* s)

  文字列のバイト数を返す。
  文字エンコーディングに対して、不正な文字列を渡してはいけない。


# int onig_set_default_syntax(OnigSyntaxType* syntax)

  デフォルトの正規表現パターン文法をセットする。

  引数
  1 syntax: 正規表現パターン文法


# void onig_copy_syntax(OnigSyntaxType* to, OnigSyntaxType* from)

  正規表現パターン文法をコピーする。

  引数
  1 to:   対象
  2 from: 元


# unsigned int onig_get_syntax_op(OnigSyntaxType* syntax)
# unsigned int onig_get_syntax_op2(OnigSyntaxType* syntax)
# unsigned int onig_get_syntax_behavior(OnigSyntaxType* syntax)
# OnigOptionType onig_get_syntax_options(OnigSyntaxType* syntax)

# void onig_set_syntax_op(OnigSyntaxType* syntax, unsigned int op)
# void onig_set_syntax_op2(OnigSyntaxType* syntax, unsigned int op2)
# void onig_set_syntax_behavior(OnigSyntaxType* syntax, unsigned int behavior)
# void onig_set_syntax_options(OnigSyntaxType* syntax, OnigOptionType options)

  正規表現パターン文法の要素を参照/取得する。

  引数
  1 syntax:                     正規表現パターン文法
  2 op, op2, behavior, options: 要素の値


# void onig_copy_encoding(OnigEncoding to, OnigEncoding from)

  文字エンコーディングをコピーする。

  引数
  1 to:   対象
  2 from: 元


# int onig_set_meta_char(OnigSyntaxType* syntax, unsigned int what,
                         OnigCodePoint code)

  メタ文字を指定したコードポイント値にセットする。
  ONIG_SYN_OP_VARIABLE_META_CHARACTERSが正規表現パターン文法で有効に
  なっていない場合には、エスケープ文字を除いて、ここで指定したメタ文字は
  機能しない。(組込みの文法では有効にしていない。)

  正常終了戻り値: ONIG_NORMAL

  引数
  1 syntax: 対象文法
  2 what:   メタ文字機能の指定

          ONIG_META_CHAR_ESCAPE
          ONIG_META_CHAR_ANYCHAR
          ONIG_META_CHAR_ANYTIME
          ONIG_META_CHAR_ZERO_OR_ONE_TIME
          ONIG_META_CHAR_ONE_OR_MORE_TIME
          ONIG_META_CHAR_ANYCHAR_ANYTIME

  3 code: メタ文字のコードポイント または ONIG_INEFFECTIVE_META_CHAR.


# OnigCaseFoldType onig_get_default_case_fold_flag()

  デフォルトのcase foldフラグを取得する。
  この関数は廃止予定(非推奨)。


# int onig_set_default_case_fold_flag(OnigCaseFoldType case_fold_flag)

  デフォルトのcase foldフラグをセットする。
  この関数は廃止予定(非推奨)。

  引数
  1 case_fold_flag: case foldフラグ


# unsigned int onig_get_match_stack_limit_size(void)

  マッチスタックサイズの最大値を返す。
  (デフォルト: 0 == 無制限)


# int onig_set_match_stack_limit_size(unsigned int size)

  マッチスタックサイズの最大値を指定する。
  (size = 0: 無制限)

  正常終了戻り値: ONIG_NORMAL


# unsigned long onig_get_retry_limit_in_match(void)

  一回のマッチングでのリトライ数の制限値を返す。
  (デフォルト: 10000000)

  正常終了戻り値: 制限値


# unsigned long onig_get_retry_limit_in_search(void)

  一回の検索でのリトライ数の制限値を返す。
  0は無制限を意味する。
  (デフォルト: 0)

  正常終了戻り値: 制限値


# int onig_set_retry_limit_in_match(unsigned long limit)

  一回のマッチング内でのリトライ数の制限値を指定する。
  ０は無制限を意味する。

  正常終了戻り値: ONIG_NORMAL


# int onig_set_retry_limit_in_search(unsigned long limit)

  一回の検索でのリトライ数の制限値をセットする。
  0は無制限を意味する。
  (デフォルト: 0)

  正常終了戻り値: ONIG_NORMAL


# unsigned long onig_get_subexp_call_limit_in_search(void)

  部分式呼出しの呼び出し回数の制限値を返す。
  (デフォルト: 0:無制限)

  正常終了戻り値: 制限値


# int onig_set_subexp_call_limit_in_search(unsigned long n)

  部分式呼出しの呼び出し回数の制限値を指定する。

  正常終了戻り値: ONIG_NORMAL


# int onig_get_subexp_call_max_nest_level(void)

  部分式呼出しのネストレベルの最大値を返す。
  (デフォルト: 24)

  正常終了戻り値: 制限値


# int onig_set_subexp_call_max_nest_level(int max_level)

  部分式呼出しのネストレベルの最大値を指定する。

  正常終了戻り値: ONIG_NORMAL


# OnigCalloutFunc onig_get_progress_callout(void)

  前進時の内容の呼び出しで呼び出される関数を返す。


# int onig_set_progress_callout(OnigCalloutFunc f)

  前進時の内容の呼び出しで呼び出される関数を指定する。
  もし0(NULL)を指定すると、前進時の内容の呼び出しで呼び出しは起こらない。

  正常終了戻り値: ONIG_NORMAL


# OnigCalloutFunc onig_get_retraction_callout(void)

  後退時の内容の呼び出しで呼び出される関数を返す。


# int onig_set_retraction_callout(OnigCalloutFunc f)

  後退時の内容の呼び出しで呼び出される関数を指定する。
  もし0(NULL)を指定すると、後退時の内容の呼び出しで呼び出しは起こらない。

  正常終了戻り値: ONIG_NORMAL


# int onig_unicode_define_user_property(const char* name, OnigCodePoint* ranges))

  新しいUnicodeプロパティを定義する。
  (この関数はスレッドセーフではない)

  引数
  1 name:    プロパティ名 (ASCIIコードのみ。 文字 ' ', '-', '_' は無視される。)
  2 ranges:  プロパティコードポイント範囲
             (最初の要素は範囲の数)

    [num-of-ranges, 1st-range-start, 1st-range-end, 2nd-range-start... ]

    * この関数を呼んだ後で、rangesを変更/破壊しないこと

  戻り値
  正常終了: ONIG_NORMAL == 0
  エラー時: エラーコード < 0


# unsigned int onig_get_parse_depth_limit(void)

  再帰パース処理の最大深さを返す。
  (デフォルト: regint.h で定義されている DEFAULT_PARSE_DEPTH_LIMIT。現在は 4096)


# int onig_set_parse_depth_limit(unsigned int depth)

  再帰パース処理の最大深さを指定する。
  (depth = 0: regint.h で定義されたデフォルト値に設定する。)

  正常終了戻り値: ONIG_NORMAL


# int onig_end(void)

  ライブラリの使用を終了する。

  正常終了戻り値: ONIG_NORMAL

  onig_initialize()を再度呼び出しても、以前に作成した正規表現オブジェクト
  を使用することはできない。


# const char* onig_version(void)

  バージョン文字列を返す。(例 "5.0.3")

// END
