Callouts API  Version 6.9.7 2021/03/21

#include <oniguruma.h>

(1) Callout functions
(2) Set/Get functions for Callouts of contents
(3) Set functions for Callouts of name
(4) User data
(5) Get values from OnigCalloutArgs
(6) Tag
(7) Callout data (used in callout functions)
(8) Callout data (used in applications)
(9) Miscellaneous functions


(1) Callout functions

  type: OnigCalloutFunc

  typedef int (*OnigCalloutFunc)(OnigCalloutArgs* args, void* user_data);

  If 0 (NULL) is set as a callout function value, never called.


  * Callout function return value (int)

    ONIG_CALLOUT_FAIL (== 1):     fail
    ONIG_CALLOUT_SUCCESS (== 0):  success
    less than -1:                 error code (terminate search/match)

    ONIG_CALLOUT_FAIL/SUCCESS values are ignored in retractions,
    because retraction is a part of recovery process after failure.

  * Example of callout function

    extern int always_success(OnigCalloutArgs* args, void* user_data)
    {
      return ONIG_CALLOUT_SUCCESS;
    }



(2) Set/Get functions for Callouts of contents

# OnigCalloutFunc onig_get_progress_callout(void)

  Get a function for callouts of contents in progress.


# int onig_set_progress_callout(OnigCalloutFunc f)

  Set a function for callouts of contents in progress.
  This value set in onig_initialize_match_param() as a default
  callout function.

  normal return: ONIG_NORMAL


# OnigCalloutFunc onig_get_retraction_callout(void)

  Get a function for callouts of contents in retraction (backtrack).


# int onig_set_retraction_callout(OnigCalloutFunc f)

  Set a function for callouts of contents in retraction (backtrack).
  This value set in onig_initialize_match_param() as a default
  callout function.

  normal return: ONIG_NORMAL


# int onig_set_progress_callout_of_match_param(OnigMatchParam* mp, OnigCalloutFunc f)

  Set a function for callouts of contents in progress.

  arguments
  1 mp: match-param pointer
  2 f: function

  normal return: ONIG_NORMAL


# int onig_set_retraction_callout_of_match_param(OnigMatchParam* mp, OnigCalloutFunc f)

  Set a function for callouts of contents in retraction (backtrack).

  arguments
  1 mp: match-param pointer
  2 f: function

  normal return: ONIG_NORMAL



(3) Set functions for Callouts of name

# int onig_set_callout_of_name(OnigEncoding enc, OnigCalloutType type, OnigUChar* name, OnigUChar* name_end, int callout_in, OnigCalloutFunc callout, OnigCalloutFunc end_callout, int arg_num, unsigned int arg_types[], int opt_arg_num, OnigValue opt_defaults[])

  Set a function for callouts of name.
  Allowed name string characters: _ A-Z a-z 0-9   (* first character: _ A-Z a-z)

  (enc, name) pair is used as key value to find callout function.
  You have to call this function for every encoding used in your applications.
  But if enc is ASCII compatible and (enc, name) entry is not found,
  then (ASCII, name) entry is used.
  Therefore, if you use ASCII compatible encodings only, it is enough to call
  this function one time for (ASCII, name).

  arguments
   1 enc:         character encoding
   2 type:        callout type (currently ONIG_CALLOUT_TYPE_SINGLE only supported)
   3 name:        name string address (the string is encoded by enc)
   4 name_end:    name string end address
   5 callout_in:  direction (ONIG_CALLOUT_IN_PROGRESS/RETRACTION/BOTH)
   6 callout:     callout function
   7 end_callout: * not used currently (set 0)
   8 arg_num:     number of arguments (*limit by ONIG_CALLOUT_MAX_ARGS_NUM == 4)
   9 arg_types:   type array of arguments
  10 opt_arg_num: number of optional arguments
  11 opt_defaults: default values array of optional arguments

  normal return: ONIG_NORMAL
  error:
    ONIGERR_INVALID_CALLOUT_NAME
    ONIGERR_INVALID_ARGUMENT
    ONIGERR_INVALID_CALLOUT_ARG



(4) User data

# int onig_set_callout_user_data_of_match_param(OnigMatchParam* param, void* user_data)

  Set a user_data value which passed as second argument of callout.

  normal return: ONIG_NORMAL



(5) Get values from OnigCalloutArgs

# int onig_get_callout_num_by_callout_args(OnigCalloutArgs* args)

  Returns callout number of this callout.
  "Callout number" is an identifier of callout in a regex pattern.


# OnigCalloutIn onig_get_callout_in_by_callout_args(OnigCalloutArgs* args)

  Returns the direction of this callout.
  (ONIG_CALLOUT_IN_PROGRESS or ONIG_CALLOUT_IN_RETRACTION)


# int onig_get_name_id_by_callout_args(OnigCalloutArgs* args)

  Returns the name identifier of this callout.
  If this callout is callout of contents, then returns ONIG_NON_NAME_ID.


# const OnigUChar* onig_get_contents_by_callout_args(OnigCalloutArgs* args)

  Returns the contents string of this callout. (NULL terminated string)
  If this callout is callout of name, then returns NULL.


# const OnigUChar* onig_get_contents_end_by_callout_args(OnigCalloutArgs* args)

  Returns the end of contents string of this callout.
  If this callout is callout of name, then returns NULL.


# int onig_get_args_num_by_callout_args(OnigCalloutArgs* args)

  Returns the number of args of this callout.
  It includes optional arguments that doesn't passed in regex pattern.
  If this callout is callout of contents, then returns
  ONIGERR_INVALID_ARGUMENT.


# int onig_get_passed_args_num_by_callout_args(OnigCalloutArgs* args)

  Returns the number of args that passed really in regex pattern.
  If this callout is callout of contents, then returns
  ONIGERR_INVALID_ARGUMENT.


# int onig_get_arg_by_callout_args(OnigCalloutArgs* args, int index, OnigType* type, OnigValue* val)

  Returns a value and a type of the callout argument.
  If this callout is callout of contents, then returns
  ONIGERR_INVALID_ARGUMENT.

  normal return: ONIG_NORMAL


# const OnigUChar* onig_get_string_by_callout_args(OnigCalloutArgs* args)

  Returns the subject string address.
  This is the second argument(str) of onig_search().


# const OnigUChar* onig_get_string_end_by_callout_args(OnigCalloutArgs* args)

  Returns the end address of subject string.
  This is the third argument(end) of onig_search().


# const OnigUChar* onig_get_start_by_callout_args(OnigCalloutArgs* args)

  Returns the start address of subject string in current match process.


# const OnigUChar* onig_get_right_range_by_callout_args(OnigCalloutArgs* args)

  Returns the right range address of subject string.


# const OnigUChar* onig_get_current_by_callout_args(OnigCalloutArgs* args)

  Returns the current address of subject string in current match process.


# OnigRegex onig_get_regex_by_callout_args(OnigCalloutArgs* args)

  Returns the regex object address of this callout.


# unsigned long onig_get_retry_counter_by_callout_args(OnigCalloutArgs* args)

  Returns the current counter value for retry-limit-in-match.



(6) Tag

  "Tag" is a name assigned to a callout in regexp pattern.
  Allowed tag string characters: _ A-Z a-z 0-9   (* first character: _ A-Z a-z)


# int onig_callout_tag_is_exist_at_callout_num(OnigRegex reg, int callout_num)

  Returns 1 if tag is assigned for the callout, else returns 0.


# int onig_get_callout_num_by_tag(OnigRegex reg, const OnigUChar* tag, const OnigUChar* tag_end)

  Returns the callout number for the tag.


# const OnigUChar* onig_get_callout_tag_start(OnigRegex reg, int callout_num)

  Returns the start address of tag string for the callout.
  (NULL terminated string)


# const OnigUChar* onig_get_callout_tag_end(OnigRegex reg, int callout_num)

  Returns the end address of tag string for the callout.



(7) Callout data (used in callout functions)

  "Callout data" is ONIG_CALLOUT_DATA_SLOT_NUM(5) values area
  for each callout in each search process.
  Each value area in a callout is indicated by "slot" number (0 - 4).
  Callout data are used for any purpose by callout function implementers.


# int onig_get_callout_data_by_callout_args(OnigCalloutArgs* args, int callout_num, int slot, OnigType* type, OnigValue* val)

  Returns the callout data value/type for a callout slot indicated by
  callout_num/slot.

  ONIG_NORMAL:           normal return
  ONIG_VALUE_IS_NOT_SET: value is not set / type is ONIG_TYPE_VOID
  < 0: error code


# int onig_get_callout_data_by_callout_args_self(OnigCalloutArgs* args, int slot, OnigType* type, OnigValue* val)

  Returns self callout data value/type.

  ONIG_NORMAL:           normal return
  ONIG_VALUE_IS_NOT_SET: value is not set / type is ONIG_TYPE_VOID
  < 0: error code


# int onig_set_callout_data_by_callout_args(OnigCalloutArgs* args, int callout_num, int slot, OnigType type, OnigValue* val)

  Set the callout data value/type for a callout slot indicated by callout_num/slot.

  ONIG_NORMAL: normal return
          < 0: error code


# int onig_set_callout_data_by_callout_args_self(OnigCalloutArgs* args, int slot, OnigType type, OnigValue* val)

  Set self callout data value/type for a callout slot indicated by slot.

  ONIG_NORMAL: normal return
          < 0: error code


# int onig_get_callout_data_by_callout_args_self_dont_clear_old(OnigCalloutArgs* args, int slot, OnigType* type, OnigValue* val)

  This function is almost same as onig_get_callout_data_by_callout_args_self().
  But this function does not clear the value set in the collation position before the current position. (dont_clear_old)
  The other onig_get_callout_data_xxxx() function clears the value set in the collation process of the previous position.

  For example, Builtin callout (*TOTAL_COUNT) is implemented by using this
  function for accumulate count of all of match processes in a search process.
  Builtin callout (*COUNT) returns count in last success match process only,
  because it doesn't use this function.

  ONIG_NORMAL:           normal return
  ONIG_VALUE_IS_NOT_SET: value is not set / type is ONIG_TYPE_VOID
  < 0: error code


(8) Callout data (used in applications)

# int onig_get_callout_data(OnigRegex reg, OnigMatchParam* mp, int callout_num, int slot, OnigType* type, OnigValue* val)

  Returns the callout data value/type for a callout slot indicated by
  callout_num/slot.

  ONIG_NORMAL:           normal return
  ONIG_VALUE_IS_NOT_SET: value is not set / type is ONIG_TYPE_VOID
  < 0: error code


# int onig_get_callout_data_by_tag(OnigRegex reg, OnigMatchParam* mp, const OnigUChar* tag, const OnigUChar* tag_end, int slot, OnigType* type, OnigValue* val)

  Returns the callout data value/type for a callout slot indicated by tag/slot.

  ONIG_NORMAL:           normal return
  ONIG_VALUE_IS_NOT_SET: value is not set / type is ONIG_TYPE_VOID
  < 0: error code


# int onig_set_callout_data(OnigRegex reg, OnigMatchParam* mp, int callout_num, int slot, OnigType type, OnigValue* val)

  Set the callout data value/type for a callout slot indicated by callout_num/slot.

  ONIG_NORMAL: normal return
          < 0: error code


# int onig_set_callout_data_by_tag(OnigRegex reg, OnigMatchParam* mp, const OnigUChar* tag, const OnigUChar* tag_end, int slot, OnigType type, OnigValue* val)

  Set the callout data value/type for a callout slot indicated by tag/slot.

  ONIG_NORMAL: normal return
          < 0: error code


# int onig_get_callout_data_dont_clear_old(OnigRegex reg, OnigMatchParam* mp, int callout_num, int slot, OnigType* type, OnigValue* val)

  No needs to use this function.
  It will be abolished.


# int onig_get_callout_data_by_tag_dont_clear_old(regex_t* reg, OnigMatchParam* mp, const UChar* tag, const UChar* tag_end, int slot, OnigType* type, OnigValue* val)

  Returns the callout data value/type for a callout slot indicated by tag/slot.
  This function does not clear the value set in the collation position before the current position. (dont_clear_old)

  ONIG_NORMAL:           正常終了
  ONIG_VALUE_IS_NOT_SET: 値が未セット / 型がVOID
  < 0: エラーコード


(9) Miscellaneous functions

# OnigUChar* onig_get_callout_name_by_name_id(int name_id)

  Returns callout name of the name id.
  if invalid name id is passed, return 0.


# int onig_get_capture_range_in_callout(OnigCalloutArgs* args, int mem_num, int* begin, int* end)

  Returns current capture range position.
  Position is byte length offset from subject string.
  For uncaptured mem_num, ONIG_REGION_NOTPOS is set.


# int onig_get_used_stack_size_in_callout(OnigCalloutArgs* args, int* used_num, int* used_bytes)

  Returns current used match-stack size.

  used_num:   number of match-stack elements
  used_bytes: used byte size of match-stack

//END
