/**********************************************************************
  regtrav.c -  <PERSON>ig<PERSON><PERSON> (regular expression library)
**********************************************************************/
/*-
 * Copyright (c) 2002-2019  K.Kosako
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR AND CONTRIBUTORS ``AS IS'' AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR OR CONTRIBUTORS BE LIABLE
 * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 */

#include "regint.h"

#ifdef USE_CAPTURE_HISTORY

static int
capture_tree_traverse(OnigCaptureTreeNode* node, int at,
                      int(*callback_func)(int,int,int,int,int,void*),
                      int level, void* arg)
{
  int r, i;

  if (node == (OnigCaptureTreeNode* )0)
    return 0;

  if ((at & ONIG_TRAVERSE_CALLBACK_AT_FIRST) != 0) {
    r = (*callback_func)(node->group, node->beg, node->end,
                         level, ONIG_TRAVERSE_CALLBACK_AT_FIRST, arg);
    if (r != 0) return r;
  }

  for (i = 0; i < node->num_childs; i++) {
    r = capture_tree_traverse(node->childs[i], at,
                              callback_func, level + 1, arg);
    if (r != 0) return r;
  }

  if ((at & ONIG_TRAVERSE_CALLBACK_AT_LAST) != 0) {
    r = (*callback_func)(node->group, node->beg, node->end,
                         level, ONIG_TRAVERSE_CALLBACK_AT_LAST, arg);
    if (r != 0) return r;
  }

  return 0;
}
#endif /* USE_CAPTURE_HISTORY */

extern int
onig_capture_tree_traverse(OnigRegion* region, int at,
                  int(*callback_func)(int,int,int,int,int,void*), void* arg)
{
#ifdef USE_CAPTURE_HISTORY
  return capture_tree_traverse(region->history_root, at,
                               callback_func, 0, arg);
#else
  return ONIG_NO_SUPPORT_CONFIG;
#endif
}
