/* This file was converted by gperf_unfold_key_conv.py
      from gperf output file. */
/* ANSI-C code produced by gperf version 3.1 */
/* Command-line: gperf -n -C -T -c -t -j1 -L ANSI-C -F,-1,0 -N onigenc_unicode_unfold_key unicode_unfold_key.gperf  */
/* Computed positions: -k'1-3' */



/* This gperf source file was generated by make_unicode_fold_data.py */

/*-
 * Copyright (c) 2017-2024  K.Kosako
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR AND CONTRIBUTORS ``AS IS'' AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR OR CONTRIBUTORS BE LIABLE
 * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 */
#include "regint.h"

#define TOTAL_KEYWORDS 1557
#define MIN_WORD_LENGTH 3
#define MAX_WORD_LENGTH 3
#define MIN_HASH_VALUE 8
#define MAX_HASH_VALUE 2248
/* maximum key range = 2241, duplicates = 0 */

#ifdef __GNUC__
__inline
#else
#ifdef __cplusplus
inline
#endif
#endif
/*ARGSUSED*/
static unsigned int
hash(OnigCodePoint codes[])
{
  static const unsigned short asso_values[] =
    {
         9,    7,    4,   52,  130,    1,  169, 2249, 2249, 2249,
      2249, 2249, 2249,   21,  155, 2249, 2249,   78, 2249, 2249,
       100, 2249, 2249, 2249, 2249,   84, 2249, 2249, 2249,    8,
      2249,    2,    3, 2249,  834,  718, 1614,  141, 1598,  712,
      1570,  694, 1668,  706, 1606,    0, 1593,  680, 1584,  651,
      1579, 1006, 1665, 1000, 1560,  688, 1553,  410, 1539,  645,
      1545,  639, 1530,  633,  269,  988, 1662,  966, 1122,  817,
      1647,  751,  994,  743,  379,  789, 1657,  261,  959,  783,
      1651,  771,  599,  826,  381, 1151,  286, 1521,  206, 1516,
        68, 1511,  915, 1431, 1607, 1640,  697,  671, 1531, 1507,
      1434, 1420, 1367, 1257, 1387, 1243,  717,  920, 1125,  613,
      1363,  331,  178,  909,  774,  843, 1451,  829, 1422,  822,
      1026,  724,  510, 1239,  754, 1222, 1324, 1413, 1072, 1409,
       764,  949,  316,  473,  384,   27,  169, 1405,   63, 1206,
       888, 1197,  879, 1189,  873,  386,  371, 1161,  903, 1139,
       807,  347,  613, 1398,  272,  197, 1633, 1449,  279, 1180,
         1, 1383,  286,  510, 1374,  490,  228,   15,    5,  398,
      1130,  595,    6,  464,  363,  574,  566,  457, 1228,   35,
      1112,  357, 1357,  324, 1330,  240,  158,  220, 1348,  432,
      1340,  548, 1326,  539, 1318,   97, 1106,  127, 1105,   85,
      1098,   76,  902,  211,  500,  189,  848,  118,  760,  106,
      1088,  151,  866,   56,  895,  419,  584,  315,  856,   66,
      1048,  303, 1290,  295, 1275,  274, 1257,  805,  657,  443,
      1312,  531, 1081, 1074,  253, 1065, 1311,  482, 1056,  561,
      1301, 1508, 1433, 1043, 1520, 1285,  929, 1634,  392, 1034,
       183, 1022,   42, 1014,  246, 1275,   26, 1266,   76, 1502,
      2249,  604,   70, 1497, 2249, 1491,   50, 1487,   41, 1478,
       342, 1471,   26, 1251, 2249,  666,   88, 1465,   19, 1460,
        46, 1627,   44, 1621,  207, 1443,   13, 1096,   17,  427,
         9
    };
  return asso_values[(unsigned char)onig_codes_byte_at(codes, 2)+35] + asso_values[(unsigned char)onig_codes_byte_at(codes, 1)+1] + asso_values[(unsigned char)onig_codes_byte_at(codes, 0)];
}

const struct ByUnfoldKey *
onigenc_unicode_unfold_key(OnigCodePoint code)
{
  static const struct ByUnfoldKey wordlist[] =
    {
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x1040a, 3480, 1},

      {0x2c0a, 2615, 1},

      {0x040a, 1035, 1},

      {0x1e0a, 1891, 1},

      {0x1f0a, 2252, 1},

      {0x010a, 186, 1},

      {0x017d, 351, 1},

      {0xa77d, 1867, 1},

      {0xab7d, 1645, 1},

      {0x1f85, 154, 2},

      {0x1f89, 134, 2},

      {0x0189, 622, 1},

      {0xab85, 1669, 1},

      {0xab89, 1681, 1},

      {0x1c85, 960, 1},

      {0x1c89, 1864, 1},

      {0x2c84, 2759, 1},

      {0x1ffb, 2429, 1},

      {0x1e84, 2075, 1},

      {0x1f84, 149, 2},

      {0x0184, 360, 1},

      {0xa784, 3273, 1},

      {0xab84, 1666, 1},

      {0x1ff3, 96, 2},

      {0x1c84, 960, 1},

      {0x10c85, 3798, 1},

      {0x10c89, 3810, 1},

      {0x2ced, 2906, 1},

      {0x2c64, 682, 1},

      {0x0464, 1060, 1},

      {0x1e64, 2027, 1},

      {0xa684, 3108, 1},

      {0x0164, 315, 1},

      {0xa764, 3240, 1},

      {0x00dd, 162, 1},

      {0x10c84, 3795, 1},

      {0x2c90, 2777, 1},

      {0x0490, 1114, 1},

      {0x1e90, 2093, 1},

      {0x1f90, 169, 2},

      {0x0190, 631, 1},

      {0xa790, 3282, 1},

      {0xab90, 1702, 1},

      {0xa664, 3087, 1},

      {0x1c90, 1468, 1},

      {0x1fe9, 2447, 1},

      {0x1fd9, 2441, 1},

      {0x01d9, 450, 1},

      {0x1ff7, 67, 3},

      {0x01f7, 417, 1},

      {0x00d9, 150, 1},

      {0xa690, 3126, 1},

      {0xa7f5, 3366, 1},

      {0x020a, 520, 1},

      {0x1fe7, 47, 3},

      {0x10c90, 3831, 1},

      {0x104b2, 3576, 1},

      {0x2cb2, 2828, 1},

      {0x04b2, 1165, 1},

      {0x1eb2, 2129, 1},

      {0x1fb2, 249, 2},

      {0x01b2, 706, 1},

      {0xa7b2, 715, 1},

      {0xabb2, 1804, 1},

      {0x2c67, 2738, 1},

      {0x1cb2, 1570, 1},

      {0x104b8, 3594, 1},

      {0x2cb8, 2837, 1},

      {0x04b8, 1174, 1},

      {0x1eb8, 2138, 1},

      {0x1fb8, 2432, 1},

      {0x01b8, 411, 1},

      {0xa7b8, 3327, 1},

      {0xabb8, 1822, 1},

      {0x1fe3, 41, 3},

      {0x1cb8, 1588, 1},

      {0x10cb2, 3933, 1},

      {0x2ca6, 2810, 1},

      {0x04a6, 1147, 1},

      {0x1ea6, 2111, 1},

      {0x1fa6, 239, 2},

      {0x01a6, 685, 1},

      {0xa7a6, 3315, 1},

      {0xaba6, 1768, 1},

      {0x00df, 24, 2},

      {0x1ca6, 1534, 1},

      {0x2ca4, 2807, 1},

      {0x04a4, 1144, 1},

      {0x1ea4, 2108, 1},

      {0x1fa4, 229, 2},

      {0x01a4, 393, 1},

      {0xa7a4, 3312, 1},

      {0xaba4, 1762, 1},

      {0x01f1, 486, 1},

      {0x1ca4, 1528, 1},

      {0xff37, 3438, 1},

      {0x10ca6, 3897, 1},
      {0xffffffff, -1, 0},

      {0x2ca0, 2801, 1},

      {0x04a0, 1138, 1},

      {0x1ea0, 2102, 1},

      {0x1fa0, 209, 2},

      {0x01a0, 387, 1},

      {0xa7a0, 3306, 1},

      {0xaba0, 1750, 1},

      {0x10ca4, 3891, 1},

      {0x1ca0, 1516, 1},

      {0x2cae, 2822, 1},

      {0x04ae, 1159, 1},

      {0x1eae, 2123, 1},

      {0x1fae, 239, 2},

      {0x01ae, 697, 1},

      {0xa7ae, 661, 1},

      {0xabae, 1792, 1},

      {0x13fb, 1855, 1},

      {0x1cae, 1558, 1},
      {0xffffffff, -1, 0},

      {0x10ca0, 3879, 1},

      {0x13fd, 1861, 1},

      {0x2cac, 2819, 1},

      {0x04ac, 1156, 1},

      {0x1eac, 2120, 1},

      {0x1fac, 229, 2},

      {0x01ac, 399, 1},

      {0xa7ac, 640, 1},

      {0xabac, 1786, 1},

      {0x10cae, 3921, 1},

      {0x1cac, 1552, 1},

      {0x2ca2, 2804, 1},

      {0x04a2, 1141, 1},

      {0x1ea2, 2105, 1},

      {0x1fa2, 219, 2},

      {0x01a2, 390, 1},

      {0xa7a2, 3309, 1},

      {0xaba2, 1756, 1},

      {0x10b2, 2966, 1},

      {0x1ca2, 1522, 1},

      {0x0389, 745, 1},

      {0x10cac, 3915, 1},

      {0x118b2, 4056, 1},

      {0x03ff, 736, 1},

      {0x10402, 3456, 1},

      {0x2c02, 2591, 1},

      {0x0402, 1011, 1},

      {0x1e02, 1879, 1},

      {0x10b8, 2984, 1},

      {0x0102, 174, 1},

      {0x10ca2, 3885, 1},

      {0x03fd, 730, 1},

      {0x118b8, 4074, 1},
      {0xffffffff, -1, 0},

      {0x104b0, 3570, 1},

      {0x2cb0, 2825, 1},

      {0x04b0, 1162, 1},

      {0x1eb0, 2126, 1},

      {0x10a6, 2930, 1},
      {0xffffffff, -1, 0},

      {0xa7b0, 718, 1},

      {0xabb0, 1798, 1},

      {0x118a6, 4020, 1},

      {0x1cb0, 1564, 1},

      {0x1e97, 34, 2},

      {0x1f97, 204, 2},

      {0x0197, 655, 1},

      {0x10a4, 2924, 1},

      {0xab97, 1723, 1},

      {0x0390, 25, 3},

      {0x1c97, 1489, 1},

      {0x118a4, 4014, 1},

      {0x1057d, 3714, 1},

      {0x050a, 1297, 1},

      {0x10cb0, 3927, 1},
      {0xffffffff, -1, 0},

      {0x10585, 3738, 1},

      {0x10589, 3750, 1},

      {0x03f7, 896, 1},

      {0x10a0, 2912, 1},

      {0x03f5, 764, 1},

      {0x10c97, 3852, 1},
      {0xffffffff, -1, 0},

      {0x118a0, 4002, 1},

      {0x10d64, 3996, 1},

      {0x1f4d, 2351, 1},

      {0x10584, 3735, 1},

      {0xfb02, 12, 2},

      {0x10ae, 2954, 1},

      {0x004d, 34, 1},

      {0x1fd7, 31, 3},

      {0x01d7, 447, 1},

      {0x118ae, 4044, 1},

      {0x2caa, 2816, 1},

      {0x04aa, 1153, 1},

      {0x1eaa, 2117, 1},

      {0x1faa, 219, 2},

      {0x0202, 508, 1},

      {0xa7aa, 652, 1},

      {0xabaa, 1780, 1},

      {0x10ac, 2948, 1},

      {0x1caa, 1546, 1},

      {0x0478, 1090, 1},

      {0x1e78, 2057, 1},

      {0x118ac, 4038, 1},

      {0x0178, 168, 1},

      {0x10590, 3768, 1},

      {0xab78, 1630, 1},
      {0xffffffff, -1, 0},

      {0x10a2, 2918, 1},

      {0x03a6, 822, 1},

      {0x24b8, 2513, 1},

      {0x10caa, 3909, 1},

      {0x118a2, 4008, 1},

      {0x1ff9, 2417, 1},

      {0x2ca8, 2813, 1},

      {0x04a8, 1150, 1},

      {0x1ea8, 2114, 1},

      {0x1fa8, 209, 2},

      {0x03a4, 816, 1},

      {0xa7a8, 3318, 1},

      {0xaba8, 1774, 1},

      {0x03f1, 808, 1},

      {0x1ca8, 1540, 1},

      {0x2c98, 2789, 1},

      {0x0498, 1126, 1},

      {0x1e98, 38, 2},

      {0x1f98, 169, 2},

      {0x0198, 375, 1},

      {0xa798, 3294, 1},

      {0xab98, 1726, 1},

      {0x03a0, 804, 1},

      {0x1c98, 1492, 1},

      {0x10b0, 2960, 1},

      {0x10ca8, 3903, 1},

      {0x1f83, 144, 2},

      {0xff35, 3432, 1},

      {0x118b0, 4050, 1},

      {0xab83, 1663, 1},

      {0xa698, 3138, 1},

      {0x1c83, 956, 1},

      {0x0537, 1372, 1},
      {0xffffffff, -1, 0},

      {0x10c98, 3855, 1},

      {0x2c96, 2786, 1},

      {0x0496, 1123, 1},

      {0x1e96, 16, 2},

      {0x1f96, 199, 2},

      {0x0196, 658, 1},

      {0xa796, 3291, 1},

      {0xab96, 1720, 1},

      {0x10c83, 3792, 1},

      {0x1c96, 1486, 1},

      {0x1fdb, 2411, 1},

      {0x01db, 453, 1},

      {0x1e90a, 4224, 1},

      {0x104c7, 3639, 1},

      {0x00db, 156, 1},

      {0x04c7, 1195, 1},

      {0xa696, 3135, 1},

      {0x1fc7, 15, 3},

      {0x01c7, 424, 1},

      {0xa7c7, 3345, 1},

      {0x10c96, 3849, 1},

      {0x00c7, 99, 1},

      {0x2c2a, 2711, 1},

      {0x042a, 986, 1},

      {0x1e2a, 1939, 1},

      {0x1f2a, 2294, 1},

      {0x012a, 234, 1},

      {0xa72a, 3156, 1},

      {0x10aa, 2942, 1},

      {0x1041d, 3537, 1},

      {0x2c1d, 2672, 1},

      {0x041d, 943, 1},

      {0x118aa, 4032, 1},

      {0x1f1d, 2285, 1},

      {0x104be, 3612, 1},

      {0x2cbe, 2846, 1},

      {0x04be, 1183, 1},

      {0x1ebe, 2147, 1},

      {0x1fbe, 779, 1},

      {0xab77, 1627, 1},

      {0xa7be, 3336, 1},

      {0xabbe, 1840, 1},

      {0x03b0, 41, 3},

      {0x1cbe, 1600, 1},

      {0x017b, 348, 1},

      {0xa77b, 3261, 1},

      {0xab7b, 1639, 1},

      {0x2c7f, 586, 1},

      {0xff2a, 3399, 1},

      {0x0397, 771, 1},

      {0x10a8, 2936, 1},

      {0x017f, 52, 1},
      {0xffffffff, -1, 0},

      {0xab7f, 1651, 1},

      {0x118a8, 4026, 1},

      {0x104bc, 3606, 1},

      {0x2cbc, 2843, 1},

      {0x04bc, 1180, 1},

      {0x1ebc, 2144, 1},

      {0x1fbc, 62, 2},

      {0x01bc, 414, 1},

      {0xa7bc, 3333, 1},

      {0xabbc, 1834, 1},

      {0x104ba, 3600, 1},

      {0x2cba, 2840, 1},

      {0x04ba, 1177, 1},

      {0x1eba, 2141, 1},

      {0x1fba, 2390, 1},

      {0x13f9, 1849, 1},

      {0xa7ba, 3330, 1},

      {0xabba, 1828, 1},

      {0x0502, 1285, 1},

      {0x1cba, 1594, 1},

      {0xff33, 3426, 1},

      {0x022a, 565, 1},

      {0x104b6, 3588, 1},

      {0x2cb6, 2834, 1},

      {0x04b6, 1171, 1},

      {0x1eb6, 2135, 1},

      {0x1fb6, 58, 2},

      {0x03aa, 836, 1},

      {0xa7b6, 3324, 1},

      {0xabb6, 1816, 1},

      {0x10d65, 3999, 1},

      {0x1cb6, 1582, 1},

      {0x2c94, 2783, 1},

      {0x0494, 1120, 1},

      {0x1e94, 2099, 1},

      {0x1f94, 189, 2},

      {0x0194, 643, 1},
      {0xffffffff, -1, 0},

      {0xab94, 1714, 1},

      {0x10c7, 3026, 1},

      {0x1c94, 1480, 1},

      {0x1e4c, 1990, 1},

      {0x1f4c, 2348, 1},

      {0x014c, 279, 1},

      {0xa74c, 3204, 1},

      {0x03f9, 890, 1},

      {0x004c, 31, 1},

      {0xa694, 3132, 1},
      {0xffffffff, -1, 0},

      {0x03a8, 829, 1},

      {0x2ceb, 2903, 1},

      {0x10c94, 3843, 1},
      {0xffffffff, -1, 0},

      {0x1feb, 2423, 1},

      {0xa64c, 3051, 1},

      {0x054d, 1438, 1},

      {0x0474, 1084, 1},

      {0x1e74, 2051, 1},

      {0x0398, 774, 1},

      {0x0174, 339, 1},

      {0x10be, 3002, 1},

      {0xab74, 1618, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x118be, 4092, 1},

      {0x2c92, 2780, 1},

      {0x0492, 1117, 1},

      {0x1e92, 2096, 1},

      {0x1f92, 179, 2},
      {0xffffffff, -1, 0},

      {0xa792, 3285, 1},

      {0xab92, 1708, 1},

      {0x10578, 3702, 1},

      {0x1c92, 1474, 1},

      {0x1f8b, 144, 2},

      {0x018b, 366, 1},

      {0xa78b, 3279, 1},

      {0xab8b, 1687, 1},

      {0x0396, 768, 1},

      {0x2c6f, 607, 1},

      {0xa692, 3129, 1},

      {0x10bc, 2996, 1},

      {0x1f6f, 2387, 1},

      {0x0535, 1366, 1},

      {0x10c92, 3837, 1},

      {0x118bc, 4086, 1},

      {0x10427, 3567, 1},

      {0x2c27, 2702, 1},

      {0x0427, 977, 1},

      {0x10ba, 2990, 1},

      {0x10c8b, 3816, 1},

      {0x024c, 601, 1},

      {0x2c63, 1870, 1},

      {0x118ba, 4080, 1},

      {0x2c6e, 673, 1},

      {0x046e, 1075, 1},

      {0x1e6e, 2042, 1},

      {0x1f6e, 2384, 1},

      {0x016e, 330, 1},

      {0xa76e, 3255, 1},

      {0x1e902, 4200, 1},

      {0x10b6, 2978, 1},

      {0x24c7, 2558, 1},

      {0x10583, 3732, 1},

      {0x01d5, 444, 1},

      {0x118b6, 4068, 1},

      {0x2c86, 2762, 1},

      {0x00d5, 141, 1},

      {0x1e86, 2078, 1},

      {0x1f86, 159, 2},

      {0x0186, 619, 1},

      {0xa786, 3276, 1},

      {0xab86, 1672, 1},

      {0xff27, 3390, 1},

      {0x1c86, 986, 1},

      {0xff31, 3420, 1},
      {0xffffffff, -1, 0},

      {0x10416, 3516, 1},

      {0x2c16, 2651, 1},

      {0x0416, 922, 1},

      {0x1e16, 1909, 1},

      {0xa686, 3111, 1},

      {0x0116, 204, 1},

      {0x24be, 2531, 1},

      {0x037f, 893, 1},

      {0x10c86, 3801, 1},

      {0x104b4, 3582, 1},

      {0x2cb4, 2831, 1},

      {0x04b4, 1168, 1},

      {0x1eb4, 2132, 1},

      {0x1fb4, 50, 2},
      {0xffffffff, -1, 0},

      {0xa7b4, 3321, 1},

      {0xabb4, 1810, 1},
      {0xffffffff, -1, 0},

      {0x1cb4, 1576, 1},

      {0x04fe, 1279, 1},

      {0x1efe, 2243, 1},

      {0x052a, 1345, 1},

      {0x01fe, 502, 1},

      {0x2c9a, 2792, 1},

      {0x049a, 1129, 1},

      {0x1e9a, 0, 2},

      {0x1f9a, 179, 2},

      {0x24bc, 2525, 1},

      {0xa79a, 3297, 1},

      {0xab9a, 1732, 1},

      {0x10577, 3699, 1},

      {0x1c9a, 1498, 1},
      {0xffffffff, -1, 0},

      {0x104c2, 3624, 1},

      {0x2cc2, 2852, 1},

      {0x24ba, 2519, 1},

      {0x1ec2, 2153, 1},

      {0x1fc2, 253, 2},

      {0xa69a, 3141, 1},

      {0xa7c2, 3342, 1},
      {0xffffffff, -1, 0},

      {0x00c2, 83, 1},

      {0x10c9a, 3861, 1},

      {0xfb16, 125, 2},

      {0x1057f, 3720, 1},

      {0x0394, 761, 1},

      {0x0533, 1360, 1},

      {0x24b6, 2507, 1},

      {0x2c8e, 2774, 1},

      {0x048e, 1111, 1},

      {0x1e8e, 2090, 1},

      {0x1f8e, 159, 2},

      {0x018e, 456, 1},

      {0x0216, 538, 1},

      {0xab8e, 1696, 1},

      {0x2c8a, 2768, 1},

      {0x048a, 1105, 1},

      {0x1e8a, 2084, 1},

      {0x1f8a, 139, 2},

      {0x018a, 625, 1},

      {0x10d61, 3987, 1},

      {0xab8a, 1684, 1},
      {0xffffffff, -1, 0},

      {0xa68e, 3123, 1},

      {0x2c62, 664, 1},

      {0x0462, 1056, 1},

      {0x1e62, 2024, 1},

      {0x10c8e, 3825, 1},

      {0x0162, 312, 1},

      {0xa762, 3237, 1},

      {0xa68a, 3117, 1},
      {0xffffffff, -1, 0},

      {0x104ca, 3648, 1},

      {0x2cca, 2864, 1},

      {0x10c8a, 3813, 1},

      {0x1eca, 2165, 1},

      {0x1fca, 2402, 1},

      {0x01ca, 428, 1},

      {0x0392, 754, 1},

      {0xa662, 3084, 1},

      {0x00ca, 108, 1},

      {0x2c82, 2756, 1},

      {0x10594, 3777, 1},

      {0x1e82, 2072, 1},

      {0x1f82, 139, 2},

      {0x0182, 357, 1},

      {0xa782, 3270, 1},

      {0xab82, 1660, 1},

      {0x10b4, 2972, 1},

      {0x1c82, 946, 1},
      {0xffffffff, -1, 0},

      {0x054c, 1435, 1},

      {0x118b4, 4062, 1},
      {0xffffffff, -1, 0},

      {0x1fa9, 214, 2},

      {0x01a9, 691, 1},

      {0xa682, 3105, 1},

      {0xaba9, 1777, 1},

      {0x16e4d, 4137, 1},

      {0x1ca9, 1543, 1},

      {0x10c82, 3789, 1},

      {0x2c80, 2753, 1},

      {0x0480, 1102, 1},

      {0x1e80, 2069, 1},

      {0x1f80, 129, 2},

      {0x10574, 3690, 1},

      {0xa780, 3267, 1},

      {0xab80, 1654, 1},

      {0x0057, 65, 1},

      {0x1c80, 908, 1},

      {0x10ca9, 3906, 1},

      {0x1e91d, 4281, 1},

      {0x10c2, 3014, 1},

      {0x03d5, 822, 1},
      {0xffffffff, -1, 0},

      {0x10592, 3774, 1},

      {0xa680, 3102, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x0386, 739, 1},

      {0x10c80, 3783, 1},

      {0x104c4, 3630, 1},

      {0x2cc4, 2855, 1},
      {0xffffffff, -1, 0},

      {0x1ec4, 2156, 1},

      {0x1fc4, 54, 2},

      {0x01c4, 420, 1},

      {0xa7c4, 3288, 1},

      {0x10d63, 3993, 1},

      {0x00c4, 89, 1},

      {0x2c9e, 2798, 1},

      {0x049e, 1135, 1},

      {0x1e9e, 24, 2},

      {0x1f9e, 199, 2},
      {0xffffffff, -1, 0},

      {0xa79e, 3303, 1},

      {0xab9e, 1744, 1},
      {0xffffffff, -1, 0},

      {0x1c9e, 1510, 1},

      {0x2c9c, 2795, 1},

      {0x049c, 1132, 1},

      {0x0531, 1354, 1},

      {0x1f9c, 189, 2},

      {0x019c, 670, 1},

      {0xa79c, 3300, 1},

      {0xab9c, 1738, 1},
      {0xffffffff, -1, 0},

      {0x1c9c, 1504, 1},

      {0x03fe, 733, 1},

      {0x10c9e, 3873, 1},
      {0xffffffff, -1, 0},

      {0x104cc, 3654, 1},

      {0x2ccc, 2867, 1},

      {0x039a, 784, 1},

      {0x1ecc, 2168, 1},

      {0x1fcc, 71, 2},

      {0x10586, 3741, 1},

      {0xa7cc, 3351, 1},

      {0x10c9c, 3867, 1},

      {0x00cc, 114, 1},

      {0x1f8d, 154, 2},
      {0xffffffff, -1, 0},

      {0xa78d, 649, 1},

      {0xab8d, 1693, 1},

      {0x03c2, 812, 1},

      {0x2c8c, 2771, 1},

      {0x048c, 1108, 1},

      {0x1e8c, 2087, 1},

      {0x1f8c, 149, 2},

      {0x10a9, 2939, 1},

      {0x0516, 1315, 1},

      {0xab8c, 1690, 1},
      {0xffffffff, -1, 0},

      {0x118a9, 4029, 1},

      {0x104b5, 3585, 1},

      {0x24c2, 2543, 1},

      {0x10c8d, 3822, 1},
      {0xffffffff, -1, 0},

      {0x038e, 845, 1},

      {0x01b5, 408, 1},

      {0xa68c, 3120, 1},

      {0xabb5, 1813, 1},

      {0x00b5, 791, 1},

      {0x1cb5, 1579, 1},

      {0x10c8c, 3819, 1},

      {0x038a, 748, 1},

      {0x2c88, 2765, 1},
      {0xffffffff, -1, 0},

      {0x1e88, 2081, 1},

      {0x1f88, 129, 2},

      {0x2c2f, 2726, 1},

      {0x042f, 1002, 1},

      {0xab88, 1678, 1},

      {0x1f2f, 2309, 1},

      {0x1c88, 3047, 1},

      {0x2ce2, 2900, 1},

      {0x04e2, 1237, 1},

      {0x1ee2, 2201, 1},

      {0x1fe2, 36, 3},

      {0x01e2, 465, 1},

      {0x10c4, 3020, 1},

      {0xa688, 3114, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x2c75, 2750, 1},

      {0x10c88, 3807, 1},

      {0x1e4a, 1987, 1},

      {0x1f4a, 2342, 1},

      {0x014a, 276, 1},

      {0xa74a, 3201, 1},

      {0xab75, 1621, 1},

      {0x004a, 24, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x24ca, 2567, 1},

      {0x1058e, 3762, 1},

      {0xff2f, 3414, 1},

      {0x10d62, 3990, 1},
      {0xffffffff, -1, 0},

      {0xa64a, 3047, 1},
      {0xffffffff, -1, 0},

      {0x03a9, 832, 1},

      {0x1058a, 3753, 1},

      {0x1041c, 3534, 1},

      {0x2c1c, 2669, 1},

      {0x041c, 940, 1},

      {0x1e1c, 1918, 1},

      {0x1f1c, 2282, 1},

      {0x011c, 213, 1},

      {0x1041a, 3528, 1},

      {0x2c1a, 2663, 1},

      {0x041a, 934, 1},

      {0x1e1a, 1915, 1},

      {0x1f1a, 2276, 1},

      {0x011a, 210, 1},

      {0x10418, 3522, 1},

      {0x2c18, 2657, 1},

      {0x0418, 928, 1},

      {0x1e18, 1912, 1},

      {0x1f18, 2270, 1},

      {0x0118, 207, 1},

      {0x1040e, 3492, 1},

      {0x2c0e, 2627, 1},

      {0x040e, 1047, 1},

      {0x1e0e, 1897, 1},

      {0x1f0e, 2264, 1},

      {0x010e, 192, 1},

      {0x104c1, 3621, 1},

      {0x10582, 3729, 1},

      {0x04c1, 1186, 1},
      {0xffffffff, -1, 0},

      {0x16e4c, 4134, 1},

      {0x1e916, 4260, 1},

      {0x10b5, 2975, 1},

      {0x10d57, 3957, 1},

      {0x00c1, 80, 1},

      {0x024a, 598, 1},

      {0x118b5, 4065, 1},

      {0x04f0, 1258, 1},

      {0x1ef0, 2222, 1},

      {0x039e, 798, 1},

      {0x01f0, 20, 2},
      {0xffffffff, -1, 0},

      {0x24c4, 2549, 1},

      {0x1e3e, 1969, 1},

      {0x1f3e, 2330, 1},
      {0xffffffff, -1, 0},

      {0xa73e, 3183, 1},

      {0x10580, 3723, 1},

      {0x039c, 791, 1},

      {0x1040c, 3486, 1},

      {0x2c0c, 2621, 1},

      {0x040c, 1041, 1},

      {0x1e0c, 1894, 1},

      {0x1f0c, 2258, 1},

      {0x010c, 189, 1},

      {0x021c, 547, 1},
      {0xffffffff, -1, 0},

      {0x10414, 3510, 1},

      {0x2c14, 2645, 1},

      {0x0414, 915, 1},

      {0x1e14, 1906, 1},

      {0x021a, 544, 1},

      {0x0114, 201, 1},

      {0x10406, 3468, 1},

      {0x2c06, 2603, 1},

      {0x0406, 1023, 1},

      {0x1e06, 1885, 1},

      {0x0218, 541, 1},

      {0x0106, 180, 1},
      {0xffffffff, -1, 0},

      {0x1f3d, 2327, 1},

      {0x013d, 258, 1},

      {0x24cc, 2573, 1},

      {0x020e, 526, 1},

      {0x038c, 842, 1},

      {0x10408, 3474, 1},

      {0x2c08, 2609, 1},

      {0x0408, 1029, 1},

      {0x1e08, 1888, 1},

      {0x1f08, 2246, 1},

      {0x0108, 183, 1},

      {0x10404, 3462, 1},

      {0x2c04, 2597, 1},

      {0x0404, 1017, 1},

      {0x1e04, 1882, 1},
      {0xffffffff, -1, 0},

      {0x0104, 177, 1},

      {0x10400, 3450, 1},

      {0x2c00, 2585, 1},

      {0x0400, 1005, 1},

      {0x1e00, 1876, 1},

      {0x0147, 273, 1},

      {0x0100, 171, 1},

      {0x023e, 2735, 1},

      {0x0047, 18, 1},

      {0x0388, 742, 1},

      {0x1e56, 2005, 1},

      {0x1f56, 62, 3},

      {0x0156, 294, 1},

      {0xa756, 3219, 1},

      {0xfb14, 109, 2},

      {0x0056, 62, 1},

      {0x020c, 523, 1},

      {0x1058d, 3759, 1},

      {0x03e2, 869, 1},

      {0x10c1, 3011, 1},

      {0xfb06, 29, 2},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0xa656, 3066, 1},

      {0x0214, 535, 1},

      {0x1058c, 3756, 1},

      {0x10426, 3564, 1},

      {0x2c26, 2699, 1},

      {0x0426, 974, 1},

      {0x1e26, 1933, 1},

      {0x0206, 514, 1},

      {0x0126, 228, 1},

      {0xa726, 3150, 1},

      {0x023d, 378, 1},

      {0x10424, 3558, 1},

      {0x2c24, 2693, 1},

      {0x0424, 968, 1},

      {0x1e24, 1930, 1},

      {0xfb04, 5, 3},

      {0x0124, 225, 1},

      {0xa724, 3147, 1},

      {0x1f59, 2354, 1},

      {0x0208, 517, 1},
      {0xffffffff, -1, 0},

      {0xfb00, 4, 2},

      {0x0059, 71, 1},

      {0x10588, 3747, 1},

      {0x1fad, 234, 2},

      {0x0204, 511, 1},

      {0xa7ad, 667, 1},

      {0xabad, 1789, 1},

      {0x1f5f, 2363, 1},

      {0x1cad, 1555, 1},

      {0xff26, 3387, 1},

      {0x0200, 505, 1},

      {0x2c2e, 2723, 1},

      {0x042e, 999, 1},

      {0x1e2e, 1945, 1},

      {0x1f2e, 2306, 1},

      {0x012e, 240, 1},

      {0xa72e, 3162, 1},

      {0xff24, 3381, 1},
      {0xffffffff, -1, 0},

      {0x10cad, 3918, 1},

      {0x10575, 3693, 1},

      {0x004f, 40, 1},

      {0x054a, 1429, 1},

      {0x2c2c, 2717, 1},

      {0x042c, 993, 1},

      {0x1e2c, 1942, 1},

      {0x1f2c, 2300, 1},

      {0x012c, 237, 1},

      {0xa72c, 3159, 1},

      {0x2c28, 2705, 1},

      {0x0428, 980, 1},

      {0x1e28, 1936, 1},

      {0x1f28, 2288, 1},

      {0x0128, 231, 1},

      {0xa728, 3153, 1},

      {0x0226, 559, 1},

      {0x03f0, 784, 1},

      {0xff2e, 3411, 1},

      {0x24c1, 2540, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x051c, 1324, 1},

      {0x0224, 556, 1},

      {0x104c0, 3618, 1},

      {0x2cc0, 2849, 1},

      {0x04c0, 1207, 1},

      {0x1ec0, 2150, 1},

      {0x051a, 1321, 1},

      {0xff2c, 3405, 1},

      {0xa7c0, 3339, 1},
      {0xffffffff, -1, 0},

      {0x00c0, 77, 1},

      {0xab73, 1615, 1},

      {0x0518, 1318, 1},

      {0xff28, 3393, 1},

      {0x10422, 3552, 1},

      {0x2c22, 2687, 1},

      {0x0422, 960, 1},

      {0x1e22, 1927, 1},

      {0x050e, 1303, 1},

      {0x0122, 222, 1},

      {0xa722, 3144, 1},

      {0x022e, 571, 1},

      {0x1e54, 2002, 1},

      {0x1f54, 57, 3},

      {0x0154, 291, 1},

      {0xa754, 3216, 1},

      {0x1e30, 1948, 1},

      {0x0054, 56, 1},

      {0x0130, 261, 2},

      {0x1e52, 1999, 1},

      {0x1f52, 52, 3},

      {0x0152, 288, 1},

      {0xa752, 3213, 1},

      {0x022c, 568, 1},

      {0x0052, 49, 1},

      {0xa654, 3063, 1},

      {0x10ad, 2951, 1},

      {0x16e57, 4167, 1},

      {0x053e, 1393, 1},

      {0x0228, 562, 1},

      {0x118ad, 4041, 1},

      {0xff22, 3375, 1},

      {0xa652, 3060, 1},

      {0x1e50, 1996, 1},

      {0x1f50, 84, 2},

      {0x0150, 285, 1},

      {0xa750, 3210, 1},

      {0x050c, 1300, 1},

      {0x0050, 43, 1},

      {0x1fab, 224, 2},

      {0xff30, 3417, 1},

      {0xa7ab, 634, 1},

      {0xabab, 1783, 1},

      {0x104b7, 3591, 1},

      {0x1cab, 1549, 1},

      {0x0514, 1312, 1},

      {0xa650, 3057, 1},

      {0x1fb7, 10, 3},

      {0x01b7, 712, 1},

      {0x2164, 2468, 1},

      {0xabb7, 1819, 1},

      {0x0506, 1291, 1},

      {0x1cb7, 1585, 1},

      {0x104b1, 3573, 1},

      {0x053d, 1390, 1},

      {0x10cab, 3912, 1},
      {0xffffffff, -1, 0},

      {0x0222, 553, 1},

      {0x01b1, 703, 1},

      {0xa7b1, 694, 1},

      {0xabb1, 1801, 1},

      {0x2c6d, 610, 1},

      {0x1cb1, 1567, 1},

      {0x0508, 1294, 1},

      {0x1f6d, 2381, 1},

      {0x10d56, 3954, 1},

      {0x0230, 574, 1},

      {0x2c6b, 2744, 1},
      {0xffffffff, -1, 0},

      {0x0504, 1288, 1},

      {0x1f6b, 2375, 1},

      {0x10c0, 3008, 1},

      {0x1e91c, 4278, 1},

      {0x10cb1, 3930, 1},

      {0x0547, 1420, 1},

      {0x0500, 1282, 1},

      {0x2c69, 2741, 1},
      {0xffffffff, -1, 0},

      {0x1e91a, 4272, 1},

      {0x1f69, 2369, 1},
      {0xffffffff, -1, 0},

      {0x0556, 1465, 1},

      {0x104b3, 3579, 1},
      {0xffffffff, -1, 0},

      {0x1e918, 4266, 1},

      {0x2167, 2477, 1},

      {0x1fb3, 62, 2},

      {0x01b3, 405, 1},

      {0xa7b3, 3369, 1},

      {0xabb3, 1807, 1},

      {0x1e90e, 4236, 1},

      {0x1cb3, 1573, 1},
      {0xffffffff, -1, 0},

      {0x1fa7, 244, 2},

      {0x01a7, 396, 1},

      {0x10d59, 3963, 1},

      {0xaba7, 1771, 1},

      {0xab71, 1609, 1},

      {0x1ca7, 1537, 1},

      {0x1e4e, 1993, 1},

      {0x0526, 1339, 1},

      {0x014e, 282, 1},

      {0xa74e, 3207, 1},
      {0xffffffff, -1, 0},

      {0x004e, 37, 1},

      {0x10d5f, 3981, 1},

      {0x1f39, 2315, 1},

      {0x0139, 252, 1},

      {0x0524, 1336, 1},

      {0x10ca7, 3900, 1},

      {0x1e48, 1984, 1},

      {0x1f48, 2336, 1},

      {0xa64e, 3054, 1},

      {0xa748, 3198, 1},

      {0x10ab, 2945, 1},

      {0x0048, 21, 1},

      {0x104d3, 3675, 1},
      {0xffffffff, -1, 0},

      {0x118ab, 4035, 1},

      {0x1e90c, 4230, 1},

      {0x1fd3, 25, 3},

      {0x01d3, 441, 1},

      {0x10b7, 2981, 1},

      {0xa648, 3044, 1},

      {0x00d3, 135, 1},
      {0xffffffff, -1, 0},

      {0x118b7, 4071, 1},

      {0x1e914, 4254, 1},

      {0x052e, 1351, 1},

      {0xff39, 3444, 1},

      {0x16e4a, 4128, 1},

      {0x054f, 1444, 1},

      {0x10b1, 2963, 1},

      {0x1e906, 4212, 1},

      {0x24c0, 2537, 1},
      {0xffffffff, -1, 0},

      {0x118b1, 4053, 1},

      {0x2c60, 2729, 1},

      {0x0460, 1053, 1},

      {0x1e60, 2020, 1},

      {0x052c, 1348, 1},

      {0x0160, 309, 1},

      {0xa760, 3234, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x1e908, 4218, 1},

      {0x0528, 1342, 1},

      {0x2c2b, 2714, 1},

      {0x042b, 990, 1},

      {0x024e, 604, 1},

      {0x1f2b, 2297, 1},

      {0x1e904, 4206, 1},

      {0xa660, 3081, 1},

      {0x10420, 3546, 1},

      {0x2c20, 2681, 1},

      {0x0420, 953, 1},

      {0x1e20, 1924, 1},

      {0x1e900, 4194, 1},

      {0x0120, 219, 1},
      {0xffffffff, -1, 0},

      {0x0248, 595, 1},

      {0x10b3, 2969, 1},

      {0x10573, 3687, 1},

      {0x10d54, 3948, 1},
      {0xffffffff, -1, 0},

      {0x118b3, 4059, 1},

      {0x03ab, 839, 1},
      {0xffffffff, -1, 0},

      {0x10a7, 2933, 1},
      {0xffffffff, -1, 0},

      {0x10d52, 3942, 1},
      {0xffffffff, -1, 0},

      {0x118a7, 4023, 1},

      {0xff2b, 3402, 1},

      {0x0522, 1333, 1},

      {0x1041e, 3540, 1},

      {0x2c1e, 2675, 1},

      {0x041e, 946, 1},

      {0x1e1e, 1921, 1},

      {0x0554, 1459, 1},

      {0x011e, 216, 1},

      {0x10425, 3561, 1},

      {0x2c25, 2696, 1},

      {0x0425, 971, 1},

      {0x10d50, 3936, 1},

      {0x24b7, 2510, 1},

      {0x0552, 1453, 1},

      {0x10412, 3504, 1},

      {0x2c12, 2639, 1},

      {0x0412, 908, 1},

      {0x1e12, 1903, 1},

      {0x2165, 2471, 1},

      {0x0112, 198, 1},

      {0x10410, 3498, 1},

      {0x2c10, 2633, 1},

      {0x0410, 902, 1},

      {0x1e10, 1900, 1},
      {0xffffffff, -1, 0},

      {0x0110, 195, 1},
      {0xffffffff, -1, 0},

      {0x0550, 1447, 1},
      {0xffffffff, -1, 0},

      {0x2cda, 2888, 1},

      {0x04da, 1225, 1},

      {0x1eda, 2189, 1},

      {0x1fda, 2408, 1},

      {0x0220, 384, 1},

      {0xa7da, 3363, 1},

      {0xff25, 3384, 1},

      {0x00da, 153, 1},

      {0x2cd8, 2885, 1},

      {0x04d8, 1222, 1},

      {0x1ed8, 2186, 1},

      {0x1fd8, 2438, 1},
      {0xffffffff, -1, 0},

      {0xa7d8, 3360, 1},
      {0xffffffff, -1, 0},

      {0x00d8, 147, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x03a7, 826, 1},

      {0x0055, 59, 1},

      {0x2cd6, 2882, 1},

      {0x04d6, 1219, 1},

      {0x1ed6, 2183, 1},

      {0x1fd6, 76, 2},
      {0xffffffff, -1, 0},

      {0xa7d6, 3357, 1},

      {0x021e, 550, 1},

      {0x00d6, 144, 1},

      {0x104d0, 3666, 1},

      {0x2cd0, 2873, 1},

      {0x04d0, 1210, 1},

      {0x1ed0, 2174, 1},

      {0x16e47, 4119, 1},

      {0x104b9, 3597, 1},

      {0xa7d0, 3354, 1},
      {0xffffffff, -1, 0},

      {0x00d0, 126, 1},

      {0x1fb9, 2435, 1},

      {0x0212, 532, 1},

      {0x16e56, 4164, 1},

      {0xabb9, 1825, 1},

      {0x104cb, 3651, 1},

      {0x1cb9, 1591, 1},

      {0x04cb, 1201, 1},

      {0x0210, 529, 1},

      {0x1fcb, 2405, 1},

      {0x01cb, 428, 1},

      {0xa7cb, 646, 1},

      {0x2183, 2504, 1},

      {0x00cb, 111, 1},

      {0x104c8, 3642, 1},

      {0x2cc8, 2861, 1},
      {0xffffffff, -1, 0},

      {0x1ec8, 2162, 1},

      {0x1fc8, 2396, 1},

      {0x01c8, 424, 1},

      {0x10571, 3681, 1},
      {0xffffffff, -1, 0},

      {0x00c8, 102, 1},

      {0x104c6, 3636, 1},

      {0x2cc6, 2858, 1},

      {0x1f5d, 2360, 1},

      {0x1ec6, 2159, 1},

      {0x1fc6, 67, 2},

      {0x054e, 1441, 1},

      {0xa7c6, 1873, 1},

      {0x104c5, 3633, 1},

      {0x00c6, 96, 1},

      {0x04c5, 1192, 1},

      {0x16e59, 4173, 1},

      {0x0539, 1378, 1},

      {0x01c5, 420, 1},

      {0xa7c5, 688, 1},
      {0xffffffff, -1, 0},

      {0x00c5, 92, 1},

      {0x0548, 1423, 1},
      {0xffffffff, -1, 0},

      {0x1faf, 244, 2},

      {0x01af, 402, 1},

      {0x16e5f, 4191, 1},

      {0xabaf, 1795, 1},

      {0x212a, 27, 1},

      {0x1caf, 1561, 1},

      {0x04fc, 1276, 1},

      {0x1efc, 2240, 1},

      {0x1ffc, 96, 2},

      {0x01fc, 499, 1},

      {0x1fa5, 234, 2},

      {0x10d60, 3984, 1},

      {0x16e4f, 4143, 1},

      {0xaba5, 1765, 1},
      {0xffffffff, -1, 0},

      {0x1ca5, 1531, 1},

      {0x10caf, 3924, 1},

      {0x1fa3, 224, 2},

      {0x1fa1, 214, 2},
      {0xffffffff, -1, 0},

      {0xaba3, 1759, 1},

      {0xaba1, 1753, 1},

      {0x1ca3, 1525, 1},

      {0x1ca1, 1519, 1},

      {0x1f91, 174, 2},

      {0x0191, 369, 1},

      {0x10ca5, 3894, 1},

      {0xab91, 1705, 1},
      {0xffffffff, -1, 0},

      {0x1c91, 1471, 1},

      {0x10421, 3549, 1},

      {0x2c21, 2684, 1},

      {0x0421, 956, 1},

      {0x10ca3, 3888, 1},

      {0x10ca1, 3882, 1},

      {0x10b9, 2987, 1},
      {0xffffffff, -1, 0},

      {0x1f49, 2339, 1},

      {0x0149, 46, 2},

      {0x118b9, 4077, 1},

      {0x10c91, 3834, 1},

      {0x0049, 4296, 1},

      {0x1f87, 164, 2},

      {0x0187, 363, 1},

      {0x0520, 1330, 1},

      {0xab87, 1675, 1},
      {0xffffffff, -1, 0},

      {0x1c87, 1056, 1},

      {0x2c72, 2747, 1},

      {0x0472, 1081, 1},

      {0x1e72, 2048, 1},
      {0xffffffff, -1, 0},

      {0x0172, 336, 1},

      {0x03da, 857, 1},

      {0xab72, 1612, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0xff21, 3372, 1},

      {0x10c87, 3804, 1},

      {0x2161, 2459, 1},

      {0x16e54, 4158, 1},

      {0x03d8, 854, 1},

      {0x1e32, 1951, 1},
      {0xffffffff, -1, 0},

      {0x0132, 243, 1},

      {0xa732, 3165, 1},

      {0x051e, 1327, 1},

      {0x16e52, 4152, 1},

      {0x10c5, 3023, 1},
      {0xffffffff, -1, 0},

      {0x2c70, 613, 1},

      {0x0470, 1078, 1},

      {0x1e70, 2045, 1},

      {0x03d6, 804, 1},

      {0x0170, 333, 1},

      {0x10af, 2957, 1},

      {0xab70, 1606, 1},
      {0xffffffff, -1, 0},

      {0x0512, 1309, 1},

      {0x118af, 4047, 1},
      {0xffffffff, -1, 0},

      {0x16e50, 4146, 1},

      {0x03d0, 754, 1},
      {0xffffffff, -1, 0},

      {0x0510, 1306, 1},

      {0x10a5, 2927, 1},

      {0xff32, 3423, 1},
      {0xffffffff, -1, 0},

      {0x10d55, 3951, 1},

      {0x118a5, 4017, 1},

      {0x047c, 1096, 1},

      {0x1e7c, 2063, 1},

      {0x10a3, 2921, 1},

      {0x10a1, 2915, 1},
      {0xffffffff, -1, 0},

      {0xab7c, 1642, 1},

      {0x118a3, 4011, 1},

      {0x118a1, 4005, 1},

      {0x24b9, 2516, 1},

      {0x046c, 1072, 1},

      {0x1e6c, 2039, 1},

      {0x1f6c, 2378, 1},

      {0x016c, 327, 1},

      {0xa76c, 3252, 1},

      {0x0555, 1462, 1},

      {0x13fc, 1858, 1},

      {0x24cb, 2570, 1},

      {0x046a, 1069, 1},

      {0x1e6a, 2036, 1},

      {0x1f6a, 2372, 1},

      {0x016a, 324, 1},

      {0xa76a, 3249, 1},

      {0x0232, 577, 1},

      {0xa66c, 3099, 1},

      {0x216f, 2501, 1},

      {0x24c8, 2561, 1},

      {0x0468, 1066, 1},

      {0x1e68, 2033, 1},

      {0x1f68, 2366, 1},

      {0x0168, 321, 1},

      {0xa768, 3246, 1},

      {0xa66a, 3096, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x24c6, 2555, 1},
      {0xffffffff, -1, 0},

      {0x1e920, 4290, 1},

      {0x2163, 2465, 1},
      {0xffffffff, -1, 0},

      {0x216e, 2498, 1},

      {0xa668, 3093, 1},

      {0x24c5, 2552, 1},
      {0xffffffff, -1, 0},

      {0x1e5a, 2011, 1},

      {0x10d5d, 3975, 1},

      {0x015a, 300, 1},

      {0xa75a, 3225, 1},

      {0x03a5, 819, 1},

      {0x005a, 74, 1},
      {0xffffffff, -1, 0},

      {0x1f8f, 164, 2},

      {0x018f, 628, 1},
      {0xffffffff, -1, 0},

      {0xab8f, 1699, 1},

      {0x03a3, 812, 1},

      {0x03a1, 808, 1},

      {0xa65a, 3072, 1},

      {0x16e4e, 4140, 1},

      {0x1e91e, 4284, 1},
      {0xffffffff, -1, 0},

      {0x1e58, 2008, 1},

      {0x0391, 751, 1},

      {0x0158, 297, 1},

      {0xa758, 3222, 1},

      {0x1e46, 1981, 1},

      {0x0058, 68, 1},

      {0x10c8f, 3828, 1},

      {0xa746, 3195, 1},

      {0x16e48, 4122, 1},

      {0x0046, 15, 1},

      {0x1e912, 4248, 1},

      {0x04ee, 1255, 1},

      {0x1eee, 2219, 1},

      {0xa658, 3069, 1},

      {0x01ee, 483, 1},

      {0x104bf, 3615, 1},

      {0x1e910, 4242, 1},

      {0xa646, 3041, 1},

      {0x1e44, 1978, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0xa744, 3192, 1},

      {0xabbf, 1843, 1},

      {0x0044, 9, 1},

      {0x1cbf, 1603, 1},

      {0x2cde, 2894, 1},

      {0x04de, 1231, 1},

      {0x1ede, 2195, 1},

      {0x0372, 724, 1},

      {0x01de, 459, 1},
      {0xffffffff, -1, 0},

      {0xa644, 3038, 1},

      {0x00de, 165, 1},

      {0x104bd, 3609, 1},

      {0x2cdc, 2891, 1},

      {0x04dc, 1228, 1},

      {0x1edc, 2192, 1},
      {0xffffffff, -1, 0},

      {0x10591, 3771, 1},

      {0xa7dc, 381, 1},

      {0xabbd, 1837, 1},

      {0x00dc, 159, 1},

      {0x1cbd, 1597, 1},

      {0x104d2, 3672, 1},

      {0x2cd2, 2876, 1},

      {0x04d2, 1213, 1},

      {0x1ed2, 2177, 1},

      {0x1fd2, 20, 3},

      {0x104bb, 3603, 1},
      {0xffffffff, -1, 0},

      {0x0370, 721, 1},

      {0x00d2, 132, 1},

      {0x1fbb, 2393, 1},

      {0x0549, 1426, 1},

      {0x0246, 592, 1},

      {0xabbb, 1831, 1},

      {0x10587, 3744, 1},
      {0xffffffff, -1, 0},

      {0x0587, 105, 2},

      {0x104cd, 3657, 1},
      {0xffffffff, -1, 0},

      {0x04cd, 1204, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x01cd, 432, 1},

      {0x10572, 3684, 1},

      {0x2162, 2462, 1},

      {0x00cd, 117, 1},

      {0x0244, 700, 1},

      {0x104c9, 3645, 1},

      {0x104c3, 3627, 1},

      {0x04c9, 1198, 1},

      {0x04c3, 1189, 1},

      {0x1fc9, 2399, 1},

      {0x1fc3, 71, 2},

      {0xa7c9, 3348, 1},
      {0xffffffff, -1, 0},

      {0x00c9, 105, 1},

      {0x00c3, 86, 1},

      {0x0532, 1357, 1},

      {0x1f9f, 204, 2},

      {0x019f, 679, 1},
      {0xffffffff, -1, 0},

      {0xab9f, 1747, 1},
      {0xffffffff, -1, 0},

      {0x1c9f, 1513, 1},

      {0x1f5b, 2357, 1},

      {0x10570, 3678, 1},

      {0x1f9d, 194, 2},

      {0x019d, 676, 1},
      {0xffffffff, -1, 0},

      {0xab9d, 1741, 1},

      {0x1f95, 194, 2},

      {0x1c9d, 1507, 1},

      {0x10bf, 3005, 1},

      {0xab95, 1717, 1},

      {0x10c9f, 3876, 1},

      {0x1c95, 1483, 1},

      {0x118bf, 4095, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x1e9b, 2020, 1},

      {0x1f9b, 184, 2},
      {0xffffffff, -1, 0},

      {0x10c9d, 3870, 1},

      {0xab9b, 1735, 1},

      {0x1057c, 3711, 1},

      {0x1c9b, 1501, 1},

      {0x10c95, 3846, 1},

      {0x1e99, 42, 2},

      {0x1f99, 174, 2},
      {0xffffffff, -1, 0},

      {0x10bd, 2999, 1},

      {0xab99, 1729, 1},

      {0x16e55, 4161, 1},

      {0x1c99, 1495, 1},

      {0x118bd, 4089, 1},

      {0x038f, 848, 1},

      {0x10c9b, 3864, 1},

      {0x1f93, 184, 2},

      {0x0193, 637, 1},
      {0xffffffff, -1, 0},

      {0xab93, 1711, 1},
      {0xffffffff, -1, 0},

      {0x1c93, 1477, 1},

      {0x1f4b, 2345, 1},

      {0x10c99, 3858, 1},

      {0x10bb, 2993, 1},
      {0xffffffff, -1, 0},

      {0x004b, 27, 1},

      {0x0143, 267, 1},

      {0x118bb, 4083, 1},

      {0x1e921, 4293, 1},

      {0x0043, 6, 1},

      {0x10d5a, 3966, 1},

      {0x10c93, 3840, 1},

      {0x1f81, 134, 2},

      {0x0181, 616, 1},

      {0x10cd, 3029, 1},

      {0xab81, 1657, 1},

      {0x03ee, 887, 1},

      {0x1c81, 915, 1},

      {0x2c7e, 583, 1},

      {0x047e, 1099, 1},

      {0x1e7e, 2066, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0xa77e, 3264, 1},

      {0xab7e, 1648, 1},

      {0x10c3, 3017, 1},

      {0x0145, 270, 1},

      {0x10d58, 3960, 1},

      {0x10c81, 3786, 1},

      {0x0045, 12, 1},

      {0x1058f, 3765, 1},

      {0x03de, 863, 1},
      {0xffffffff, -1, 0},

      {0x24bf, 2534, 1},

      {0x0476, 1087, 1},

      {0x1e76, 2054, 1},

      {0x16e5d, 4185, 1},

      {0x0176, 342, 1},
      {0xffffffff, -1, 0},

      {0xab76, 1624, 1},

      {0x03dc, 860, 1},

      {0x0466, 1063, 1},

      {0x1e66, 2030, 1},
      {0xffffffff, -1, 0},

      {0x0166, 318, 1},

      {0xa766, 3243, 1},

      {0x1e5e, 2017, 1},

      {0x0546, 1417, 1},

      {0x015e, 306, 1},

      {0xa75e, 3231, 1},

      {0x1e5c, 2014, 1},

      {0x24bd, 2528, 1},

      {0x015c, 303, 1},

      {0xa75c, 3228, 1},

      {0x0243, 354, 1},

      {0xa666, 3090, 1},
      {0xffffffff, -1, 0},

      {0x1e42, 1975, 1},
      {0xffffffff, -1, 0},

      {0xa65e, 3078, 1},

      {0xa742, 3189, 1},

      {0x0544, 1411, 1},

      {0x0042, 3, 1},

      {0xa65c, 3075, 1},

      {0x0053, 52, 1},
      {0xffffffff, -1, 0},

      {0x24bb, 2522, 1},

      {0x104cf, 3663, 1},

      {0x1e3a, 1963, 1},

      {0x1f3a, 2318, 1},

      {0xa642, 3035, 1},

      {0xa73a, 3177, 1},

      {0x01cf, 435, 1},

      {0x0141, 264, 1},

      {0x0245, 709, 1},

      {0x00cf, 123, 1},

      {0x0041, 0, 1},

      {0x24cd, 2576, 1},
      {0xffffffff, -1, 0},

      {0x04fa, 1273, 1},

      {0x1efa, 2237, 1},

      {0x1ffa, 2426, 1},

      {0x01fa, 496, 1},

      {0x039f, 801, 1},
      {0xffffffff, -1, 0},

      {0x047a, 1093, 1},

      {0x1e7a, 2060, 1},

      {0x24c9, 2564, 1},

      {0x24c3, 2546, 1},

      {0x16e49, 4125, 1},

      {0xab7a, 1636, 1},

      {0x039d, 795, 1},

      {0xff3a, 3447, 1},

      {0x0051, 46, 1},
      {0xffffffff, -1, 0},

      {0x0395, 764, 1},

      {0x04f4, 1264, 1},

      {0x1ef4, 2228, 1},

      {0x1ff4, 101, 2},

      {0x01f4, 490, 1},

      {0x2cf2, 2909, 1},

      {0x04f2, 1261, 1},

      {0x1ef2, 2225, 1},

      {0x1ff2, 257, 2},

      {0x01f2, 486, 1},

      {0x039b, 788, 1},
      {0xffffffff, -1, 0},

      {0x04ec, 1252, 1},

      {0x1eec, 2216, 1},

      {0x1fec, 2450, 1},

      {0x01ec, 480, 1},
      {0xffffffff, -1, 0},

      {0x10d5b, 3969, 1},

      {0x0399, 779, 1},

      {0x04ea, 1249, 1},

      {0x1eea, 2213, 1},

      {0x1fea, 2420, 1},

      {0x01ea, 477, 1},

      {0x023a, 2732, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x0241, 589, 1},

      {0x0393, 758, 1},

      {0x04e8, 1246, 1},

      {0x1ee8, 2210, 1},

      {0x1fe8, 2444, 1},

      {0x01e8, 474, 1},

      {0x04e6, 1243, 1},

      {0x1ee6, 2207, 1},

      {0x1fe6, 88, 2},

      {0x01e6, 471, 1},
      {0xffffffff, -1, 0},

      {0x10595, 3780, 1},

      {0x04e4, 1240, 1},

      {0x1ee4, 2204, 1},

      {0x1fe4, 80, 2},

      {0x01e4, 468, 1},

      {0x2ce0, 2897, 1},

      {0x04e0, 1234, 1},

      {0x1ee0, 2198, 1},
      {0xffffffff, -1, 0},

      {0x01e0, 462, 1},

      {0x104ce, 3660, 1},

      {0x2cce, 2870, 1},

      {0x1e40, 1972, 1},

      {0x1ece, 2171, 1},
      {0xffffffff, -1, 0},

      {0xa740, 3186, 1},

      {0x1e38, 1960, 1},

      {0x1f38, 2312, 1},

      {0x00ce, 120, 1},

      {0xa738, 3174, 1},

      {0x0345, 779, 1},

      {0x1e36, 1957, 1},

      {0x104d1, 3669, 1},

      {0x0136, 249, 1},

      {0xa736, 3171, 1},

      {0xa640, 3032, 1},

      {0x1e34, 1954, 1},

      {0x01d1, 438, 1},

      {0x0134, 246, 1},

      {0xa734, 3168, 1},

      {0x00d1, 129, 1},

      {0x0376, 727, 1},

      {0x1041b, 3531, 1},

      {0x2c1b, 2666, 1},

      {0x041b, 937, 1},

      {0x054b, 1432, 1},

      {0x1f1b, 2279, 1},

      {0x1f3f, 2333, 1},

      {0x013f, 261, 1},

      {0x0543, 1408, 1},

      {0xff38, 3441, 1},

      {0x10417, 3519, 1},

      {0x2c17, 2654, 1},

      {0x0417, 925, 1},

      {0x10581, 3726, 1},

      {0xff36, 3435, 1},

      {0x13fa, 1852, 1},

      {0x10419, 3525, 1},

      {0x2c19, 2660, 1},

      {0x0419, 931, 1},

      {0xff34, 3429, 1},

      {0x1f19, 2273, 1},
      {0xffffffff, -1, 0},

      {0x1057e, 3717, 1},

      {0x16e5a, 4176, 1},

      {0x10415, 3513, 1},

      {0x2c15, 2648, 1},

      {0x0415, 919, 1},
      {0xffffffff, -1, 0},

      {0x0545, 1414, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x10413, 3507, 1},

      {0x2c13, 2642, 1},

      {0x0413, 912, 1},

      {0x10d5e, 3978, 1},

      {0x03cf, 851, 1},
      {0xffffffff, -1, 0},

      {0x10576, 3696, 1},

      {0x10d5c, 3972, 1},
      {0xffffffff, -1, 0},

      {0x16e58, 4170, 1},

      {0x10405, 3465, 1},

      {0x2c05, 2600, 1},

      {0x0405, 1020, 1},

      {0x16e46, 4116, 1},

      {0x03fa, 899, 1},

      {0x24cf, 2582, 1},

      {0x10d53, 3945, 1},
      {0xffffffff, -1, 0},

      {0x2126, 832, 1},

      {0x1040f, 3495, 1},

      {0x2c0f, 2630, 1},

      {0x040f, 1050, 1},

      {0xfb17, 117, 2},

      {0x1f0f, 2267, 1},

      {0x1040d, 3489, 1},

      {0x2c0d, 2624, 1},

      {0x040d, 1044, 1},

      {0x16e44, 4110, 1},

      {0x1f0d, 2261, 1},
      {0xffffffff, -1, 0},

      {0x0542, 1405, 1},

      {0x03f4, 774, 1},

      {0x0553, 1456, 1},

      {0x1040b, 3483, 1},

      {0x2c0b, 2618, 1},

      {0x040b, 1038, 1},

      {0xfb15, 113, 2},

      {0x1f0b, 2255, 1},

      {0x10403, 3459, 1},

      {0x2c03, 2594, 1},

      {0x0403, 1014, 1},

      {0x053a, 1381, 1},

      {0x03ec, 884, 1},

      {0xfb13, 121, 2},

      {0x0541, 1402, 1},

      {0x10d51, 3939, 1},

      {0x10409, 3477, 1},

      {0x2c09, 2612, 1},

      {0x0409, 1032, 1},

      {0x03ea, 881, 1},

      {0x1f09, 2249, 1},

      {0x1f3b, 2321, 1},

      {0x013b, 255, 1},

      {0xfb05, 29, 2},

      {0x10401, 3453, 1},

      {0x2c01, 2588, 1},

      {0x0401, 1008, 1},

      {0x1057a, 3708, 1},

      {0x03e8, 878, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x0551, 1450, 1},

      {0x03e6, 875, 1},

      {0x04f8, 1270, 1},

      {0x1ef8, 2234, 1},

      {0x1ff8, 2414, 1},

      {0x01f8, 493, 1},
      {0xffffffff, -1, 0},

      {0x03e4, 872, 1},

      {0x04f6, 1267, 1},

      {0x1ef6, 2231, 1},

      {0x1ff6, 92, 2},

      {0x01f6, 372, 1},

      {0x03e0, 866, 1},
      {0xffffffff, -1, 0},

      {0x2cd4, 2879, 1},

      {0x04d4, 1216, 1},

      {0x1ed4, 2180, 1},

      {0x0179, 345, 1},

      {0xa779, 3258, 1},

      {0xab79, 1633, 1},

      {0xfb03, 0, 3},

      {0x00d4, 138, 1},

      {0x1e3c, 1966, 1},

      {0x1f3c, 2324, 1},
      {0xffffffff, -1, 0},

      {0xa73c, 3180, 1},

      {0x10423, 3555, 1},

      {0x2c23, 2690, 1},

      {0x0423, 965, 1},

      {0x24ce, 2579, 1},

      {0x03d1, 774, 1},

      {0x2c2d, 2720, 1},

      {0x042d, 996, 1},

      {0x16e5b, 4179, 1},

      {0x1f2d, 2303, 1},
      {0xffffffff, -1, 0},

      {0xfb01, 8, 2},

      {0x2c29, 2708, 1},

      {0x0429, 983, 1},

      {0x023b, 580, 1},

      {0x1f29, 2291, 1},

      {0x1041f, 3543, 1},

      {0x2c1f, 2678, 1},

      {0x041f, 950, 1},

      {0x10411, 3501, 1},

      {0x2c11, 2636, 1},

      {0x0411, 905, 1},

      {0x10407, 3471, 1},

      {0x2c07, 2606, 1},

      {0x0407, 1026, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0xff23, 3378, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x0540, 1399, 1},

      {0xff2d, 3408, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x0538, 1375, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0xff29, 3396, 1},
      {0xffffffff, -1, 0},

      {0x0536, 1369, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x0534, 1363, 1},
      {0xffffffff, -1, 0},

      {0x16e4b, 4131, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x16e43, 4107, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x053f, 1396, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0},

      {0x216d, 2495, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x216b, 2489, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x16e45, 4113, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x13f8, 1846, 1},

      {0x2169, 2483, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x16e5e, 4188, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x16e5c, 4182, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0},

      {0x16e42, 4104, 1},
      {0xffffffff, -1, 0},

      {0x16e53, 4155, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0},

      {0x16e41, 4101, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x053b, 1384, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x16e51, 4149, 1},

      {0x1e91b, 4275, 1},
      {0xffffffff, -1, 0},

      {0x2160, 2456, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0},

      {0x1e917, 4263, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x212b, 92, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x1e919, 4269, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x10579, 3705, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x1e915, 4257, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x053c, 1387, 1},
      {0xffffffff, -1, 0},

      {0x1e913, 4251, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x1e905, 4209, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x1e90f, 4239, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x1e90d, 4233, 1},

      {0x16e40, 4098, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x1e90b, 4227, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x1e903, 4203, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x1e909, 4221, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x1e901, 4197, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x1e91f, 4287, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x1e911, 4245, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x1e907, 4215, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x2132, 2453, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x216c, 2492, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x216a, 2486, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x2168, 2480, 1},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},
      {0xffffffff, -1, 0}, {0xffffffff, -1, 0}, {0xffffffff, -1, 0},

      {0x2166, 2474, 1}
    };


    {
      int key = hash(&code);

      if (key <= MAX_HASH_VALUE)
        {
          OnigCodePoint gcode = wordlist[key].code;

          if (code == gcode && wordlist[key].index >= 0)
            return &wordlist[key];
        }
    }
  return 0;
}
