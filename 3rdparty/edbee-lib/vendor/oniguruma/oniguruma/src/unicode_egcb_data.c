/* unicode_egcb_data.c: Generated by make_unicode_egcb_data.py. */
/*-
 * Copyright (c) 2017-2024  <PERSON><PERSON>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR AND CONTRIBUTORS ``AS IS'' AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR OR CONTRIBUTORS BE LIABLE
 * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 */

#define GRAPHEME_BREAK_PROPERTY_VERSION  160000

/*
CR
Control
Extend
L
LF
LV
LVT
Prepend
Regional_Indicator
SpacingMark
T
V
ZWJ
*/

static int EGCB_RANGE_NUM = 1376;
static EGCB_RANGE_TYPE EGCB_RANGES[] = {
 {0x000000, 0x000009, EGCB_Control },
 {0x00000a, 0x00000a, EGCB_LF },
 {0x00000b, 0x00000c, EGCB_Control },
 {0x00000d, 0x00000d, EGCB_CR },
 {0x00000e, 0x00001f, EGCB_Control },
 {0x00007f, 0x00009f, EGCB_Control },
 {0x0000ad, 0x0000ad, EGCB_Control },
 {0x000300, 0x00036f, EGCB_Extend },
 {0x000483, 0x000489, EGCB_Extend },
 {0x000591, 0x0005bd, EGCB_Extend },
 {0x0005bf, 0x0005bf, EGCB_Extend },
 {0x0005c1, 0x0005c2, EGCB_Extend },
 {0x0005c4, 0x0005c5, EGCB_Extend },
 {0x0005c7, 0x0005c7, EGCB_Extend },
 {0x000600, 0x000605, EGCB_Prepend },
 {0x000610, 0x00061a, EGCB_Extend },
 {0x00061c, 0x00061c, EGCB_Control },
 {0x00064b, 0x00065f, EGCB_Extend },
 {0x000670, 0x000670, EGCB_Extend },
 {0x0006d6, 0x0006dc, EGCB_Extend },
 {0x0006dd, 0x0006dd, EGCB_Prepend },
 {0x0006df, 0x0006e4, EGCB_Extend },
 {0x0006e7, 0x0006e8, EGCB_Extend },
 {0x0006ea, 0x0006ed, EGCB_Extend },
 {0x00070f, 0x00070f, EGCB_Prepend },
 {0x000711, 0x000711, EGCB_Extend },
 {0x000730, 0x00074a, EGCB_Extend },
 {0x0007a6, 0x0007b0, EGCB_Extend },
 {0x0007eb, 0x0007f3, EGCB_Extend },
 {0x0007fd, 0x0007fd, EGCB_Extend },
 {0x000816, 0x000819, EGCB_Extend },
 {0x00081b, 0x000823, EGCB_Extend },
 {0x000825, 0x000827, EGCB_Extend },
 {0x000829, 0x00082d, EGCB_Extend },
 {0x000859, 0x00085b, EGCB_Extend },
 {0x000890, 0x000891, EGCB_Prepend },
 {0x000897, 0x00089f, EGCB_Extend },
 {0x0008ca, 0x0008e1, EGCB_Extend },
 {0x0008e2, 0x0008e2, EGCB_Prepend },
 {0x0008e3, 0x000902, EGCB_Extend },
 {0x000903, 0x000903, EGCB_SpacingMark },
 {0x00093a, 0x00093a, EGCB_Extend },
 {0x00093b, 0x00093b, EGCB_SpacingMark },
 {0x00093c, 0x00093c, EGCB_Extend },
 {0x00093e, 0x000940, EGCB_SpacingMark },
 {0x000941, 0x000948, EGCB_Extend },
 {0x000949, 0x00094c, EGCB_SpacingMark },
 {0x00094d, 0x00094d, EGCB_Extend },
 {0x00094e, 0x00094f, EGCB_SpacingMark },
 {0x000951, 0x000957, EGCB_Extend },
 {0x000962, 0x000963, EGCB_Extend },
 {0x000981, 0x000981, EGCB_Extend },
 {0x000982, 0x000983, EGCB_SpacingMark },
 {0x0009bc, 0x0009bc, EGCB_Extend },
 {0x0009be, 0x0009be, EGCB_Extend },
 {0x0009bf, 0x0009c0, EGCB_SpacingMark },
 {0x0009c1, 0x0009c4, EGCB_Extend },
 {0x0009c7, 0x0009c8, EGCB_SpacingMark },
 {0x0009cb, 0x0009cc, EGCB_SpacingMark },
 {0x0009cd, 0x0009cd, EGCB_Extend },
 {0x0009d7, 0x0009d7, EGCB_Extend },
 {0x0009e2, 0x0009e3, EGCB_Extend },
 {0x0009fe, 0x0009fe, EGCB_Extend },
 {0x000a01, 0x000a02, EGCB_Extend },
 {0x000a03, 0x000a03, EGCB_SpacingMark },
 {0x000a3c, 0x000a3c, EGCB_Extend },
 {0x000a3e, 0x000a40, EGCB_SpacingMark },
 {0x000a41, 0x000a42, EGCB_Extend },
 {0x000a47, 0x000a48, EGCB_Extend },
 {0x000a4b, 0x000a4d, EGCB_Extend },
 {0x000a51, 0x000a51, EGCB_Extend },
 {0x000a70, 0x000a71, EGCB_Extend },
 {0x000a75, 0x000a75, EGCB_Extend },
 {0x000a81, 0x000a82, EGCB_Extend },
 {0x000a83, 0x000a83, EGCB_SpacingMark },
 {0x000abc, 0x000abc, EGCB_Extend },
 {0x000abe, 0x000ac0, EGCB_SpacingMark },
 {0x000ac1, 0x000ac5, EGCB_Extend },
 {0x000ac7, 0x000ac8, EGCB_Extend },
 {0x000ac9, 0x000ac9, EGCB_SpacingMark },
 {0x000acb, 0x000acc, EGCB_SpacingMark },
 {0x000acd, 0x000acd, EGCB_Extend },
 {0x000ae2, 0x000ae3, EGCB_Extend },
 {0x000afa, 0x000aff, EGCB_Extend },
 {0x000b01, 0x000b01, EGCB_Extend },
 {0x000b02, 0x000b03, EGCB_SpacingMark },
 {0x000b3c, 0x000b3c, EGCB_Extend },
 {0x000b3e, 0x000b3f, EGCB_Extend },
 {0x000b40, 0x000b40, EGCB_SpacingMark },
 {0x000b41, 0x000b44, EGCB_Extend },
 {0x000b47, 0x000b48, EGCB_SpacingMark },
 {0x000b4b, 0x000b4c, EGCB_SpacingMark },
 {0x000b4d, 0x000b4d, EGCB_Extend },
 {0x000b55, 0x000b57, EGCB_Extend },
 {0x000b62, 0x000b63, EGCB_Extend },
 {0x000b82, 0x000b82, EGCB_Extend },
 {0x000bbe, 0x000bbe, EGCB_Extend },
 {0x000bbf, 0x000bbf, EGCB_SpacingMark },
 {0x000bc0, 0x000bc0, EGCB_Extend },
 {0x000bc1, 0x000bc2, EGCB_SpacingMark },
 {0x000bc6, 0x000bc8, EGCB_SpacingMark },
 {0x000bca, 0x000bcc, EGCB_SpacingMark },
 {0x000bcd, 0x000bcd, EGCB_Extend },
 {0x000bd7, 0x000bd7, EGCB_Extend },
 {0x000c00, 0x000c00, EGCB_Extend },
 {0x000c01, 0x000c03, EGCB_SpacingMark },
 {0x000c04, 0x000c04, EGCB_Extend },
 {0x000c3c, 0x000c3c, EGCB_Extend },
 {0x000c3e, 0x000c40, EGCB_Extend },
 {0x000c41, 0x000c44, EGCB_SpacingMark },
 {0x000c46, 0x000c48, EGCB_Extend },
 {0x000c4a, 0x000c4d, EGCB_Extend },
 {0x000c55, 0x000c56, EGCB_Extend },
 {0x000c62, 0x000c63, EGCB_Extend },
 {0x000c81, 0x000c81, EGCB_Extend },
 {0x000c82, 0x000c83, EGCB_SpacingMark },
 {0x000cbc, 0x000cbc, EGCB_Extend },
 {0x000cbe, 0x000cbe, EGCB_SpacingMark },
 {0x000cbf, 0x000cc0, EGCB_Extend },
 {0x000cc1, 0x000cc1, EGCB_SpacingMark },
 {0x000cc2, 0x000cc2, EGCB_Extend },
 {0x000cc3, 0x000cc4, EGCB_SpacingMark },
 {0x000cc6, 0x000cc8, EGCB_Extend },
 {0x000cca, 0x000ccd, EGCB_Extend },
 {0x000cd5, 0x000cd6, EGCB_Extend },
 {0x000ce2, 0x000ce3, EGCB_Extend },
 {0x000cf3, 0x000cf3, EGCB_SpacingMark },
 {0x000d00, 0x000d01, EGCB_Extend },
 {0x000d02, 0x000d03, EGCB_SpacingMark },
 {0x000d3b, 0x000d3c, EGCB_Extend },
 {0x000d3e, 0x000d3e, EGCB_Extend },
 {0x000d3f, 0x000d40, EGCB_SpacingMark },
 {0x000d41, 0x000d44, EGCB_Extend },
 {0x000d46, 0x000d48, EGCB_SpacingMark },
 {0x000d4a, 0x000d4c, EGCB_SpacingMark },
 {0x000d4d, 0x000d4d, EGCB_Extend },
 {0x000d4e, 0x000d4e, EGCB_Prepend },
 {0x000d57, 0x000d57, EGCB_Extend },
 {0x000d62, 0x000d63, EGCB_Extend },
 {0x000d81, 0x000d81, EGCB_Extend },
 {0x000d82, 0x000d83, EGCB_SpacingMark },
 {0x000dca, 0x000dca, EGCB_Extend },
 {0x000dcf, 0x000dcf, EGCB_Extend },
 {0x000dd0, 0x000dd1, EGCB_SpacingMark },
 {0x000dd2, 0x000dd4, EGCB_Extend },
 {0x000dd6, 0x000dd6, EGCB_Extend },
 {0x000dd8, 0x000dde, EGCB_SpacingMark },
 {0x000ddf, 0x000ddf, EGCB_Extend },
 {0x000df2, 0x000df3, EGCB_SpacingMark },
 {0x000e31, 0x000e31, EGCB_Extend },
 {0x000e33, 0x000e33, EGCB_SpacingMark },
 {0x000e34, 0x000e3a, EGCB_Extend },
 {0x000e47, 0x000e4e, EGCB_Extend },
 {0x000eb1, 0x000eb1, EGCB_Extend },
 {0x000eb3, 0x000eb3, EGCB_SpacingMark },
 {0x000eb4, 0x000ebc, EGCB_Extend },
 {0x000ec8, 0x000ece, EGCB_Extend },
 {0x000f18, 0x000f19, EGCB_Extend },
 {0x000f35, 0x000f35, EGCB_Extend },
 {0x000f37, 0x000f37, EGCB_Extend },
 {0x000f39, 0x000f39, EGCB_Extend },
 {0x000f3e, 0x000f3f, EGCB_SpacingMark },
 {0x000f71, 0x000f7e, EGCB_Extend },
 {0x000f7f, 0x000f7f, EGCB_SpacingMark },
 {0x000f80, 0x000f84, EGCB_Extend },
 {0x000f86, 0x000f87, EGCB_Extend },
 {0x000f8d, 0x000f97, EGCB_Extend },
 {0x000f99, 0x000fbc, EGCB_Extend },
 {0x000fc6, 0x000fc6, EGCB_Extend },
 {0x00102d, 0x001030, EGCB_Extend },
 {0x001031, 0x001031, EGCB_SpacingMark },
 {0x001032, 0x001037, EGCB_Extend },
 {0x001039, 0x00103a, EGCB_Extend },
 {0x00103b, 0x00103c, EGCB_SpacingMark },
 {0x00103d, 0x00103e, EGCB_Extend },
 {0x001056, 0x001057, EGCB_SpacingMark },
 {0x001058, 0x001059, EGCB_Extend },
 {0x00105e, 0x001060, EGCB_Extend },
 {0x001071, 0x001074, EGCB_Extend },
 {0x001082, 0x001082, EGCB_Extend },
 {0x001084, 0x001084, EGCB_SpacingMark },
 {0x001085, 0x001086, EGCB_Extend },
 {0x00108d, 0x00108d, EGCB_Extend },
 {0x00109d, 0x00109d, EGCB_Extend },
 {0x001100, 0x00115f, EGCB_L },
 {0x001160, 0x0011a7, EGCB_V },
 {0x0011a8, 0x0011ff, EGCB_T },
 {0x00135d, 0x00135f, EGCB_Extend },
 {0x001712, 0x001715, EGCB_Extend },
 {0x001732, 0x001734, EGCB_Extend },
 {0x001752, 0x001753, EGCB_Extend },
 {0x001772, 0x001773, EGCB_Extend },
 {0x0017b4, 0x0017b5, EGCB_Extend },
 {0x0017b6, 0x0017b6, EGCB_SpacingMark },
 {0x0017b7, 0x0017bd, EGCB_Extend },
 {0x0017be, 0x0017c5, EGCB_SpacingMark },
 {0x0017c6, 0x0017c6, EGCB_Extend },
 {0x0017c7, 0x0017c8, EGCB_SpacingMark },
 {0x0017c9, 0x0017d3, EGCB_Extend },
 {0x0017dd, 0x0017dd, EGCB_Extend },
 {0x00180b, 0x00180d, EGCB_Extend },
 {0x00180e, 0x00180e, EGCB_Control },
 {0x00180f, 0x00180f, EGCB_Extend },
 {0x001885, 0x001886, EGCB_Extend },
 {0x0018a9, 0x0018a9, EGCB_Extend },
 {0x001920, 0x001922, EGCB_Extend },
 {0x001923, 0x001926, EGCB_SpacingMark },
 {0x001927, 0x001928, EGCB_Extend },
 {0x001929, 0x00192b, EGCB_SpacingMark },
 {0x001930, 0x001931, EGCB_SpacingMark },
 {0x001932, 0x001932, EGCB_Extend },
 {0x001933, 0x001938, EGCB_SpacingMark },
 {0x001939, 0x00193b, EGCB_Extend },
 {0x001a17, 0x001a18, EGCB_Extend },
 {0x001a19, 0x001a1a, EGCB_SpacingMark },
 {0x001a1b, 0x001a1b, EGCB_Extend },
 {0x001a55, 0x001a55, EGCB_SpacingMark },
 {0x001a56, 0x001a56, EGCB_Extend },
 {0x001a57, 0x001a57, EGCB_SpacingMark },
 {0x001a58, 0x001a5e, EGCB_Extend },
 {0x001a60, 0x001a60, EGCB_Extend },
 {0x001a62, 0x001a62, EGCB_Extend },
 {0x001a65, 0x001a6c, EGCB_Extend },
 {0x001a6d, 0x001a72, EGCB_SpacingMark },
 {0x001a73, 0x001a7c, EGCB_Extend },
 {0x001a7f, 0x001a7f, EGCB_Extend },
 {0x001ab0, 0x001ace, EGCB_Extend },
 {0x001b00, 0x001b03, EGCB_Extend },
 {0x001b04, 0x001b04, EGCB_SpacingMark },
 {0x001b34, 0x001b3d, EGCB_Extend },
 {0x001b3e, 0x001b41, EGCB_SpacingMark },
 {0x001b42, 0x001b44, EGCB_Extend },
 {0x001b6b, 0x001b73, EGCB_Extend },
 {0x001b80, 0x001b81, EGCB_Extend },
 {0x001b82, 0x001b82, EGCB_SpacingMark },
 {0x001ba1, 0x001ba1, EGCB_SpacingMark },
 {0x001ba2, 0x001ba5, EGCB_Extend },
 {0x001ba6, 0x001ba7, EGCB_SpacingMark },
 {0x001ba8, 0x001bad, EGCB_Extend },
 {0x001be6, 0x001be6, EGCB_Extend },
 {0x001be7, 0x001be7, EGCB_SpacingMark },
 {0x001be8, 0x001be9, EGCB_Extend },
 {0x001bea, 0x001bec, EGCB_SpacingMark },
 {0x001bed, 0x001bed, EGCB_Extend },
 {0x001bee, 0x001bee, EGCB_SpacingMark },
 {0x001bef, 0x001bf3, EGCB_Extend },
 {0x001c24, 0x001c2b, EGCB_SpacingMark },
 {0x001c2c, 0x001c33, EGCB_Extend },
 {0x001c34, 0x001c35, EGCB_SpacingMark },
 {0x001c36, 0x001c37, EGCB_Extend },
 {0x001cd0, 0x001cd2, EGCB_Extend },
 {0x001cd4, 0x001ce0, EGCB_Extend },
 {0x001ce1, 0x001ce1, EGCB_SpacingMark },
 {0x001ce2, 0x001ce8, EGCB_Extend },
 {0x001ced, 0x001ced, EGCB_Extend },
 {0x001cf4, 0x001cf4, EGCB_Extend },
 {0x001cf7, 0x001cf7, EGCB_SpacingMark },
 {0x001cf8, 0x001cf9, EGCB_Extend },
 {0x001dc0, 0x001dff, EGCB_Extend },
 {0x00200b, 0x00200b, EGCB_Control },
 {0x00200c, 0x00200c, EGCB_Extend },
 {0x00200d, 0x00200d, EGCB_ZWJ },
 {0x00200e, 0x00200f, EGCB_Control },
 {0x002028, 0x00202e, EGCB_Control },
 {0x002060, 0x00206f, EGCB_Control },
 {0x0020d0, 0x0020f0, EGCB_Extend },
 {0x002cef, 0x002cf1, EGCB_Extend },
 {0x002d7f, 0x002d7f, EGCB_Extend },
 {0x002de0, 0x002dff, EGCB_Extend },
 {0x00302a, 0x00302f, EGCB_Extend },
 {0x003099, 0x00309a, EGCB_Extend },
 {0x00a66f, 0x00a672, EGCB_Extend },
 {0x00a674, 0x00a67d, EGCB_Extend },
 {0x00a69e, 0x00a69f, EGCB_Extend },
 {0x00a6f0, 0x00a6f1, EGCB_Extend },
 {0x00a802, 0x00a802, EGCB_Extend },
 {0x00a806, 0x00a806, EGCB_Extend },
 {0x00a80b, 0x00a80b, EGCB_Extend },
 {0x00a823, 0x00a824, EGCB_SpacingMark },
 {0x00a825, 0x00a826, EGCB_Extend },
 {0x00a827, 0x00a827, EGCB_SpacingMark },
 {0x00a82c, 0x00a82c, EGCB_Extend },
 {0x00a880, 0x00a881, EGCB_SpacingMark },
 {0x00a8b4, 0x00a8c3, EGCB_SpacingMark },
 {0x00a8c4, 0x00a8c5, EGCB_Extend },
 {0x00a8e0, 0x00a8f1, EGCB_Extend },
 {0x00a8ff, 0x00a8ff, EGCB_Extend },
 {0x00a926, 0x00a92d, EGCB_Extend },
 {0x00a947, 0x00a951, EGCB_Extend },
 {0x00a952, 0x00a952, EGCB_SpacingMark },
 {0x00a953, 0x00a953, EGCB_Extend },
 {0x00a960, 0x00a97c, EGCB_L },
 {0x00a980, 0x00a982, EGCB_Extend },
 {0x00a983, 0x00a983, EGCB_SpacingMark },
 {0x00a9b3, 0x00a9b3, EGCB_Extend },
 {0x00a9b4, 0x00a9b5, EGCB_SpacingMark },
 {0x00a9b6, 0x00a9b9, EGCB_Extend },
 {0x00a9ba, 0x00a9bb, EGCB_SpacingMark },
 {0x00a9bc, 0x00a9bd, EGCB_Extend },
 {0x00a9be, 0x00a9bf, EGCB_SpacingMark },
 {0x00a9c0, 0x00a9c0, EGCB_Extend },
 {0x00a9e5, 0x00a9e5, EGCB_Extend },
 {0x00aa29, 0x00aa2e, EGCB_Extend },
 {0x00aa2f, 0x00aa30, EGCB_SpacingMark },
 {0x00aa31, 0x00aa32, EGCB_Extend },
 {0x00aa33, 0x00aa34, EGCB_SpacingMark },
 {0x00aa35, 0x00aa36, EGCB_Extend },
 {0x00aa43, 0x00aa43, EGCB_Extend },
 {0x00aa4c, 0x00aa4c, EGCB_Extend },
 {0x00aa4d, 0x00aa4d, EGCB_SpacingMark },
 {0x00aa7c, 0x00aa7c, EGCB_Extend },
 {0x00aab0, 0x00aab0, EGCB_Extend },
 {0x00aab2, 0x00aab4, EGCB_Extend },
 {0x00aab7, 0x00aab8, EGCB_Extend },
 {0x00aabe, 0x00aabf, EGCB_Extend },
 {0x00aac1, 0x00aac1, EGCB_Extend },
 {0x00aaeb, 0x00aaeb, EGCB_SpacingMark },
 {0x00aaec, 0x00aaed, EGCB_Extend },
 {0x00aaee, 0x00aaef, EGCB_SpacingMark },
 {0x00aaf5, 0x00aaf5, EGCB_SpacingMark },
 {0x00aaf6, 0x00aaf6, EGCB_Extend },
 {0x00abe3, 0x00abe4, EGCB_SpacingMark },
 {0x00abe5, 0x00abe5, EGCB_Extend },
 {0x00abe6, 0x00abe7, EGCB_SpacingMark },
 {0x00abe8, 0x00abe8, EGCB_Extend },
 {0x00abe9, 0x00abea, EGCB_SpacingMark },
 {0x00abec, 0x00abec, EGCB_SpacingMark },
 {0x00abed, 0x00abed, EGCB_Extend },
 {0x00ac00, 0x00ac00, EGCB_LV },
 {0x00ac01, 0x00ac1b, EGCB_LVT },
 {0x00ac1c, 0x00ac1c, EGCB_LV },
 {0x00ac1d, 0x00ac37, EGCB_LVT },
 {0x00ac38, 0x00ac38, EGCB_LV },
 {0x00ac39, 0x00ac53, EGCB_LVT },
 {0x00ac54, 0x00ac54, EGCB_LV },
 {0x00ac55, 0x00ac6f, EGCB_LVT },
 {0x00ac70, 0x00ac70, EGCB_LV },
 {0x00ac71, 0x00ac8b, EGCB_LVT },
 {0x00ac8c, 0x00ac8c, EGCB_LV },
 {0x00ac8d, 0x00aca7, EGCB_LVT },
 {0x00aca8, 0x00aca8, EGCB_LV },
 {0x00aca9, 0x00acc3, EGCB_LVT },
 {0x00acc4, 0x00acc4, EGCB_LV },
 {0x00acc5, 0x00acdf, EGCB_LVT },
 {0x00ace0, 0x00ace0, EGCB_LV },
 {0x00ace1, 0x00acfb, EGCB_LVT },
 {0x00acfc, 0x00acfc, EGCB_LV },
 {0x00acfd, 0x00ad17, EGCB_LVT },
 {0x00ad18, 0x00ad18, EGCB_LV },
 {0x00ad19, 0x00ad33, EGCB_LVT },
 {0x00ad34, 0x00ad34, EGCB_LV },
 {0x00ad35, 0x00ad4f, EGCB_LVT },
 {0x00ad50, 0x00ad50, EGCB_LV },
 {0x00ad51, 0x00ad6b, EGCB_LVT },
 {0x00ad6c, 0x00ad6c, EGCB_LV },
 {0x00ad6d, 0x00ad87, EGCB_LVT },
 {0x00ad88, 0x00ad88, EGCB_LV },
 {0x00ad89, 0x00ada3, EGCB_LVT },
 {0x00ada4, 0x00ada4, EGCB_LV },
 {0x00ada5, 0x00adbf, EGCB_LVT },
 {0x00adc0, 0x00adc0, EGCB_LV },
 {0x00adc1, 0x00addb, EGCB_LVT },
 {0x00addc, 0x00addc, EGCB_LV },
 {0x00addd, 0x00adf7, EGCB_LVT },
 {0x00adf8, 0x00adf8, EGCB_LV },
 {0x00adf9, 0x00ae13, EGCB_LVT },
 {0x00ae14, 0x00ae14, EGCB_LV },
 {0x00ae15, 0x00ae2f, EGCB_LVT },
 {0x00ae30, 0x00ae30, EGCB_LV },
 {0x00ae31, 0x00ae4b, EGCB_LVT },
 {0x00ae4c, 0x00ae4c, EGCB_LV },
 {0x00ae4d, 0x00ae67, EGCB_LVT },
 {0x00ae68, 0x00ae68, EGCB_LV },
 {0x00ae69, 0x00ae83, EGCB_LVT },
 {0x00ae84, 0x00ae84, EGCB_LV },
 {0x00ae85, 0x00ae9f, EGCB_LVT },
 {0x00aea0, 0x00aea0, EGCB_LV },
 {0x00aea1, 0x00aebb, EGCB_LVT },
 {0x00aebc, 0x00aebc, EGCB_LV },
 {0x00aebd, 0x00aed7, EGCB_LVT },
 {0x00aed8, 0x00aed8, EGCB_LV },
 {0x00aed9, 0x00aef3, EGCB_LVT },
 {0x00aef4, 0x00aef4, EGCB_LV },
 {0x00aef5, 0x00af0f, EGCB_LVT },
 {0x00af10, 0x00af10, EGCB_LV },
 {0x00af11, 0x00af2b, EGCB_LVT },
 {0x00af2c, 0x00af2c, EGCB_LV },
 {0x00af2d, 0x00af47, EGCB_LVT },
 {0x00af48, 0x00af48, EGCB_LV },
 {0x00af49, 0x00af63, EGCB_LVT },
 {0x00af64, 0x00af64, EGCB_LV },
 {0x00af65, 0x00af7f, EGCB_LVT },
 {0x00af80, 0x00af80, EGCB_LV },
 {0x00af81, 0x00af9b, EGCB_LVT },
 {0x00af9c, 0x00af9c, EGCB_LV },
 {0x00af9d, 0x00afb7, EGCB_LVT },
 {0x00afb8, 0x00afb8, EGCB_LV },
 {0x00afb9, 0x00afd3, EGCB_LVT },
 {0x00afd4, 0x00afd4, EGCB_LV },
 {0x00afd5, 0x00afef, EGCB_LVT },
 {0x00aff0, 0x00aff0, EGCB_LV },
 {0x00aff1, 0x00b00b, EGCB_LVT },
 {0x00b00c, 0x00b00c, EGCB_LV },
 {0x00b00d, 0x00b027, EGCB_LVT },
 {0x00b028, 0x00b028, EGCB_LV },
 {0x00b029, 0x00b043, EGCB_LVT },
 {0x00b044, 0x00b044, EGCB_LV },
 {0x00b045, 0x00b05f, EGCB_LVT },
 {0x00b060, 0x00b060, EGCB_LV },
 {0x00b061, 0x00b07b, EGCB_LVT },
 {0x00b07c, 0x00b07c, EGCB_LV },
 {0x00b07d, 0x00b097, EGCB_LVT },
 {0x00b098, 0x00b098, EGCB_LV },
 {0x00b099, 0x00b0b3, EGCB_LVT },
 {0x00b0b4, 0x00b0b4, EGCB_LV },
 {0x00b0b5, 0x00b0cf, EGCB_LVT },
 {0x00b0d0, 0x00b0d0, EGCB_LV },
 {0x00b0d1, 0x00b0eb, EGCB_LVT },
 {0x00b0ec, 0x00b0ec, EGCB_LV },
 {0x00b0ed, 0x00b107, EGCB_LVT },
 {0x00b108, 0x00b108, EGCB_LV },
 {0x00b109, 0x00b123, EGCB_LVT },
 {0x00b124, 0x00b124, EGCB_LV },
 {0x00b125, 0x00b13f, EGCB_LVT },
 {0x00b140, 0x00b140, EGCB_LV },
 {0x00b141, 0x00b15b, EGCB_LVT },
 {0x00b15c, 0x00b15c, EGCB_LV },
 {0x00b15d, 0x00b177, EGCB_LVT },
 {0x00b178, 0x00b178, EGCB_LV },
 {0x00b179, 0x00b193, EGCB_LVT },
 {0x00b194, 0x00b194, EGCB_LV },
 {0x00b195, 0x00b1af, EGCB_LVT },
 {0x00b1b0, 0x00b1b0, EGCB_LV },
 {0x00b1b1, 0x00b1cb, EGCB_LVT },
 {0x00b1cc, 0x00b1cc, EGCB_LV },
 {0x00b1cd, 0x00b1e7, EGCB_LVT },
 {0x00b1e8, 0x00b1e8, EGCB_LV },
 {0x00b1e9, 0x00b203, EGCB_LVT },
 {0x00b204, 0x00b204, EGCB_LV },
 {0x00b205, 0x00b21f, EGCB_LVT },
 {0x00b220, 0x00b220, EGCB_LV },
 {0x00b221, 0x00b23b, EGCB_LVT },
 {0x00b23c, 0x00b23c, EGCB_LV },
 {0x00b23d, 0x00b257, EGCB_LVT },
 {0x00b258, 0x00b258, EGCB_LV },
 {0x00b259, 0x00b273, EGCB_LVT },
 {0x00b274, 0x00b274, EGCB_LV },
 {0x00b275, 0x00b28f, EGCB_LVT },
 {0x00b290, 0x00b290, EGCB_LV },
 {0x00b291, 0x00b2ab, EGCB_LVT },
 {0x00b2ac, 0x00b2ac, EGCB_LV },
 {0x00b2ad, 0x00b2c7, EGCB_LVT },
 {0x00b2c8, 0x00b2c8, EGCB_LV },
 {0x00b2c9, 0x00b2e3, EGCB_LVT },
 {0x00b2e4, 0x00b2e4, EGCB_LV },
 {0x00b2e5, 0x00b2ff, EGCB_LVT },
 {0x00b300, 0x00b300, EGCB_LV },
 {0x00b301, 0x00b31b, EGCB_LVT },
 {0x00b31c, 0x00b31c, EGCB_LV },
 {0x00b31d, 0x00b337, EGCB_LVT },
 {0x00b338, 0x00b338, EGCB_LV },
 {0x00b339, 0x00b353, EGCB_LVT },
 {0x00b354, 0x00b354, EGCB_LV },
 {0x00b355, 0x00b36f, EGCB_LVT },
 {0x00b370, 0x00b370, EGCB_LV },
 {0x00b371, 0x00b38b, EGCB_LVT },
 {0x00b38c, 0x00b38c, EGCB_LV },
 {0x00b38d, 0x00b3a7, EGCB_LVT },
 {0x00b3a8, 0x00b3a8, EGCB_LV },
 {0x00b3a9, 0x00b3c3, EGCB_LVT },
 {0x00b3c4, 0x00b3c4, EGCB_LV },
 {0x00b3c5, 0x00b3df, EGCB_LVT },
 {0x00b3e0, 0x00b3e0, EGCB_LV },
 {0x00b3e1, 0x00b3fb, EGCB_LVT },
 {0x00b3fc, 0x00b3fc, EGCB_LV },
 {0x00b3fd, 0x00b417, EGCB_LVT },
 {0x00b418, 0x00b418, EGCB_LV },
 {0x00b419, 0x00b433, EGCB_LVT },
 {0x00b434, 0x00b434, EGCB_LV },
 {0x00b435, 0x00b44f, EGCB_LVT },
 {0x00b450, 0x00b450, EGCB_LV },
 {0x00b451, 0x00b46b, EGCB_LVT },
 {0x00b46c, 0x00b46c, EGCB_LV },
 {0x00b46d, 0x00b487, EGCB_LVT },
 {0x00b488, 0x00b488, EGCB_LV },
 {0x00b489, 0x00b4a3, EGCB_LVT },
 {0x00b4a4, 0x00b4a4, EGCB_LV },
 {0x00b4a5, 0x00b4bf, EGCB_LVT },
 {0x00b4c0, 0x00b4c0, EGCB_LV },
 {0x00b4c1, 0x00b4db, EGCB_LVT },
 {0x00b4dc, 0x00b4dc, EGCB_LV },
 {0x00b4dd, 0x00b4f7, EGCB_LVT },
 {0x00b4f8, 0x00b4f8, EGCB_LV },
 {0x00b4f9, 0x00b513, EGCB_LVT },
 {0x00b514, 0x00b514, EGCB_LV },
 {0x00b515, 0x00b52f, EGCB_LVT },
 {0x00b530, 0x00b530, EGCB_LV },
 {0x00b531, 0x00b54b, EGCB_LVT },
 {0x00b54c, 0x00b54c, EGCB_LV },
 {0x00b54d, 0x00b567, EGCB_LVT },
 {0x00b568, 0x00b568, EGCB_LV },
 {0x00b569, 0x00b583, EGCB_LVT },
 {0x00b584, 0x00b584, EGCB_LV },
 {0x00b585, 0x00b59f, EGCB_LVT },
 {0x00b5a0, 0x00b5a0, EGCB_LV },
 {0x00b5a1, 0x00b5bb, EGCB_LVT },
 {0x00b5bc, 0x00b5bc, EGCB_LV },
 {0x00b5bd, 0x00b5d7, EGCB_LVT },
 {0x00b5d8, 0x00b5d8, EGCB_LV },
 {0x00b5d9, 0x00b5f3, EGCB_LVT },
 {0x00b5f4, 0x00b5f4, EGCB_LV },
 {0x00b5f5, 0x00b60f, EGCB_LVT },
 {0x00b610, 0x00b610, EGCB_LV },
 {0x00b611, 0x00b62b, EGCB_LVT },
 {0x00b62c, 0x00b62c, EGCB_LV },
 {0x00b62d, 0x00b647, EGCB_LVT },
 {0x00b648, 0x00b648, EGCB_LV },
 {0x00b649, 0x00b663, EGCB_LVT },
 {0x00b664, 0x00b664, EGCB_LV },
 {0x00b665, 0x00b67f, EGCB_LVT },
 {0x00b680, 0x00b680, EGCB_LV },
 {0x00b681, 0x00b69b, EGCB_LVT },
 {0x00b69c, 0x00b69c, EGCB_LV },
 {0x00b69d, 0x00b6b7, EGCB_LVT },
 {0x00b6b8, 0x00b6b8, EGCB_LV },
 {0x00b6b9, 0x00b6d3, EGCB_LVT },
 {0x00b6d4, 0x00b6d4, EGCB_LV },
 {0x00b6d5, 0x00b6ef, EGCB_LVT },
 {0x00b6f0, 0x00b6f0, EGCB_LV },
 {0x00b6f1, 0x00b70b, EGCB_LVT },
 {0x00b70c, 0x00b70c, EGCB_LV },
 {0x00b70d, 0x00b727, EGCB_LVT },
 {0x00b728, 0x00b728, EGCB_LV },
 {0x00b729, 0x00b743, EGCB_LVT },
 {0x00b744, 0x00b744, EGCB_LV },
 {0x00b745, 0x00b75f, EGCB_LVT },
 {0x00b760, 0x00b760, EGCB_LV },
 {0x00b761, 0x00b77b, EGCB_LVT },
 {0x00b77c, 0x00b77c, EGCB_LV },
 {0x00b77d, 0x00b797, EGCB_LVT },
 {0x00b798, 0x00b798, EGCB_LV },
 {0x00b799, 0x00b7b3, EGCB_LVT },
 {0x00b7b4, 0x00b7b4, EGCB_LV },
 {0x00b7b5, 0x00b7cf, EGCB_LVT },
 {0x00b7d0, 0x00b7d0, EGCB_LV },
 {0x00b7d1, 0x00b7eb, EGCB_LVT },
 {0x00b7ec, 0x00b7ec, EGCB_LV },
 {0x00b7ed, 0x00b807, EGCB_LVT },
 {0x00b808, 0x00b808, EGCB_LV },
 {0x00b809, 0x00b823, EGCB_LVT },
 {0x00b824, 0x00b824, EGCB_LV },
 {0x00b825, 0x00b83f, EGCB_LVT },
 {0x00b840, 0x00b840, EGCB_LV },
 {0x00b841, 0x00b85b, EGCB_LVT },
 {0x00b85c, 0x00b85c, EGCB_LV },
 {0x00b85d, 0x00b877, EGCB_LVT },
 {0x00b878, 0x00b878, EGCB_LV },
 {0x00b879, 0x00b893, EGCB_LVT },
 {0x00b894, 0x00b894, EGCB_LV },
 {0x00b895, 0x00b8af, EGCB_LVT },
 {0x00b8b0, 0x00b8b0, EGCB_LV },
 {0x00b8b1, 0x00b8cb, EGCB_LVT },
 {0x00b8cc, 0x00b8cc, EGCB_LV },
 {0x00b8cd, 0x00b8e7, EGCB_LVT },
 {0x00b8e8, 0x00b8e8, EGCB_LV },
 {0x00b8e9, 0x00b903, EGCB_LVT },
 {0x00b904, 0x00b904, EGCB_LV },
 {0x00b905, 0x00b91f, EGCB_LVT },
 {0x00b920, 0x00b920, EGCB_LV },
 {0x00b921, 0x00b93b, EGCB_LVT },
 {0x00b93c, 0x00b93c, EGCB_LV },
 {0x00b93d, 0x00b957, EGCB_LVT },
 {0x00b958, 0x00b958, EGCB_LV },
 {0x00b959, 0x00b973, EGCB_LVT },
 {0x00b974, 0x00b974, EGCB_LV },
 {0x00b975, 0x00b98f, EGCB_LVT },
 {0x00b990, 0x00b990, EGCB_LV },
 {0x00b991, 0x00b9ab, EGCB_LVT },
 {0x00b9ac, 0x00b9ac, EGCB_LV },
 {0x00b9ad, 0x00b9c7, EGCB_LVT },
 {0x00b9c8, 0x00b9c8, EGCB_LV },
 {0x00b9c9, 0x00b9e3, EGCB_LVT },
 {0x00b9e4, 0x00b9e4, EGCB_LV },
 {0x00b9e5, 0x00b9ff, EGCB_LVT },
 {0x00ba00, 0x00ba00, EGCB_LV },
 {0x00ba01, 0x00ba1b, EGCB_LVT },
 {0x00ba1c, 0x00ba1c, EGCB_LV },
 {0x00ba1d, 0x00ba37, EGCB_LVT },
 {0x00ba38, 0x00ba38, EGCB_LV },
 {0x00ba39, 0x00ba53, EGCB_LVT },
 {0x00ba54, 0x00ba54, EGCB_LV },
 {0x00ba55, 0x00ba6f, EGCB_LVT },
 {0x00ba70, 0x00ba70, EGCB_LV },
 {0x00ba71, 0x00ba8b, EGCB_LVT },
 {0x00ba8c, 0x00ba8c, EGCB_LV },
 {0x00ba8d, 0x00baa7, EGCB_LVT },
 {0x00baa8, 0x00baa8, EGCB_LV },
 {0x00baa9, 0x00bac3, EGCB_LVT },
 {0x00bac4, 0x00bac4, EGCB_LV },
 {0x00bac5, 0x00badf, EGCB_LVT },
 {0x00bae0, 0x00bae0, EGCB_LV },
 {0x00bae1, 0x00bafb, EGCB_LVT },
 {0x00bafc, 0x00bafc, EGCB_LV },
 {0x00bafd, 0x00bb17, EGCB_LVT },
 {0x00bb18, 0x00bb18, EGCB_LV },
 {0x00bb19, 0x00bb33, EGCB_LVT },
 {0x00bb34, 0x00bb34, EGCB_LV },
 {0x00bb35, 0x00bb4f, EGCB_LVT },
 {0x00bb50, 0x00bb50, EGCB_LV },
 {0x00bb51, 0x00bb6b, EGCB_LVT },
 {0x00bb6c, 0x00bb6c, EGCB_LV },
 {0x00bb6d, 0x00bb87, EGCB_LVT },
 {0x00bb88, 0x00bb88, EGCB_LV },
 {0x00bb89, 0x00bba3, EGCB_LVT },
 {0x00bba4, 0x00bba4, EGCB_LV },
 {0x00bba5, 0x00bbbf, EGCB_LVT },
 {0x00bbc0, 0x00bbc0, EGCB_LV },
 {0x00bbc1, 0x00bbdb, EGCB_LVT },
 {0x00bbdc, 0x00bbdc, EGCB_LV },
 {0x00bbdd, 0x00bbf7, EGCB_LVT },
 {0x00bbf8, 0x00bbf8, EGCB_LV },
 {0x00bbf9, 0x00bc13, EGCB_LVT },
 {0x00bc14, 0x00bc14, EGCB_LV },
 {0x00bc15, 0x00bc2f, EGCB_LVT },
 {0x00bc30, 0x00bc30, EGCB_LV },
 {0x00bc31, 0x00bc4b, EGCB_LVT },
 {0x00bc4c, 0x00bc4c, EGCB_LV },
 {0x00bc4d, 0x00bc67, EGCB_LVT },
 {0x00bc68, 0x00bc68, EGCB_LV },
 {0x00bc69, 0x00bc83, EGCB_LVT },
 {0x00bc84, 0x00bc84, EGCB_LV },
 {0x00bc85, 0x00bc9f, EGCB_LVT },
 {0x00bca0, 0x00bca0, EGCB_LV },
 {0x00bca1, 0x00bcbb, EGCB_LVT },
 {0x00bcbc, 0x00bcbc, EGCB_LV },
 {0x00bcbd, 0x00bcd7, EGCB_LVT },
 {0x00bcd8, 0x00bcd8, EGCB_LV },
 {0x00bcd9, 0x00bcf3, EGCB_LVT },
 {0x00bcf4, 0x00bcf4, EGCB_LV },
 {0x00bcf5, 0x00bd0f, EGCB_LVT },
 {0x00bd10, 0x00bd10, EGCB_LV },
 {0x00bd11, 0x00bd2b, EGCB_LVT },
 {0x00bd2c, 0x00bd2c, EGCB_LV },
 {0x00bd2d, 0x00bd47, EGCB_LVT },
 {0x00bd48, 0x00bd48, EGCB_LV },
 {0x00bd49, 0x00bd63, EGCB_LVT },
 {0x00bd64, 0x00bd64, EGCB_LV },
 {0x00bd65, 0x00bd7f, EGCB_LVT },
 {0x00bd80, 0x00bd80, EGCB_LV },
 {0x00bd81, 0x00bd9b, EGCB_LVT },
 {0x00bd9c, 0x00bd9c, EGCB_LV },
 {0x00bd9d, 0x00bdb7, EGCB_LVT },
 {0x00bdb8, 0x00bdb8, EGCB_LV },
 {0x00bdb9, 0x00bdd3, EGCB_LVT },
 {0x00bdd4, 0x00bdd4, EGCB_LV },
 {0x00bdd5, 0x00bdef, EGCB_LVT },
 {0x00bdf0, 0x00bdf0, EGCB_LV },
 {0x00bdf1, 0x00be0b, EGCB_LVT },
 {0x00be0c, 0x00be0c, EGCB_LV },
 {0x00be0d, 0x00be27, EGCB_LVT },
 {0x00be28, 0x00be28, EGCB_LV },
 {0x00be29, 0x00be43, EGCB_LVT },
 {0x00be44, 0x00be44, EGCB_LV },
 {0x00be45, 0x00be5f, EGCB_LVT },
 {0x00be60, 0x00be60, EGCB_LV },
 {0x00be61, 0x00be7b, EGCB_LVT },
 {0x00be7c, 0x00be7c, EGCB_LV },
 {0x00be7d, 0x00be97, EGCB_LVT },
 {0x00be98, 0x00be98, EGCB_LV },
 {0x00be99, 0x00beb3, EGCB_LVT },
 {0x00beb4, 0x00beb4, EGCB_LV },
 {0x00beb5, 0x00becf, EGCB_LVT },
 {0x00bed0, 0x00bed0, EGCB_LV },
 {0x00bed1, 0x00beeb, EGCB_LVT },
 {0x00beec, 0x00beec, EGCB_LV },
 {0x00beed, 0x00bf07, EGCB_LVT },
 {0x00bf08, 0x00bf08, EGCB_LV },
 {0x00bf09, 0x00bf23, EGCB_LVT },
 {0x00bf24, 0x00bf24, EGCB_LV },
 {0x00bf25, 0x00bf3f, EGCB_LVT },
 {0x00bf40, 0x00bf40, EGCB_LV },
 {0x00bf41, 0x00bf5b, EGCB_LVT },
 {0x00bf5c, 0x00bf5c, EGCB_LV },
 {0x00bf5d, 0x00bf77, EGCB_LVT },
 {0x00bf78, 0x00bf78, EGCB_LV },
 {0x00bf79, 0x00bf93, EGCB_LVT },
 {0x00bf94, 0x00bf94, EGCB_LV },
 {0x00bf95, 0x00bfaf, EGCB_LVT },
 {0x00bfb0, 0x00bfb0, EGCB_LV },
 {0x00bfb1, 0x00bfcb, EGCB_LVT },
 {0x00bfcc, 0x00bfcc, EGCB_LV },
 {0x00bfcd, 0x00bfe7, EGCB_LVT },
 {0x00bfe8, 0x00bfe8, EGCB_LV },
 {0x00bfe9, 0x00c003, EGCB_LVT },
 {0x00c004, 0x00c004, EGCB_LV },
 {0x00c005, 0x00c01f, EGCB_LVT },
 {0x00c020, 0x00c020, EGCB_LV },
 {0x00c021, 0x00c03b, EGCB_LVT },
 {0x00c03c, 0x00c03c, EGCB_LV },
 {0x00c03d, 0x00c057, EGCB_LVT },
 {0x00c058, 0x00c058, EGCB_LV },
 {0x00c059, 0x00c073, EGCB_LVT },
 {0x00c074, 0x00c074, EGCB_LV },
 {0x00c075, 0x00c08f, EGCB_LVT },
 {0x00c090, 0x00c090, EGCB_LV },
 {0x00c091, 0x00c0ab, EGCB_LVT },
 {0x00c0ac, 0x00c0ac, EGCB_LV },
 {0x00c0ad, 0x00c0c7, EGCB_LVT },
 {0x00c0c8, 0x00c0c8, EGCB_LV },
 {0x00c0c9, 0x00c0e3, EGCB_LVT },
 {0x00c0e4, 0x00c0e4, EGCB_LV },
 {0x00c0e5, 0x00c0ff, EGCB_LVT },
 {0x00c100, 0x00c100, EGCB_LV },
 {0x00c101, 0x00c11b, EGCB_LVT },
 {0x00c11c, 0x00c11c, EGCB_LV },
 {0x00c11d, 0x00c137, EGCB_LVT },
 {0x00c138, 0x00c138, EGCB_LV },
 {0x00c139, 0x00c153, EGCB_LVT },
 {0x00c154, 0x00c154, EGCB_LV },
 {0x00c155, 0x00c16f, EGCB_LVT },
 {0x00c170, 0x00c170, EGCB_LV },
 {0x00c171, 0x00c18b, EGCB_LVT },
 {0x00c18c, 0x00c18c, EGCB_LV },
 {0x00c18d, 0x00c1a7, EGCB_LVT },
 {0x00c1a8, 0x00c1a8, EGCB_LV },
 {0x00c1a9, 0x00c1c3, EGCB_LVT },
 {0x00c1c4, 0x00c1c4, EGCB_LV },
 {0x00c1c5, 0x00c1df, EGCB_LVT },
 {0x00c1e0, 0x00c1e0, EGCB_LV },
 {0x00c1e1, 0x00c1fb, EGCB_LVT },
 {0x00c1fc, 0x00c1fc, EGCB_LV },
 {0x00c1fd, 0x00c217, EGCB_LVT },
 {0x00c218, 0x00c218, EGCB_LV },
 {0x00c219, 0x00c233, EGCB_LVT },
 {0x00c234, 0x00c234, EGCB_LV },
 {0x00c235, 0x00c24f, EGCB_LVT },
 {0x00c250, 0x00c250, EGCB_LV },
 {0x00c251, 0x00c26b, EGCB_LVT },
 {0x00c26c, 0x00c26c, EGCB_LV },
 {0x00c26d, 0x00c287, EGCB_LVT },
 {0x00c288, 0x00c288, EGCB_LV },
 {0x00c289, 0x00c2a3, EGCB_LVT },
 {0x00c2a4, 0x00c2a4, EGCB_LV },
 {0x00c2a5, 0x00c2bf, EGCB_LVT },
 {0x00c2c0, 0x00c2c0, EGCB_LV },
 {0x00c2c1, 0x00c2db, EGCB_LVT },
 {0x00c2dc, 0x00c2dc, EGCB_LV },
 {0x00c2dd, 0x00c2f7, EGCB_LVT },
 {0x00c2f8, 0x00c2f8, EGCB_LV },
 {0x00c2f9, 0x00c313, EGCB_LVT },
 {0x00c314, 0x00c314, EGCB_LV },
 {0x00c315, 0x00c32f, EGCB_LVT },
 {0x00c330, 0x00c330, EGCB_LV },
 {0x00c331, 0x00c34b, EGCB_LVT },
 {0x00c34c, 0x00c34c, EGCB_LV },
 {0x00c34d, 0x00c367, EGCB_LVT },
 {0x00c368, 0x00c368, EGCB_LV },
 {0x00c369, 0x00c383, EGCB_LVT },
 {0x00c384, 0x00c384, EGCB_LV },
 {0x00c385, 0x00c39f, EGCB_LVT },
 {0x00c3a0, 0x00c3a0, EGCB_LV },
 {0x00c3a1, 0x00c3bb, EGCB_LVT },
 {0x00c3bc, 0x00c3bc, EGCB_LV },
 {0x00c3bd, 0x00c3d7, EGCB_LVT },
 {0x00c3d8, 0x00c3d8, EGCB_LV },
 {0x00c3d9, 0x00c3f3, EGCB_LVT },
 {0x00c3f4, 0x00c3f4, EGCB_LV },
 {0x00c3f5, 0x00c40f, EGCB_LVT },
 {0x00c410, 0x00c410, EGCB_LV },
 {0x00c411, 0x00c42b, EGCB_LVT },
 {0x00c42c, 0x00c42c, EGCB_LV },
 {0x00c42d, 0x00c447, EGCB_LVT },
 {0x00c448, 0x00c448, EGCB_LV },
 {0x00c449, 0x00c463, EGCB_LVT },
 {0x00c464, 0x00c464, EGCB_LV },
 {0x00c465, 0x00c47f, EGCB_LVT },
 {0x00c480, 0x00c480, EGCB_LV },
 {0x00c481, 0x00c49b, EGCB_LVT },
 {0x00c49c, 0x00c49c, EGCB_LV },
 {0x00c49d, 0x00c4b7, EGCB_LVT },
 {0x00c4b8, 0x00c4b8, EGCB_LV },
 {0x00c4b9, 0x00c4d3, EGCB_LVT },
 {0x00c4d4, 0x00c4d4, EGCB_LV },
 {0x00c4d5, 0x00c4ef, EGCB_LVT },
 {0x00c4f0, 0x00c4f0, EGCB_LV },
 {0x00c4f1, 0x00c50b, EGCB_LVT },
 {0x00c50c, 0x00c50c, EGCB_LV },
 {0x00c50d, 0x00c527, EGCB_LVT },
 {0x00c528, 0x00c528, EGCB_LV },
 {0x00c529, 0x00c543, EGCB_LVT },
 {0x00c544, 0x00c544, EGCB_LV },
 {0x00c545, 0x00c55f, EGCB_LVT },
 {0x00c560, 0x00c560, EGCB_LV },
 {0x00c561, 0x00c57b, EGCB_LVT },
 {0x00c57c, 0x00c57c, EGCB_LV },
 {0x00c57d, 0x00c597, EGCB_LVT },
 {0x00c598, 0x00c598, EGCB_LV },
 {0x00c599, 0x00c5b3, EGCB_LVT },
 {0x00c5b4, 0x00c5b4, EGCB_LV },
 {0x00c5b5, 0x00c5cf, EGCB_LVT },
 {0x00c5d0, 0x00c5d0, EGCB_LV },
 {0x00c5d1, 0x00c5eb, EGCB_LVT },
 {0x00c5ec, 0x00c5ec, EGCB_LV },
 {0x00c5ed, 0x00c607, EGCB_LVT },
 {0x00c608, 0x00c608, EGCB_LV },
 {0x00c609, 0x00c623, EGCB_LVT },
 {0x00c624, 0x00c624, EGCB_LV },
 {0x00c625, 0x00c63f, EGCB_LVT },
 {0x00c640, 0x00c640, EGCB_LV },
 {0x00c641, 0x00c65b, EGCB_LVT },
 {0x00c65c, 0x00c65c, EGCB_LV },
 {0x00c65d, 0x00c677, EGCB_LVT },
 {0x00c678, 0x00c678, EGCB_LV },
 {0x00c679, 0x00c693, EGCB_LVT },
 {0x00c694, 0x00c694, EGCB_LV },
 {0x00c695, 0x00c6af, EGCB_LVT },
 {0x00c6b0, 0x00c6b0, EGCB_LV },
 {0x00c6b1, 0x00c6cb, EGCB_LVT },
 {0x00c6cc, 0x00c6cc, EGCB_LV },
 {0x00c6cd, 0x00c6e7, EGCB_LVT },
 {0x00c6e8, 0x00c6e8, EGCB_LV },
 {0x00c6e9, 0x00c703, EGCB_LVT },
 {0x00c704, 0x00c704, EGCB_LV },
 {0x00c705, 0x00c71f, EGCB_LVT },
 {0x00c720, 0x00c720, EGCB_LV },
 {0x00c721, 0x00c73b, EGCB_LVT },
 {0x00c73c, 0x00c73c, EGCB_LV },
 {0x00c73d, 0x00c757, EGCB_LVT },
 {0x00c758, 0x00c758, EGCB_LV },
 {0x00c759, 0x00c773, EGCB_LVT },
 {0x00c774, 0x00c774, EGCB_LV },
 {0x00c775, 0x00c78f, EGCB_LVT },
 {0x00c790, 0x00c790, EGCB_LV },
 {0x00c791, 0x00c7ab, EGCB_LVT },
 {0x00c7ac, 0x00c7ac, EGCB_LV },
 {0x00c7ad, 0x00c7c7, EGCB_LVT },
 {0x00c7c8, 0x00c7c8, EGCB_LV },
 {0x00c7c9, 0x00c7e3, EGCB_LVT },
 {0x00c7e4, 0x00c7e4, EGCB_LV },
 {0x00c7e5, 0x00c7ff, EGCB_LVT },
 {0x00c800, 0x00c800, EGCB_LV },
 {0x00c801, 0x00c81b, EGCB_LVT },
 {0x00c81c, 0x00c81c, EGCB_LV },
 {0x00c81d, 0x00c837, EGCB_LVT },
 {0x00c838, 0x00c838, EGCB_LV },
 {0x00c839, 0x00c853, EGCB_LVT },
 {0x00c854, 0x00c854, EGCB_LV },
 {0x00c855, 0x00c86f, EGCB_LVT },
 {0x00c870, 0x00c870, EGCB_LV },
 {0x00c871, 0x00c88b, EGCB_LVT },
 {0x00c88c, 0x00c88c, EGCB_LV },
 {0x00c88d, 0x00c8a7, EGCB_LVT },
 {0x00c8a8, 0x00c8a8, EGCB_LV },
 {0x00c8a9, 0x00c8c3, EGCB_LVT },
 {0x00c8c4, 0x00c8c4, EGCB_LV },
 {0x00c8c5, 0x00c8df, EGCB_LVT },
 {0x00c8e0, 0x00c8e0, EGCB_LV },
 {0x00c8e1, 0x00c8fb, EGCB_LVT },
 {0x00c8fc, 0x00c8fc, EGCB_LV },
 {0x00c8fd, 0x00c917, EGCB_LVT },
 {0x00c918, 0x00c918, EGCB_LV },
 {0x00c919, 0x00c933, EGCB_LVT },
 {0x00c934, 0x00c934, EGCB_LV },
 {0x00c935, 0x00c94f, EGCB_LVT },
 {0x00c950, 0x00c950, EGCB_LV },
 {0x00c951, 0x00c96b, EGCB_LVT },
 {0x00c96c, 0x00c96c, EGCB_LV },
 {0x00c96d, 0x00c987, EGCB_LVT },
 {0x00c988, 0x00c988, EGCB_LV },
 {0x00c989, 0x00c9a3, EGCB_LVT },
 {0x00c9a4, 0x00c9a4, EGCB_LV },
 {0x00c9a5, 0x00c9bf, EGCB_LVT },
 {0x00c9c0, 0x00c9c0, EGCB_LV },
 {0x00c9c1, 0x00c9db, EGCB_LVT },
 {0x00c9dc, 0x00c9dc, EGCB_LV },
 {0x00c9dd, 0x00c9f7, EGCB_LVT },
 {0x00c9f8, 0x00c9f8, EGCB_LV },
 {0x00c9f9, 0x00ca13, EGCB_LVT },
 {0x00ca14, 0x00ca14, EGCB_LV },
 {0x00ca15, 0x00ca2f, EGCB_LVT },
 {0x00ca30, 0x00ca30, EGCB_LV },
 {0x00ca31, 0x00ca4b, EGCB_LVT },
 {0x00ca4c, 0x00ca4c, EGCB_LV },
 {0x00ca4d, 0x00ca67, EGCB_LVT },
 {0x00ca68, 0x00ca68, EGCB_LV },
 {0x00ca69, 0x00ca83, EGCB_LVT },
 {0x00ca84, 0x00ca84, EGCB_LV },
 {0x00ca85, 0x00ca9f, EGCB_LVT },
 {0x00caa0, 0x00caa0, EGCB_LV },
 {0x00caa1, 0x00cabb, EGCB_LVT },
 {0x00cabc, 0x00cabc, EGCB_LV },
 {0x00cabd, 0x00cad7, EGCB_LVT },
 {0x00cad8, 0x00cad8, EGCB_LV },
 {0x00cad9, 0x00caf3, EGCB_LVT },
 {0x00caf4, 0x00caf4, EGCB_LV },
 {0x00caf5, 0x00cb0f, EGCB_LVT },
 {0x00cb10, 0x00cb10, EGCB_LV },
 {0x00cb11, 0x00cb2b, EGCB_LVT },
 {0x00cb2c, 0x00cb2c, EGCB_LV },
 {0x00cb2d, 0x00cb47, EGCB_LVT },
 {0x00cb48, 0x00cb48, EGCB_LV },
 {0x00cb49, 0x00cb63, EGCB_LVT },
 {0x00cb64, 0x00cb64, EGCB_LV },
 {0x00cb65, 0x00cb7f, EGCB_LVT },
 {0x00cb80, 0x00cb80, EGCB_LV },
 {0x00cb81, 0x00cb9b, EGCB_LVT },
 {0x00cb9c, 0x00cb9c, EGCB_LV },
 {0x00cb9d, 0x00cbb7, EGCB_LVT },
 {0x00cbb8, 0x00cbb8, EGCB_LV },
 {0x00cbb9, 0x00cbd3, EGCB_LVT },
 {0x00cbd4, 0x00cbd4, EGCB_LV },
 {0x00cbd5, 0x00cbef, EGCB_LVT },
 {0x00cbf0, 0x00cbf0, EGCB_LV },
 {0x00cbf1, 0x00cc0b, EGCB_LVT },
 {0x00cc0c, 0x00cc0c, EGCB_LV },
 {0x00cc0d, 0x00cc27, EGCB_LVT },
 {0x00cc28, 0x00cc28, EGCB_LV },
 {0x00cc29, 0x00cc43, EGCB_LVT },
 {0x00cc44, 0x00cc44, EGCB_LV },
 {0x00cc45, 0x00cc5f, EGCB_LVT },
 {0x00cc60, 0x00cc60, EGCB_LV },
 {0x00cc61, 0x00cc7b, EGCB_LVT },
 {0x00cc7c, 0x00cc7c, EGCB_LV },
 {0x00cc7d, 0x00cc97, EGCB_LVT },
 {0x00cc98, 0x00cc98, EGCB_LV },
 {0x00cc99, 0x00ccb3, EGCB_LVT },
 {0x00ccb4, 0x00ccb4, EGCB_LV },
 {0x00ccb5, 0x00cccf, EGCB_LVT },
 {0x00ccd0, 0x00ccd0, EGCB_LV },
 {0x00ccd1, 0x00cceb, EGCB_LVT },
 {0x00ccec, 0x00ccec, EGCB_LV },
 {0x00cced, 0x00cd07, EGCB_LVT },
 {0x00cd08, 0x00cd08, EGCB_LV },
 {0x00cd09, 0x00cd23, EGCB_LVT },
 {0x00cd24, 0x00cd24, EGCB_LV },
 {0x00cd25, 0x00cd3f, EGCB_LVT },
 {0x00cd40, 0x00cd40, EGCB_LV },
 {0x00cd41, 0x00cd5b, EGCB_LVT },
 {0x00cd5c, 0x00cd5c, EGCB_LV },
 {0x00cd5d, 0x00cd77, EGCB_LVT },
 {0x00cd78, 0x00cd78, EGCB_LV },
 {0x00cd79, 0x00cd93, EGCB_LVT },
 {0x00cd94, 0x00cd94, EGCB_LV },
 {0x00cd95, 0x00cdaf, EGCB_LVT },
 {0x00cdb0, 0x00cdb0, EGCB_LV },
 {0x00cdb1, 0x00cdcb, EGCB_LVT },
 {0x00cdcc, 0x00cdcc, EGCB_LV },
 {0x00cdcd, 0x00cde7, EGCB_LVT },
 {0x00cde8, 0x00cde8, EGCB_LV },
 {0x00cde9, 0x00ce03, EGCB_LVT },
 {0x00ce04, 0x00ce04, EGCB_LV },
 {0x00ce05, 0x00ce1f, EGCB_LVT },
 {0x00ce20, 0x00ce20, EGCB_LV },
 {0x00ce21, 0x00ce3b, EGCB_LVT },
 {0x00ce3c, 0x00ce3c, EGCB_LV },
 {0x00ce3d, 0x00ce57, EGCB_LVT },
 {0x00ce58, 0x00ce58, EGCB_LV },
 {0x00ce59, 0x00ce73, EGCB_LVT },
 {0x00ce74, 0x00ce74, EGCB_LV },
 {0x00ce75, 0x00ce8f, EGCB_LVT },
 {0x00ce90, 0x00ce90, EGCB_LV },
 {0x00ce91, 0x00ceab, EGCB_LVT },
 {0x00ceac, 0x00ceac, EGCB_LV },
 {0x00cead, 0x00cec7, EGCB_LVT },
 {0x00cec8, 0x00cec8, EGCB_LV },
 {0x00cec9, 0x00cee3, EGCB_LVT },
 {0x00cee4, 0x00cee4, EGCB_LV },
 {0x00cee5, 0x00ceff, EGCB_LVT },
 {0x00cf00, 0x00cf00, EGCB_LV },
 {0x00cf01, 0x00cf1b, EGCB_LVT },
 {0x00cf1c, 0x00cf1c, EGCB_LV },
 {0x00cf1d, 0x00cf37, EGCB_LVT },
 {0x00cf38, 0x00cf38, EGCB_LV },
 {0x00cf39, 0x00cf53, EGCB_LVT },
 {0x00cf54, 0x00cf54, EGCB_LV },
 {0x00cf55, 0x00cf6f, EGCB_LVT },
 {0x00cf70, 0x00cf70, EGCB_LV },
 {0x00cf71, 0x00cf8b, EGCB_LVT },
 {0x00cf8c, 0x00cf8c, EGCB_LV },
 {0x00cf8d, 0x00cfa7, EGCB_LVT },
 {0x00cfa8, 0x00cfa8, EGCB_LV },
 {0x00cfa9, 0x00cfc3, EGCB_LVT },
 {0x00cfc4, 0x00cfc4, EGCB_LV },
 {0x00cfc5, 0x00cfdf, EGCB_LVT },
 {0x00cfe0, 0x00cfe0, EGCB_LV },
 {0x00cfe1, 0x00cffb, EGCB_LVT },
 {0x00cffc, 0x00cffc, EGCB_LV },
 {0x00cffd, 0x00d017, EGCB_LVT },
 {0x00d018, 0x00d018, EGCB_LV },
 {0x00d019, 0x00d033, EGCB_LVT },
 {0x00d034, 0x00d034, EGCB_LV },
 {0x00d035, 0x00d04f, EGCB_LVT },
 {0x00d050, 0x00d050, EGCB_LV },
 {0x00d051, 0x00d06b, EGCB_LVT },
 {0x00d06c, 0x00d06c, EGCB_LV },
 {0x00d06d, 0x00d087, EGCB_LVT },
 {0x00d088, 0x00d088, EGCB_LV },
 {0x00d089, 0x00d0a3, EGCB_LVT },
 {0x00d0a4, 0x00d0a4, EGCB_LV },
 {0x00d0a5, 0x00d0bf, EGCB_LVT },
 {0x00d0c0, 0x00d0c0, EGCB_LV },
 {0x00d0c1, 0x00d0db, EGCB_LVT },
 {0x00d0dc, 0x00d0dc, EGCB_LV },
 {0x00d0dd, 0x00d0f7, EGCB_LVT },
 {0x00d0f8, 0x00d0f8, EGCB_LV },
 {0x00d0f9, 0x00d113, EGCB_LVT },
 {0x00d114, 0x00d114, EGCB_LV },
 {0x00d115, 0x00d12f, EGCB_LVT },
 {0x00d130, 0x00d130, EGCB_LV },
 {0x00d131, 0x00d14b, EGCB_LVT },
 {0x00d14c, 0x00d14c, EGCB_LV },
 {0x00d14d, 0x00d167, EGCB_LVT },
 {0x00d168, 0x00d168, EGCB_LV },
 {0x00d169, 0x00d183, EGCB_LVT },
 {0x00d184, 0x00d184, EGCB_LV },
 {0x00d185, 0x00d19f, EGCB_LVT },
 {0x00d1a0, 0x00d1a0, EGCB_LV },
 {0x00d1a1, 0x00d1bb, EGCB_LVT },
 {0x00d1bc, 0x00d1bc, EGCB_LV },
 {0x00d1bd, 0x00d1d7, EGCB_LVT },
 {0x00d1d8, 0x00d1d8, EGCB_LV },
 {0x00d1d9, 0x00d1f3, EGCB_LVT },
 {0x00d1f4, 0x00d1f4, EGCB_LV },
 {0x00d1f5, 0x00d20f, EGCB_LVT },
 {0x00d210, 0x00d210, EGCB_LV },
 {0x00d211, 0x00d22b, EGCB_LVT },
 {0x00d22c, 0x00d22c, EGCB_LV },
 {0x00d22d, 0x00d247, EGCB_LVT },
 {0x00d248, 0x00d248, EGCB_LV },
 {0x00d249, 0x00d263, EGCB_LVT },
 {0x00d264, 0x00d264, EGCB_LV },
 {0x00d265, 0x00d27f, EGCB_LVT },
 {0x00d280, 0x00d280, EGCB_LV },
 {0x00d281, 0x00d29b, EGCB_LVT },
 {0x00d29c, 0x00d29c, EGCB_LV },
 {0x00d29d, 0x00d2b7, EGCB_LVT },
 {0x00d2b8, 0x00d2b8, EGCB_LV },
 {0x00d2b9, 0x00d2d3, EGCB_LVT },
 {0x00d2d4, 0x00d2d4, EGCB_LV },
 {0x00d2d5, 0x00d2ef, EGCB_LVT },
 {0x00d2f0, 0x00d2f0, EGCB_LV },
 {0x00d2f1, 0x00d30b, EGCB_LVT },
 {0x00d30c, 0x00d30c, EGCB_LV },
 {0x00d30d, 0x00d327, EGCB_LVT },
 {0x00d328, 0x00d328, EGCB_LV },
 {0x00d329, 0x00d343, EGCB_LVT },
 {0x00d344, 0x00d344, EGCB_LV },
 {0x00d345, 0x00d35f, EGCB_LVT },
 {0x00d360, 0x00d360, EGCB_LV },
 {0x00d361, 0x00d37b, EGCB_LVT },
 {0x00d37c, 0x00d37c, EGCB_LV },
 {0x00d37d, 0x00d397, EGCB_LVT },
 {0x00d398, 0x00d398, EGCB_LV },
 {0x00d399, 0x00d3b3, EGCB_LVT },
 {0x00d3b4, 0x00d3b4, EGCB_LV },
 {0x00d3b5, 0x00d3cf, EGCB_LVT },
 {0x00d3d0, 0x00d3d0, EGCB_LV },
 {0x00d3d1, 0x00d3eb, EGCB_LVT },
 {0x00d3ec, 0x00d3ec, EGCB_LV },
 {0x00d3ed, 0x00d407, EGCB_LVT },
 {0x00d408, 0x00d408, EGCB_LV },
 {0x00d409, 0x00d423, EGCB_LVT },
 {0x00d424, 0x00d424, EGCB_LV },
 {0x00d425, 0x00d43f, EGCB_LVT },
 {0x00d440, 0x00d440, EGCB_LV },
 {0x00d441, 0x00d45b, EGCB_LVT },
 {0x00d45c, 0x00d45c, EGCB_LV },
 {0x00d45d, 0x00d477, EGCB_LVT },
 {0x00d478, 0x00d478, EGCB_LV },
 {0x00d479, 0x00d493, EGCB_LVT },
 {0x00d494, 0x00d494, EGCB_LV },
 {0x00d495, 0x00d4af, EGCB_LVT },
 {0x00d4b0, 0x00d4b0, EGCB_LV },
 {0x00d4b1, 0x00d4cb, EGCB_LVT },
 {0x00d4cc, 0x00d4cc, EGCB_LV },
 {0x00d4cd, 0x00d4e7, EGCB_LVT },
 {0x00d4e8, 0x00d4e8, EGCB_LV },
 {0x00d4e9, 0x00d503, EGCB_LVT },
 {0x00d504, 0x00d504, EGCB_LV },
 {0x00d505, 0x00d51f, EGCB_LVT },
 {0x00d520, 0x00d520, EGCB_LV },
 {0x00d521, 0x00d53b, EGCB_LVT },
 {0x00d53c, 0x00d53c, EGCB_LV },
 {0x00d53d, 0x00d557, EGCB_LVT },
 {0x00d558, 0x00d558, EGCB_LV },
 {0x00d559, 0x00d573, EGCB_LVT },
 {0x00d574, 0x00d574, EGCB_LV },
 {0x00d575, 0x00d58f, EGCB_LVT },
 {0x00d590, 0x00d590, EGCB_LV },
 {0x00d591, 0x00d5ab, EGCB_LVT },
 {0x00d5ac, 0x00d5ac, EGCB_LV },
 {0x00d5ad, 0x00d5c7, EGCB_LVT },
 {0x00d5c8, 0x00d5c8, EGCB_LV },
 {0x00d5c9, 0x00d5e3, EGCB_LVT },
 {0x00d5e4, 0x00d5e4, EGCB_LV },
 {0x00d5e5, 0x00d5ff, EGCB_LVT },
 {0x00d600, 0x00d600, EGCB_LV },
 {0x00d601, 0x00d61b, EGCB_LVT },
 {0x00d61c, 0x00d61c, EGCB_LV },
 {0x00d61d, 0x00d637, EGCB_LVT },
 {0x00d638, 0x00d638, EGCB_LV },
 {0x00d639, 0x00d653, EGCB_LVT },
 {0x00d654, 0x00d654, EGCB_LV },
 {0x00d655, 0x00d66f, EGCB_LVT },
 {0x00d670, 0x00d670, EGCB_LV },
 {0x00d671, 0x00d68b, EGCB_LVT },
 {0x00d68c, 0x00d68c, EGCB_LV },
 {0x00d68d, 0x00d6a7, EGCB_LVT },
 {0x00d6a8, 0x00d6a8, EGCB_LV },
 {0x00d6a9, 0x00d6c3, EGCB_LVT },
 {0x00d6c4, 0x00d6c4, EGCB_LV },
 {0x00d6c5, 0x00d6df, EGCB_LVT },
 {0x00d6e0, 0x00d6e0, EGCB_LV },
 {0x00d6e1, 0x00d6fb, EGCB_LVT },
 {0x00d6fc, 0x00d6fc, EGCB_LV },
 {0x00d6fd, 0x00d717, EGCB_LVT },
 {0x00d718, 0x00d718, EGCB_LV },
 {0x00d719, 0x00d733, EGCB_LVT },
 {0x00d734, 0x00d734, EGCB_LV },
 {0x00d735, 0x00d74f, EGCB_LVT },
 {0x00d750, 0x00d750, EGCB_LV },
 {0x00d751, 0x00d76b, EGCB_LVT },
 {0x00d76c, 0x00d76c, EGCB_LV },
 {0x00d76d, 0x00d787, EGCB_LVT },
 {0x00d788, 0x00d788, EGCB_LV },
 {0x00d789, 0x00d7a3, EGCB_LVT },
 {0x00d7b0, 0x00d7c6, EGCB_V },
 {0x00d7cb, 0x00d7fb, EGCB_T },
 {0x00fb1e, 0x00fb1e, EGCB_Extend },
 {0x00fe00, 0x00fe0f, EGCB_Extend },
 {0x00fe20, 0x00fe2f, EGCB_Extend },
 {0x00feff, 0x00feff, EGCB_Control },
 {0x00ff9e, 0x00ff9f, EGCB_Extend },
 {0x00fff0, 0x00fffb, EGCB_Control },
 {0x0101fd, 0x0101fd, EGCB_Extend },
 {0x0102e0, 0x0102e0, EGCB_Extend },
 {0x010376, 0x01037a, EGCB_Extend },
 {0x010a01, 0x010a03, EGCB_Extend },
 {0x010a05, 0x010a06, EGCB_Extend },
 {0x010a0c, 0x010a0f, EGCB_Extend },
 {0x010a38, 0x010a3a, EGCB_Extend },
 {0x010a3f, 0x010a3f, EGCB_Extend },
 {0x010ae5, 0x010ae6, EGCB_Extend },
 {0x010d24, 0x010d27, EGCB_Extend },
 {0x010d69, 0x010d6d, EGCB_Extend },
 {0x010eab, 0x010eac, EGCB_Extend },
 {0x010efc, 0x010eff, EGCB_Extend },
 {0x010f46, 0x010f50, EGCB_Extend },
 {0x010f82, 0x010f85, EGCB_Extend },
 {0x011000, 0x011000, EGCB_SpacingMark },
 {0x011001, 0x011001, EGCB_Extend },
 {0x011002, 0x011002, EGCB_SpacingMark },
 {0x011038, 0x011046, EGCB_Extend },
 {0x011070, 0x011070, EGCB_Extend },
 {0x011073, 0x011074, EGCB_Extend },
 {0x01107f, 0x011081, EGCB_Extend },
 {0x011082, 0x011082, EGCB_SpacingMark },
 {0x0110b0, 0x0110b2, EGCB_SpacingMark },
 {0x0110b3, 0x0110b6, EGCB_Extend },
 {0x0110b7, 0x0110b8, EGCB_SpacingMark },
 {0x0110b9, 0x0110ba, EGCB_Extend },
 {0x0110bd, 0x0110bd, EGCB_Prepend },
 {0x0110c2, 0x0110c2, EGCB_Extend },
 {0x0110cd, 0x0110cd, EGCB_Prepend },
 {0x011100, 0x011102, EGCB_Extend },
 {0x011127, 0x01112b, EGCB_Extend },
 {0x01112c, 0x01112c, EGCB_SpacingMark },
 {0x01112d, 0x011134, EGCB_Extend },
 {0x011145, 0x011146, EGCB_SpacingMark },
 {0x011173, 0x011173, EGCB_Extend },
 {0x011180, 0x011181, EGCB_Extend },
 {0x011182, 0x011182, EGCB_SpacingMark },
 {0x0111b3, 0x0111b5, EGCB_SpacingMark },
 {0x0111b6, 0x0111be, EGCB_Extend },
 {0x0111bf, 0x0111bf, EGCB_SpacingMark },
 {0x0111c0, 0x0111c0, EGCB_Extend },
 {0x0111c2, 0x0111c3, EGCB_Prepend },
 {0x0111c9, 0x0111cc, EGCB_Extend },
 {0x0111ce, 0x0111ce, EGCB_SpacingMark },
 {0x0111cf, 0x0111cf, EGCB_Extend },
 {0x01122c, 0x01122e, EGCB_SpacingMark },
 {0x01122f, 0x011231, EGCB_Extend },
 {0x011232, 0x011233, EGCB_SpacingMark },
 {0x011234, 0x011237, EGCB_Extend },
 {0x01123e, 0x01123e, EGCB_Extend },
 {0x011241, 0x011241, EGCB_Extend },
 {0x0112df, 0x0112df, EGCB_Extend },
 {0x0112e0, 0x0112e2, EGCB_SpacingMark },
 {0x0112e3, 0x0112ea, EGCB_Extend },
 {0x011300, 0x011301, EGCB_Extend },
 {0x011302, 0x011303, EGCB_SpacingMark },
 {0x01133b, 0x01133c, EGCB_Extend },
 {0x01133e, 0x01133e, EGCB_Extend },
 {0x01133f, 0x01133f, EGCB_SpacingMark },
 {0x011340, 0x011340, EGCB_Extend },
 {0x011341, 0x011344, EGCB_SpacingMark },
 {0x011347, 0x011348, EGCB_SpacingMark },
 {0x01134b, 0x01134c, EGCB_SpacingMark },
 {0x01134d, 0x01134d, EGCB_Extend },
 {0x011357, 0x011357, EGCB_Extend },
 {0x011362, 0x011363, EGCB_SpacingMark },
 {0x011366, 0x01136c, EGCB_Extend },
 {0x011370, 0x011374, EGCB_Extend },
 {0x0113b8, 0x0113b8, EGCB_Extend },
 {0x0113b9, 0x0113ba, EGCB_SpacingMark },
 {0x0113bb, 0x0113c0, EGCB_Extend },
 {0x0113c2, 0x0113c2, EGCB_Extend },
 {0x0113c5, 0x0113c5, EGCB_Extend },
 {0x0113c7, 0x0113c9, EGCB_Extend },
 {0x0113ca, 0x0113ca, EGCB_SpacingMark },
 {0x0113cc, 0x0113cd, EGCB_SpacingMark },
 {0x0113ce, 0x0113d0, EGCB_Extend },
 {0x0113d1, 0x0113d1, EGCB_Prepend },
 {0x0113d2, 0x0113d2, EGCB_Extend },
 {0x0113e1, 0x0113e2, EGCB_Extend },
 {0x011435, 0x011437, EGCB_SpacingMark },
 {0x011438, 0x01143f, EGCB_Extend },
 {0x011440, 0x011441, EGCB_SpacingMark },
 {0x011442, 0x011444, EGCB_Extend },
 {0x011445, 0x011445, EGCB_SpacingMark },
 {0x011446, 0x011446, EGCB_Extend },
 {0x01145e, 0x01145e, EGCB_Extend },
 {0x0114b0, 0x0114b0, EGCB_Extend },
 {0x0114b1, 0x0114b2, EGCB_SpacingMark },
 {0x0114b3, 0x0114b8, EGCB_Extend },
 {0x0114b9, 0x0114b9, EGCB_SpacingMark },
 {0x0114ba, 0x0114ba, EGCB_Extend },
 {0x0114bb, 0x0114bc, EGCB_SpacingMark },
 {0x0114bd, 0x0114bd, EGCB_Extend },
 {0x0114be, 0x0114be, EGCB_SpacingMark },
 {0x0114bf, 0x0114c0, EGCB_Extend },
 {0x0114c1, 0x0114c1, EGCB_SpacingMark },
 {0x0114c2, 0x0114c3, EGCB_Extend },
 {0x0115af, 0x0115af, EGCB_Extend },
 {0x0115b0, 0x0115b1, EGCB_SpacingMark },
 {0x0115b2, 0x0115b5, EGCB_Extend },
 {0x0115b8, 0x0115bb, EGCB_SpacingMark },
 {0x0115bc, 0x0115bd, EGCB_Extend },
 {0x0115be, 0x0115be, EGCB_SpacingMark },
 {0x0115bf, 0x0115c0, EGCB_Extend },
 {0x0115dc, 0x0115dd, EGCB_Extend },
 {0x011630, 0x011632, EGCB_SpacingMark },
 {0x011633, 0x01163a, EGCB_Extend },
 {0x01163b, 0x01163c, EGCB_SpacingMark },
 {0x01163d, 0x01163d, EGCB_Extend },
 {0x01163e, 0x01163e, EGCB_SpacingMark },
 {0x01163f, 0x011640, EGCB_Extend },
 {0x0116ab, 0x0116ab, EGCB_Extend },
 {0x0116ac, 0x0116ac, EGCB_SpacingMark },
 {0x0116ad, 0x0116ad, EGCB_Extend },
 {0x0116ae, 0x0116af, EGCB_SpacingMark },
 {0x0116b0, 0x0116b7, EGCB_Extend },
 {0x01171d, 0x01171d, EGCB_Extend },
 {0x01171e, 0x01171e, EGCB_SpacingMark },
 {0x01171f, 0x01171f, EGCB_Extend },
 {0x011722, 0x011725, EGCB_Extend },
 {0x011726, 0x011726, EGCB_SpacingMark },
 {0x011727, 0x01172b, EGCB_Extend },
 {0x01182c, 0x01182e, EGCB_SpacingMark },
 {0x01182f, 0x011837, EGCB_Extend },
 {0x011838, 0x011838, EGCB_SpacingMark },
 {0x011839, 0x01183a, EGCB_Extend },
 {0x011930, 0x011930, EGCB_Extend },
 {0x011931, 0x011935, EGCB_SpacingMark },
 {0x011937, 0x011938, EGCB_SpacingMark },
 {0x01193b, 0x01193e, EGCB_Extend },
 {0x01193f, 0x01193f, EGCB_Prepend },
 {0x011940, 0x011940, EGCB_SpacingMark },
 {0x011941, 0x011941, EGCB_Prepend },
 {0x011942, 0x011942, EGCB_SpacingMark },
 {0x011943, 0x011943, EGCB_Extend },
 {0x0119d1, 0x0119d3, EGCB_SpacingMark },
 {0x0119d4, 0x0119d7, EGCB_Extend },
 {0x0119da, 0x0119db, EGCB_Extend },
 {0x0119dc, 0x0119df, EGCB_SpacingMark },
 {0x0119e0, 0x0119e0, EGCB_Extend },
 {0x0119e4, 0x0119e4, EGCB_SpacingMark },
 {0x011a01, 0x011a0a, EGCB_Extend },
 {0x011a33, 0x011a38, EGCB_Extend },
 {0x011a39, 0x011a39, EGCB_SpacingMark },
 {0x011a3a, 0x011a3a, EGCB_Prepend },
 {0x011a3b, 0x011a3e, EGCB_Extend },
 {0x011a47, 0x011a47, EGCB_Extend },
 {0x011a51, 0x011a56, EGCB_Extend },
 {0x011a57, 0x011a58, EGCB_SpacingMark },
 {0x011a59, 0x011a5b, EGCB_Extend },
 {0x011a84, 0x011a89, EGCB_Prepend },
 {0x011a8a, 0x011a96, EGCB_Extend },
 {0x011a97, 0x011a97, EGCB_SpacingMark },
 {0x011a98, 0x011a99, EGCB_Extend },
 {0x011c2f, 0x011c2f, EGCB_SpacingMark },
 {0x011c30, 0x011c36, EGCB_Extend },
 {0x011c38, 0x011c3d, EGCB_Extend },
 {0x011c3e, 0x011c3e, EGCB_SpacingMark },
 {0x011c3f, 0x011c3f, EGCB_Extend },
 {0x011c92, 0x011ca7, EGCB_Extend },
 {0x011ca9, 0x011ca9, EGCB_SpacingMark },
 {0x011caa, 0x011cb0, EGCB_Extend },
 {0x011cb1, 0x011cb1, EGCB_SpacingMark },
 {0x011cb2, 0x011cb3, EGCB_Extend },
 {0x011cb4, 0x011cb4, EGCB_SpacingMark },
 {0x011cb5, 0x011cb6, EGCB_Extend },
 {0x011d31, 0x011d36, EGCB_Extend },
 {0x011d3a, 0x011d3a, EGCB_Extend },
 {0x011d3c, 0x011d3d, EGCB_Extend },
 {0x011d3f, 0x011d45, EGCB_Extend },
 {0x011d46, 0x011d46, EGCB_Prepend },
 {0x011d47, 0x011d47, EGCB_Extend },
 {0x011d8a, 0x011d8e, EGCB_SpacingMark },
 {0x011d90, 0x011d91, EGCB_Extend },
 {0x011d93, 0x011d94, EGCB_SpacingMark },
 {0x011d95, 0x011d95, EGCB_Extend },
 {0x011d96, 0x011d96, EGCB_SpacingMark },
 {0x011d97, 0x011d97, EGCB_Extend },
 {0x011ef3, 0x011ef4, EGCB_Extend },
 {0x011ef5, 0x011ef6, EGCB_SpacingMark },
 {0x011f00, 0x011f01, EGCB_Extend },
 {0x011f02, 0x011f02, EGCB_Prepend },
 {0x011f03, 0x011f03, EGCB_SpacingMark },
 {0x011f34, 0x011f35, EGCB_SpacingMark },
 {0x011f36, 0x011f3a, EGCB_Extend },
 {0x011f3e, 0x011f3f, EGCB_SpacingMark },
 {0x011f40, 0x011f42, EGCB_Extend },
 {0x011f5a, 0x011f5a, EGCB_Extend },
 {0x013430, 0x01343f, EGCB_Control },
 {0x013440, 0x013440, EGCB_Extend },
 {0x013447, 0x013455, EGCB_Extend },
 {0x01611e, 0x016129, EGCB_Extend },
 {0x01612a, 0x01612c, EGCB_SpacingMark },
 {0x01612d, 0x01612f, EGCB_Extend },
 {0x016af0, 0x016af4, EGCB_Extend },
 {0x016b30, 0x016b36, EGCB_Extend },
 {0x016d63, 0x016d63, EGCB_V },
 {0x016d67, 0x016d6a, EGCB_V },
 {0x016f4f, 0x016f4f, EGCB_Extend },
 {0x016f51, 0x016f87, EGCB_SpacingMark },
 {0x016f8f, 0x016f92, EGCB_Extend },
 {0x016fe4, 0x016fe4, EGCB_Extend },
 {0x016ff0, 0x016ff1, EGCB_Extend },
 {0x01bc9d, 0x01bc9e, EGCB_Extend },
 {0x01bca0, 0x01bca3, EGCB_Control },
 {0x01cf00, 0x01cf2d, EGCB_Extend },
 {0x01cf30, 0x01cf46, EGCB_Extend },
 {0x01d165, 0x01d169, EGCB_Extend },
 {0x01d16d, 0x01d172, EGCB_Extend },
 {0x01d173, 0x01d17a, EGCB_Control },
 {0x01d17b, 0x01d182, EGCB_Extend },
 {0x01d185, 0x01d18b, EGCB_Extend },
 {0x01d1aa, 0x01d1ad, EGCB_Extend },
 {0x01d242, 0x01d244, EGCB_Extend },
 {0x01da00, 0x01da36, EGCB_Extend },
 {0x01da3b, 0x01da6c, EGCB_Extend },
 {0x01da75, 0x01da75, EGCB_Extend },
 {0x01da84, 0x01da84, EGCB_Extend },
 {0x01da9b, 0x01da9f, EGCB_Extend },
 {0x01daa1, 0x01daaf, EGCB_Extend },
 {0x01e000, 0x01e006, EGCB_Extend },
 {0x01e008, 0x01e018, EGCB_Extend },
 {0x01e01b, 0x01e021, EGCB_Extend },
 {0x01e023, 0x01e024, EGCB_Extend },
 {0x01e026, 0x01e02a, EGCB_Extend },
 {0x01e08f, 0x01e08f, EGCB_Extend },
 {0x01e130, 0x01e136, EGCB_Extend },
 {0x01e2ae, 0x01e2ae, EGCB_Extend },
 {0x01e2ec, 0x01e2ef, EGCB_Extend },
 {0x01e4ec, 0x01e4ef, EGCB_Extend },
 {0x01e5ee, 0x01e5ef, EGCB_Extend },
 {0x01e8d0, 0x01e8d6, EGCB_Extend },
 {0x01e944, 0x01e94a, EGCB_Extend },
 {0x01f1e6, 0x01f1ff, EGCB_Regional_Indicator },
 {0x01f3fb, 0x01f3ff, EGCB_Extend },
 {0x0e0000, 0x0e001f, EGCB_Control },
 {0x0e0020, 0x0e007f, EGCB_Extend },
 {0x0e0080, 0x0e00ff, EGCB_Control },
 {0x0e0100, 0x0e01ef, EGCB_Extend },
 {0x0e01f0, 0x0e0fff, EGCB_Control }
};
