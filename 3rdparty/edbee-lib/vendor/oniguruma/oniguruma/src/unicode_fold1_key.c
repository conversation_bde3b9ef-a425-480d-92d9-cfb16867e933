/* This file was converted by gperf_fold_key_conv.py
      from gperf output file. */
/* ANSI-C code produced by gperf version 3.1 */
/* Command-line: gperf -n -C -T -c -t -j1 -L ANSI-C -F,-1 -N onigenc_unicode_fold1_key unicode_fold1_key.gperf  */
/* Computed positions: -k'1-3' */



/* This gperf source file was generated by make_unicode_fold_data.py */

/*-
 * Copyright (c) 2017-2024  K.Kosako
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR AND CONTRIBUTORS ``AS IS'' AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR OR CONTRIBUTORS BE LIABLE
 * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 */
#include "regint.h"

#define TOTAL_KEYWORDS 1423
#define MIN_WORD_LENGTH 3
#define MAX_WORD_LENGTH 3
#define MIN_HASH_VALUE 4
#define MAX_HASH_VALUE 1846
/* maximum key range = 1843, duplicates = 0 */

#ifdef __GNUC__
__inline
#else
#ifdef __cplusplus
inline
#endif
#endif
/*ARGSUSED*/
static unsigned int
hash(OnigCodePoint codes[])
{
  static const unsigned short asso_values[] =
    {
         4,    2,   15,  124,    1,    9, 1398,  671, 1395,  664,
      1384,   84,   91,   75, 1529,   74,   99,   54, 1525,   26,
      1379,  552, 1374,  431,  144,   50, 1517,   24,  250,  557,
         0,   27, 1506,  262, 1505,  197,  131, 1310,  782,  350,
       781,   32,  771,  588,    5,   95, 1079,  463,  770,  455,
      1074, 1496,  824, 1495,  584, 1484,  762, 1465,  751, 1559,
      1372, 1339, 1350, 1365, 1347,  732, 1062,  951,  551,  701,
       543, 1334,  657, 1479,  743,  108,  724, 1549,  530,    7,
       522,  252,  503, 1541,  484, 1540,  425, 1532,  709, 1232,
       466, 1147,  666, 1138, 1041,  845, 1325,   36, 1317,  465,
        65, 1526,  301,  690,   45, 1301,  401, 1450,  381, 1437,
        25,  994,  636,  603,  591, 1281,  176,  997,  339, 1215,
       446, 1009,  571, 1175,  616,  473,  623,  386,  414, 1394,
       921, 1489, 1022,  612,  613,  173, 1002,   74,  308,  327,
      1471,  291, 1434,  202,  908,    1,  147, 1847, 1426,  226,
      1222,   73,   56, 1847, 1423,   48, 1294,  292, 1267,   14,
       275,  315,  205,  410,  990,    4,   17,    6,  974,  382,
      1255,  675, 1225,   26,  360, 1437,  886, 1268, 1189,   37,
       863,  267, 1167, 1190,  963, 1178,  936, 1133,  603,  137,
       933,  393,  897,   71,  853, 1427,  843, 1161,  831, 1111,
      1165, 1128, 1156, 1140,  830,  686, 1155, 1125,  507, 1116,
      1123,  818,  237, 1064,  576, 1046,  562, 1033,  196,  816,
       167,  815,  155,  793,  136,  131,   76,  795,   85, 1112,
       124, 1110,  116,  285,  232, 1026,  188, 1383,  371, 1087,
       350, 1045,  333, 1359,  812,  354,  270,  407,  801,   99,
       565,  555, 1104,  128, 1097,   31, 1419,  130, 1414
    };
  return asso_values[(unsigned char)onig_codes_byte_at(codes, 2)+3] + asso_values[(unsigned char)onig_codes_byte_at(codes, 1)] + asso_values[(unsigned char)onig_codes_byte_at(codes, 0)];
}

int
onigenc_unicode_fold1_key(OnigCodePoint codes[])
{
  static const short int wordlist[] =
    {
      -1, -1, -1, -1,

      1915,

      1876,

      210,

      171,

      3453,

      1936,

      3558,

      231,

      993,

      1321,

      1282,

      3153,

      2669,

      3714,

      1342,

      544,

      505,

      2105,

      1141,

      390,

      562,

      3693,

      2804,

      3309,

      3711,

      2036,

      1069,

      324,

      2249,

      27,

      1612,

      3249,

      1618,

      3732,

      1384,

      159,

      2252,

      1047,

      3405,

      402,

      664,

      2723,

      3096,

      1615,

      3750,

      2027,

      1060,

      315,

      4131,

      12,

      2732,

      3240,

      1636,

      2270,

      1366,

      3681,

      2099,

      1120,

      372,

      2306,

      649,

      2783,

      3087,

      1654,

      2432,

      2020,

      1053,

      309,

      4113,

      0,

      2729,

      3234,

      2381,

      3132,

      1354,

      3591,

      2195,

      1231,

      459,

      3288,

      640,

      2894,

      3081,

      1465,

      3597,

      2198,

      1234,

      462,

      4101,

      80,

      2897,

      1888,

      2369,

      183,

      3456,

      2993,

      2915,

      1696,

      3660,

      1894,

      1294,

      189,

      1795,

      144,

      2918,

      3366,

      517,

      3546,

      1300,

      980,

      273,

      1801,

      2447,

      2657,

      523,

      3609,

      2204,

      1240,

      468,

      2984,

      92,

      2960,

      2996,

      3603,

      2201,

      1237,

      465,

      3672,

      86,

      2900,

      3588,

      1924,

      153,

      219,

      165,

      3585,

      2192,

      1228,

      456,

      3393,

      1330,

      2891,

      1813,

      2450,

      1906,

      2978,

      201,

      2090,

      1111,

      2954,

      1807,

      2246,

      2774,

      1312,

      3579,

      2189,

      1225,

      1792,

      2291,

      535,

      2888,

      3363,

      1789,

      1684,

      3123,

      3876,

      3573,

      2186,

      1222,

      2948,

      2942,

      2285,

      2885,

      3360,

      3882,

      1513,

      2045,

      1078,

      333,

      2936,

      46,

      1783,

      1459,
      -1,

      1519,

      1402,

      2939,

      3621,

      2210,

      1246,

      474,

      673,

      105,

      1777,

      2951,

      798,

      2183,

      1219,

      1582,

      4149,

      863,

      2882,

      3357,

      2393,

      366,

      3894,

      1135,

      2552,

      3279,

      866,

      2798,

      3303,

      3702,

      3888,

      1825,

      1531,

      2558,

      709,

      4095,

      2912,

      3873,
      -1,

      1771,

      1525,

      2288,

      3870,

      3011,

      1594,

      369,

      1600,

      1510,

      3615,

      2207,

      1243,

      471,

      1507,

      99,

      2174,

      1210,

      2975,

      872,

      712,

      2873,

      3354,

      3864,
      -1,

      3996,

      2570,

      869,

      3939,

      1912,

      3564,

      207,

      999,

      1501,

      2564,

      3858,

      2675,

      1819,

      1318,

      860,

      784,

      2549,

      1753,

      2441,

      541,

      1495,

      2546,
      -1,

      3651,

      2225,

      1261,

      486,

      4092,

      135,

      2909,

      1132,

      3906,

      4089,

      857,

      2795,

      3300,

      3696,

      3411,

      3612,

      3852,

      2540,

      1543,

      4215,

      96,

      715,

      854,

      3008,

      1660,

      378,

      1489,

      1855,

      4083,

      2534,

      3687,

      721,

      2024,

      1056,

      312,
      -1,

      6,

      703,

      3237,

      2078,

      4077,

      1360,

      1816,

      878,

      2762,

      3276,

      4206,

      643,

      384,

      3084,

      2582,

      851,

      3900,

      3699,

      694,

      4107,

      3111,

      3834,

      2528,

      2375,

      363,

      718,

      1537,

      3639,

      2219,

      1255,

      483,

      1471,

      123,

      4071,

      2048,

      1081,

      336,

      697,

      52,

      2747,

      2987,
      -1, -1,

      1408,

      3633,

      2216,

      1252,

      480,

      3648,

      117,
      -1,

      875,

      3002,

      132,

      1843,

      2117,

      1153,

      4155,

      2576,
      -1,

      2816,

      2399,

      3735,

      2510,

      1573,

      3627,

      2213,

      1249,

      477,

      3897,

      111,

      1837,

      2300,

      4218,

      4053,

      1852,

      2033,

      1066,

      321,

      1534,

      4296,

      1639,

      3246,

      348,

      3720,

      1378,

      754,

      3261,
      -1,

      893,

      1435,

      658,

      1831,

      3093,
      -1,

      3780,

      2030,

      1063,

      318,

      4125,

      18,

      3654,

      3243,

      1624,

      4182,

      1372,

      138,

      3945,

      2426,

      2063,

      1096,

      2573,

      3705,

      3090,

      1690,
      -1, -1,

      3924,

      1438,

      4119,

      1999,

      1014,

      288,

      2387,

      682,

      2690,

      3213,

      1561,

      1858,
      -1,

      2504,

      1606,

      4185,

      2459,

      3918,

      616,

      2429,

      3060,

      3933,
      -1,

      3020,

      2051,

      1084,

      339,

      1555,

      59,
      -1,

      2357,

      1570,

      3468,

      1414,

      3426,

      887,

      2282,

      3972,

      3912,

      679,

      3462,

      724,
      -1, -1,

      2005,

      1026,

      294,

      4161,

      1549,

      2702,

      3219,

      2405,

      884,

      345,
      -1,

      74,

      890,

      3258,

      637,

      625,

      1429,

      3066,

      1996,

      1008,

      285,

      3975,

      4098,

      2684,

      3210,
      -1,

      2366,

      2363,
      -1,

      881,

      4176,

      3438,
      -1,

      610,

      2420,

      3057,

      3567,

      1993,

      1002,

      282,

      1576,

      2168,

      2678,

      3207,

      733,

      2354,

      2867,

      3351,

      2453,

      3420,
      -1,

      791,

      604,

      3951,

      3054,

      3561,

      1990,

      996,

      279,
      -1,

      2972,

      2672,

      3204,

      3555,

      1987,

      990,

      276,

      1741,

      3414,

      2666,

      3201,

      601,

      736,

      3051,
      -1,

      1864,

      3531,

      1975,

      965,

      598,

      3966,

      3047,

      2642,

      3189,

      3525,

      1972,

      956,

      3408,

      3666,
      -1,

      2636,

      3186,

      1873,

      147,

      3035,

      3402,

      2180,

      1216,

      3663,

      2231,

      1267,

      2879,

      3032,
      -1,

      2345,

      2054,

      1087,

      342,

      3378,

      65,

      2177,

      1213,

      2339,

      2276,

      1420,

      2876,

      3372,

      3483,

      1951,

      912,

      243,

      3450,

      1765,

      2594,

      3165,

      2042,

      1075,

      330,

      4167,

      40,

      3822,

      3255,

      2411,

      577,

      1396,

      2465,

      1759,

      2138,

      1174,

      411,

      670,

      37,

      2837,

      3327,

      3774,

      2321,

      1393,

      2072,

      4143,

      357,

      2057,

      1090,

      2756,

      3270,

      71,

      1453,

      1456,

      2060,

      1093,

      1426,

      4140,

      688,

      691,

      1681,

      3105,

      845,

      1432,

      4200,
      -1, -1,

      2039,

      1072,

      327,

      4173,

      34,
      -1,

      3252,

      2417,

      3957,

      1390,

      4179,

      2966,

      2492,

      4041,

      2423,

      3846,

      2990,

      3099,

      1588,
      -1,

      3537,

      1978,

      971,

      4137,
      -1,

      1483,

      2648,

      3192,

      1585,

      3840,

      2008,

      1032,

      297,
      -1, -1,

      2708,

      3222,
      -1,

      3038,

      1477,

      2495,

      396,
      -1,

      896,
      -1,

      628,

      3726,

      3069,

      2351,

      3990,

      3993,

      1198,

      3384,

      3963,
      -1,

      2264,

      3348,

      2522,

      9,

      727,

      3969,

      3444,

      2258,

      1363,

      3528,

      1630,

      960,

      264,

      4065,

      646,

      2639,

      2516,

      2471,

      2002,

      1020,

      291,

      1732,

      4110,

      2696,

      3216,

      589,

      2378,

      4059,
      -1, -1, -1, -1,

      3549,

      1984,

      983,

      3063,

      779,

      2342,

      2660,

      3198,

      3516,

      3375,

      946,

      258,

      2486,

      2360,

      2627,

      4230,

      595,

      3432,

      3044,

      3543,

      1981,

      977,
      -1,

      4224,

      730,

      2654,

      3195,

      3495,

      1957,

      925,

      249,
      -1,

      3396,

      2606,

      3171,

      592,

      2930,

      3041,

      3489,

      1954,

      919,

      246,
      -1,

      2924,

      2600,

      3168,

      3465,

      1942,

      1933,

      237,

      228,

      3390,

      3813,

      3159,

      3150,

      2333,

      1348,

      1339,

      1930,

      1927,

      225,

      222,

      568,

      559,

      3147,

      3144,

      2327,

      1336,

      1333,

      3582,
      -1,

      3594,

      453,

      556,

      553,

      2309,

      77,

      3657,

      2228,

      1264,

      490,
      -1,

      141,
      -1, -1,

      2303,

      2297,

      836,

      3645,

      2222,

      1258,

      3576,

      3570,

      129,

      450,

      447,

      1786,

      435,

      1798,

      2444,

      3477,

      1948,

      905,

      4293,

      1861,

      4032,

      2588,

      2162,

      2153,

      424,

      2477,

      4287,

      2861,

      2852,

      3342,

      1849,

      574,
      -1,

      1780,

      1774,

      2150,

      1750,

      2438,

      1041,
      -1,

      2849,

      3339,

      2717,

      2315,
      -1,

      2147,

      1183,

      417,

      1729,

      1711,

      2846,

      3336,

      634,
      -1, -1,

      2126,

      1162,

      3029,

      3026,

      4245,

      2825,

      1705,

      3753,

      4212,
      -1, -1, -1, -1,

      3023,

      3017,

      2483,

      1699,
      -1, -1,

      3867,
      -1,

      3879,

      2489,

      2120,

      1156,

      399,

      1657,

      2435,

      2819,

      1504,

      3741,

      1516,
      -1, -1,

      2144,

      1180,

      414,

      1579,

      3930,

      2843,

      3333,

      3861,

      3855,
      -1,

      3831,

      2087,

      1108,
      -1,

      1567,

      1645,

      2771,

      1498,

      1492,
      -1,

      1468,
      -1,

      3810,

      3792,

      2066,

      1099,

      1693,

      2543,

      3120,

      2555,

      3264,
      -1, -1,

      1444,
      -1,

      3786,

      2141,

      1177,

      4086,

      2135,

      1171,

      2840,

      3330,

      3777,

      2834,

      3324,

      3768,

      4191,
      -1,

      2537,

      2531,
      -1,

      2507,

      3522,
      -1,

      953,

      261,

      832,

      812,

      2633,

      4080,

      4074,

      1687,

      4050,
      -1,

      1675,

      2132,

      1168,
      -1,

      586,

      808,

      2831,

      3321,

      3762,

      1870,

      4029,

      4011,

      2108,

      1144,

      393,

      801,

      2336,

      2807,

      3312,

      3717,
      -1, -1,

      4290,

      4005,
      -1,

      751,
      -1,

      1669,

      2102,

      1138,

      387,
      -1,

      3981,

      2801,

      3306,

      3708,

      31,

      2744,

      1621,

      49,

      2075,

      1387,

      360,
      -1,

      1405,

      2759,

      3273,

      667,

      742,

      1462,

      676,

      62,

      2750,

      4278,

      1609,

      4134,

      1417,

      3108,

      4152,

      795,

      2069,

      1102,

      2396,

      3618,
      -1,

      2753,

      3267,
      -1,

      102,

      1450,

      4164,
      -1,

      4257,

      444,

      2408,
      -1, -1,

      3102,
      -1,

      2011,

      1038,

      300,

      3636,

      4251,

      2714,

      3225,

      441,

      120,

      2906,
      -1,

      1822,

      4227,

      4209,
      -1,

      631,

      788,

      3072,

      1768,

      771,

      3519,

      1969,

      950,

      4203,

      4197,

      438,

      2630,

      3183,
      -1,

      3942,

      1840,

      1762,

      3471,

      1945,

      3999,

      240,

      583,

      3459,

      1939,

      3162,

      234,

      3954,

      1351,
      -1,

      3156,

      3630,

      764,

      1345,

      571,

      1756,

      114,

      2903,
      -1,

      565,

      3987,

      3675,

      2237,

      1273,

      496,

      3369,

      156,
      -1,

      3669,

      2234,

      1270,

      493,

      4239,

      150,

      3606,
      -1,

      3600,

      1189,

      1834,

      89,

      3903,

      83,

      1204,

      432,
      -1, -1, -1,

      3849,

      2171,

      1207,

      1540,

      1201,

      428,

      2870,

      1192,

      420,
      -1,

      1486,
      -1,

      3921,

      3843,

      1810,

      1714,

      1804,

      1035,

      3771,

      1195,

      1744,

      2711,

      1558,

      1480,

      3345,
      -1,

      1029,

      1747,
      -1,

      1738,

      2705,

      3837,

      1720,

      2165,

      2159,

      2579,
      -1,

      1678,

      2864,

      2858,

      1186,

      1474,

      2525,

      2156,

      1726,

      2129,

      1165,

      3447,

      2855,
      -1,

      2828,

      1867,

      3756,

      4068,

      3915,

      2519,

      3441,

      68,

      408,

      1735,

      1723,

      2501,

      1423,

      3765,

      1552,

      1708,

      4062,

      2123,

      1159,

      1717,

      405,

      1663,

      2822,

      2513,

      3747,

      3759,

      4170,

      3891,

      3795,

      3885,

      2414,

      1591,

      1672,

      3825,

      4056,
      -1, -1,

      1528,
      -1,

      1522,

      3828,
      -1,

      3819,

      1651,

      1666,

      3801,
      -1,

      56,
      -1,

      899,

      2096,

      1117,

      1411,

      2114,

      1150,

      2780,

      3285,

      3807,

      2813,

      3318,

      3729,

      1023,

      4284,

      816,
      -1,

      2699,

      4158,

      3129,

      848,

      2567,

      2402,

      2561,

      3816,

      3804,
      -1,

      622,

      3960,

      842,

      3789,

      1633,

      822,

      4014,

      3798,

      2111,

      1147,

      774,

      4044,

      2462,

      2810,

      3315,

      3723,

      3435,

      829,

      4047,
      -1,

      4038,

      1129,

      381,

      4020,

      2474,

      2792,

      3297,

      3690,

      3744,
      -1, -1, -1,

      839,

      826,

      1627,

      4026,
      -1,

      3141,

      43,
      -1, -1,

      3948,

      819,

      1399,

      758,
      -1, -1,

      1648,

      1126,

      375,

      4035,

      4023,

      2789,

      3294,

      3684,

      768,

      4008,

      4146,

      15,

      2735,

      4017,

      2390,
      -1,

      1369,

      3138,
      -1,

      748,

      761,
      -1,

      652,

      2017,

      1050,

      306,
      -1, -1,

      2726,

      3231,

      4116,

      2014,

      1044,

      303,

      2384,
      -1,

      2720,

      3228,
      -1,

      3534,

      3078,

      968,

      267,

      2294,

      3504,

      2645,

      934,

      252,

      3075,
      -1,

      2615,

      4281,

      3513,

      1966,

      943,

      3507,

      1963,

      937,

      2624,

      3180,

      3936,

      2618,

      3177,

      4233,

      3642,
      -1, -1,

      2348,

      4221,

      126,

      3510,

      3381,

      940,

      255,
      -1, -1,

      2621,

      3501,

      1960,

      931,

      1903,
      -1,

      198,

      2612,

      3174,

      1900,

      580,

      195,

      3624,

      1309,

      1885,

      1846,

      180,

      108,

      1306,

      532,
      -1, -1,

      745,

      1291,

      529,

      1882,

      351,

      177,

      1879,

      514,

      174,

      2279,
      -1,

      1441,

      1288,

      3014,

      2273,

      1285,
      -1,

      1828,

      511,

      2267,
      -1,

      508,

      2243,

      1279,

      502,

      4188,

      168,

      2240,

      1276,

      499,

      2261,

      162,

      1123,

      2255,

      2093,

      1114,

      2786,

      3291,

      3678,

      2777,

      3282,
      -1,

      2084,

      1105,
      -1,

      2480,
      -1,

      2768,

      3135,

      24,

      2741,

      3126,

      3738,
      -1,

      1381,
      -1,

      3927,

      706,
      -1,

      3117,

      661,

      1702,

      21,

      2738,
      -1, -1,

      1564,

      1375,

      4128,
      -1, -1,

      1642,

      3492,

      655,

      922,

      3978,
      -1,

      2969,

      2603,

      2081,

      3909,

      4122,

      2963,
      -1,

      2765,

      2468,

      3540,

      2933,

      974,

      270,

      1546,

      3486,

      2651,

      915,

      700,
      -1,

      3114,

      2597,

      2927,

      354,

      2330,

      2921,

      3480,

      3474,

      908,

      902,

      1447,
      -1,

      2591,

      2585,
      -1, -1,

      685,

      1921,

      1918,

      216,

      213,
      -1,

      3387,

      2324,
      -1,

      1603,

      1327,

      1324,

      3783,

      1909,

      1597,

      204,

      550,

      547,

      2318,

      2312,
      -1,

      1897,

      1315,

      192,
      -1,

      1891,

      3,

      186,

      538,

      1017,

      1303,

      1357,
      -1,

      2693,

      1297,
      -1,

      526,

      1011,

      1005,

      2456,

      520,

      2687,

      2681,

      619,

      3552,

      4104,

      986,

      804,
      -1,

      2372,

      2663,

      613,

      607,
      -1,

      3498,
      -1,

      928,

      739,

      3984,

      3429,

      2609,
      -1, -1, -1, -1,

      4002,
      -1,

      3423,

      3417,
      -1, -1, -1, -1, -1, -1,
      -1,

      3399,
      -1, -1, -1, -1, -1, -1,
      -1, -1, -1, -1, -1, -1,

      4194,
      -1, -1, -1, -1, -1, -1,

      3005,

      2999,
      -1, -1, -1, -1, -1, -1,
      -1, -1, -1, -1,

      2981,
      -1, -1, -1, -1, -1, -1,
      -1,

      2957,
      -1,

      4266,
      -1,

      2945,
      -1, -1, -1, -1, -1,

      4275,
      -1, -1,

      4269,
      -1, -1, -1, -1, -1, -1,
      -1, -1, -1, -1, -1, -1,
      -1, -1,

      4272,
      -1, -1, -1, -1, -1, -1,

      4263,

      2498,
      -1, -1, -1, -1, -1, -1,
      -1, -1, -1, -1, -1, -1,
      -1, -1, -1, -1, -1, -1,
      -1, -1, -1, -1, -1, -1,
      -1, -1, -1, -1, -1, -1,
      -1, -1, -1, -1, -1, -1,
      -1, -1, -1, -1, -1, -1,
      -1, -1, -1, -1, -1, -1,
      -1, -1, -1, -1, -1, -1,
      -1, -1, -1, -1, -1, -1,
      -1, -1, -1, -1, -1, -1,
      -1, -1, -1, -1, -1, -1,
      -1, -1, -1, -1, -1, -1,
      -1, -1, -1, -1, -1, -1,
      -1, -1, -1, -1, -1, -1,
      -1,

      4254,
      -1, -1, -1, -1, -1, -1,
      -1, -1, -1, -1, -1, -1,
      -1, -1, -1, -1, -1, -1,

      4248,
      -1, -1, -1, -1, -1, -1,
      -1, -1, -1, -1,

      4242,

      4236,
      -1, -1, -1, -1, -1, -1,
      -1, -1, -1, -1, -1, -1,
      -1, -1, -1, -1, -1, -1,
      -1, -1, -1, -1, -1, -1,
      -1, -1, -1, -1, -1, -1,
      -1, -1, -1, -1, -1, -1,
      -1, -1, -1, -1, -1, -1,
      -1, -1, -1, -1, -1, -1,
      -1, -1, -1, -1, -1, -1,
      -1, -1, -1, -1, -1, -1,
      -1, -1,

      4260
    };


    {
      int key = hash(codes);

      if (key <= MAX_HASH_VALUE)
        {
          int index = wordlist[key];

          if (index >= 0 && onig_codes_cmp(codes, OnigUnicodeFolds1 + index, 1) == 0)
            return index;
        }
    }
  return -1;
}
