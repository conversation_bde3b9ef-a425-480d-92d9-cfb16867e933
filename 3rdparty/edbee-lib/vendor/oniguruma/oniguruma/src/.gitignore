Makefile
autom4te.cache/
ltmain.sh
stamp-h1
configure
config.status
config.h
config.h.in
onig-config
libtool
aclocal.m4
Makefile.in
.python-version
*.o
*.obj
*.so
*.lo
*.la
*.pc
*.log
*.trs
*.dll
*.lib
*.exe
*.exp
*.gcno
*.gcda
*.gcov
*~
.libs/
.deps/
/build
/onig-*.tar.gz
m4/*.m4
/coverage
/coverage.info
/fuzzers
/.vscode

# src/
/src/unicode_fold?_key.gperf
/src/unicode_unfold_key.gperf
/src/UNICODE_PROPERTIES
/src/*.txt
/src/mktable

# test/
/test/test_utf8
/test/test_options
/test/testc
/test/testcu
/test/testp
/test/test_regset
/test/test_syntax
/test/test_back
/test/kofu-utf8.txt

# sample/
/sample/crnl
/sample/encode
/sample/listcap
/sample/names
/sample/posix
/sample/simple
/sample/sql
/sample/syntax
/sample/user_property
/sample/callout
/sample/echo
/sample/count
/sample/bug_fix
/sample/regset
/sample/scan
/sample/callback_each_match
/sample/log*

/harnesses/utf16*.dict
/harnesses/fuzzer-*
/harnesses/read-*
/harnesses/libfuzzer-onig
/harnesses/libfuzzer-onig-full
/harnesses/slow-unit-*
/harnesses/timeout-*
/harnesses/crash-*
/harnesses/oom-*

!config.h
