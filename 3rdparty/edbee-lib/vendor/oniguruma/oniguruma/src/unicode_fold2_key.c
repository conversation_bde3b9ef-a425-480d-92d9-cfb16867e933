/* This file was converted by gperf_fold_key_conv.py
      from gperf output file. */
/* ANSI-C code produced by gperf version 3.1 */
/* Command-line: gperf -n -C -T -c -t -j1 -L ANSI-C -F,-1 -N onigenc_unicode_fold2_key unicode_fold2_key.gperf  */
/* Computed positions: -k'3,6' */



/* This gperf source file was generated by make_unicode_fold_data.py */

/*-
 * Copyright (c) 2017-2024  K.Kosako
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR AND CONTRIBUTORS ``AS IS'' AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR OR CONTRIBUTORS BE LIABLE
 * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 */
#include "regint.h"

#define TOTAL_KEYWORDS 59
#define MIN_WORD_LENGTH 6
#define MAX_WORD_LENGTH 6
#define MIN_HASH_VALUE 0
#define MAX_HASH_VALUE 58
/* maximum key range = 59, duplicates = 0 */

#ifdef __GNUC__
__inline
#else
#ifdef __cplusplus
inline
#endif
#endif
/*ARGSUSED*/
static unsigned int
hash(OnigCodePoint codes[])
{
  static const unsigned char asso_values[] =
    {
      58, 57, 56, 55, 54, 53, 52, 16, 50, 59,
      15, 59, 25, 59, 59, 59, 59, 59, 59,  3,
      59, 59, 59, 59, 59, 59, 59, 59, 59, 59,
      59, 59, 49, 48, 47, 46, 45, 44, 43, 42,
      59, 59, 59, 59, 59, 59, 59, 59, 59, 21,
      59, 59, 59, 59, 59, 59, 59, 59, 59, 59,
      59, 59, 59, 59, 59, 59,  2, 59, 59, 59,
      59, 59, 59, 59, 59, 59, 59, 59, 59, 59,
      59, 59, 59, 59, 59, 59, 59, 59, 59, 59,
      59, 59, 59, 59, 59, 59, 40, 20, 39, 38,
      37, 14,  5, 36, 20,  7, 25, 34, 29, 32,
      16, 59, 31, 59, 59,  2,  1, 59, 25, 15,
      59, 14, 59, 59, 28, 59,  2, 59, 59, 59,
      11, 59, 59, 59, 59, 59, 59, 59, 59, 59,
      59, 59, 59, 59, 59, 59, 59, 59, 59, 59,
      59, 59, 59, 59, 59, 59, 59, 59, 59, 59,
      59, 59, 59, 59, 59, 59, 59, 59, 59, 59,
      59, 59, 24, 59, 22, 59, 59, 11, 59, 59,
      59, 59, 59,  7, 59,  0, 59, 59, 16, 59,
       1, 59, 59, 16, 59, 59, 59, 15, 59, 59,
      59,  6, 59, 59, 59, 59,  0, 59, 59, 59,
      59, 59, 59, 59, 59, 59, 59, 59, 59, 59,
      59, 59, 59, 59, 59, 59, 59, 59, 59, 59,
      59, 59, 59, 59, 59, 59, 59, 59, 59, 59,
      59, 59, 59, 59, 59, 59, 59, 59, 59, 59,
      59, 59, 59, 59, 59, 59
    };
  return asso_values[(unsigned char)onig_codes_byte_at(codes, 5)] + asso_values[(unsigned char)onig_codes_byte_at(codes, 2)];
}

int
onigenc_unicode_fold2_key(OnigCodePoint codes[])
{
  static const short int wordlist[] =
    {

      101,

      253,

      76,

      29,

      24,

      239,

      96,

      71,

      92,

      67,

      4,

      62,

      8,

      58,

      234,

      109,

      164,

      88,

      84,

      80,

      214,

      0,

      54,

      261,

      50,

      105,

      121,

      125,

      257,

      42,

      38,

      249,

      46,

      117,

      12,

      113,

      244,

      229,

      224,

      219,

      209,

      16,

      204,

      199,

      194,

      189,

      184,

      179,

      174,

      169,

      20,

      34,

      159,

      154,

      149,

      144,

      139,

      134,

      129
    };


    {
      int key = hash(codes);

      if (key <= MAX_HASH_VALUE)
        {
          int index = wordlist[key];

          if (index >= 0 && onig_codes_cmp(codes, OnigUnicodeFolds2 + index, 2) == 0)
            return index;
        }
    }
  return -1;
}
