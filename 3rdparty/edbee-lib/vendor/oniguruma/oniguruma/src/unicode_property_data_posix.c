/* ANSI-C code produced by gperf version 3.1 */
/* Command-line: gperf -T -C -c -t -j1 -L ANSI-C --ignore-case --pic -Q unicode_prop_name_pool -N unicode_lookup_property_name --output-file gperf2.tmp unicode_property_data_posix.gperf  */
/* Computed positions: -k'1,3' */

#if !((' ' == 32) && ('!' == 33) && ('"' == 34) && ('#' == 35) \
      && ('%' == 37) && ('&' == 38) && ('\'' == 39) && ('(' == 40) \
      && (')' == 41) && ('*' == 42) && ('+' == 43) && (',' == 44) \
      && ('-' == 45) && ('.' == 46) && ('/' == 47) && ('0' == 48) \
      && ('1' == 49) && ('2' == 50) && ('3' == 51) && ('4' == 52) \
      && ('5' == 53) && ('6' == 54) && ('7' == 55) && ('8' == 56) \
      && ('9' == 57) && (':' == 58) && (';' == 59) && ('<' == 60) \
      && ('=' == 61) && ('>' == 62) && ('?' == 63) && ('A' == 65) \
      && ('B' == 66) && ('C' == 67) && ('D' == 68) && ('E' == 69) \
      && ('F' == 70) && ('G' == 71) && ('H' == 72) && ('I' == 73) \
      && ('J' == 74) && ('K' == 75) && ('L' == 76) && ('M' == 77) \
      && ('N' == 78) && ('O' == 79) && ('P' == 80) && ('Q' == 81) \
      && ('R' == 82) && ('S' == 83) && ('T' == 84) && ('U' == 85) \
      && ('V' == 86) && ('W' == 87) && ('X' == 88) && ('Y' == 89) \
      && ('Z' == 90) && ('[' == 91) && ('\\' == 92) && (']' == 93) \
      && ('^' == 94) && ('_' == 95) && ('a' == 97) && ('b' == 98) \
      && ('c' == 99) && ('d' == 100) && ('e' == 101) && ('f' == 102) \
      && ('g' == 103) && ('h' == 104) && ('i' == 105) && ('j' == 106) \
      && ('k' == 107) && ('l' == 108) && ('m' == 109) && ('n' == 110) \
      && ('o' == 111) && ('p' == 112) && ('q' == 113) && ('r' == 114) \
      && ('s' == 115) && ('t' == 116) && ('u' == 117) && ('v' == 118) \
      && ('w' == 119) && ('x' == 120) && ('y' == 121) && ('z' == 122) \
      && ('{' == 123) && ('|' == 124) && ('}' == 125) && ('~' == 126))
/* The character set is not based on ISO-646.  */
#error "gperf generated tables don't work with this execution character set. Please report a bug to <<EMAIL>>."
#endif



/* Generated by make_unicode_property_data.py. */

/*-
 * Copyright (c) 2016-2024  K.Kosako
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR AND CONTRIBUTORS ``AS IS'' AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR OR CONTRIBUTORS BE LIABLE
 * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 */


/* PROPERTY: 'NEWLINE': POSIX [[:NEWLINE:]] */
static const OnigCodePoint
CR_NEWLINE[] = { 1,
0x000a, 0x000a,
}; /* END of CR_NEWLINE */

/* PROPERTY: 'Alpha': POSIX [[:Alpha:]] */
static const OnigCodePoint
CR_Alpha[] = { 757,
0x0041, 0x005a,
0x0061, 0x007a,
0x00aa, 0x00aa,
0x00b5, 0x00b5,
0x00ba, 0x00ba,
0x00c0, 0x00d6,
0x00d8, 0x00f6,
0x00f8, 0x02c1,
0x02c6, 0x02d1,
0x02e0, 0x02e4,
0x02ec, 0x02ec,
0x02ee, 0x02ee,
0x0345, 0x0345,
0x0363, 0x0374,
0x0376, 0x0377,
0x037a, 0x037d,
0x037f, 0x037f,
0x0386, 0x0386,
0x0388, 0x038a,
0x038c, 0x038c,
0x038e, 0x03a1,
0x03a3, 0x03f5,
0x03f7, 0x0481,
0x048a, 0x052f,
0x0531, 0x0556,
0x0559, 0x0559,
0x0560, 0x0588,
0x05b0, 0x05bd,
0x05bf, 0x05bf,
0x05c1, 0x05c2,
0x05c4, 0x05c5,
0x05c7, 0x05c7,
0x05d0, 0x05ea,
0x05ef, 0x05f2,
0x0610, 0x061a,
0x0620, 0x0657,
0x0659, 0x065f,
0x066e, 0x06d3,
0x06d5, 0x06dc,
0x06e1, 0x06e8,
0x06ed, 0x06ef,
0x06fa, 0x06fc,
0x06ff, 0x06ff,
0x0710, 0x073f,
0x074d, 0x07b1,
0x07ca, 0x07ea,
0x07f4, 0x07f5,
0x07fa, 0x07fa,
0x0800, 0x0817,
0x081a, 0x082c,
0x0840, 0x0858,
0x0860, 0x086a,
0x0870, 0x0887,
0x0889, 0x088e,
0x0897, 0x0897,
0x08a0, 0x08c9,
0x08d4, 0x08df,
0x08e3, 0x08e9,
0x08f0, 0x093b,
0x093d, 0x094c,
0x094e, 0x0950,
0x0955, 0x0963,
0x0971, 0x0983,
0x0985, 0x098c,
0x098f, 0x0990,
0x0993, 0x09a8,
0x09aa, 0x09b0,
0x09b2, 0x09b2,
0x09b6, 0x09b9,
0x09bd, 0x09c4,
0x09c7, 0x09c8,
0x09cb, 0x09cc,
0x09ce, 0x09ce,
0x09d7, 0x09d7,
0x09dc, 0x09dd,
0x09df, 0x09e3,
0x09f0, 0x09f1,
0x09fc, 0x09fc,
0x0a01, 0x0a03,
0x0a05, 0x0a0a,
0x0a0f, 0x0a10,
0x0a13, 0x0a28,
0x0a2a, 0x0a30,
0x0a32, 0x0a33,
0x0a35, 0x0a36,
0x0a38, 0x0a39,
0x0a3e, 0x0a42,
0x0a47, 0x0a48,
0x0a4b, 0x0a4c,
0x0a51, 0x0a51,
0x0a59, 0x0a5c,
0x0a5e, 0x0a5e,
0x0a70, 0x0a75,
0x0a81, 0x0a83,
0x0a85, 0x0a8d,
0x0a8f, 0x0a91,
0x0a93, 0x0aa8,
0x0aaa, 0x0ab0,
0x0ab2, 0x0ab3,
0x0ab5, 0x0ab9,
0x0abd, 0x0ac5,
0x0ac7, 0x0ac9,
0x0acb, 0x0acc,
0x0ad0, 0x0ad0,
0x0ae0, 0x0ae3,
0x0af9, 0x0afc,
0x0b01, 0x0b03,
0x0b05, 0x0b0c,
0x0b0f, 0x0b10,
0x0b13, 0x0b28,
0x0b2a, 0x0b30,
0x0b32, 0x0b33,
0x0b35, 0x0b39,
0x0b3d, 0x0b44,
0x0b47, 0x0b48,
0x0b4b, 0x0b4c,
0x0b56, 0x0b57,
0x0b5c, 0x0b5d,
0x0b5f, 0x0b63,
0x0b71, 0x0b71,
0x0b82, 0x0b83,
0x0b85, 0x0b8a,
0x0b8e, 0x0b90,
0x0b92, 0x0b95,
0x0b99, 0x0b9a,
0x0b9c, 0x0b9c,
0x0b9e, 0x0b9f,
0x0ba3, 0x0ba4,
0x0ba8, 0x0baa,
0x0bae, 0x0bb9,
0x0bbe, 0x0bc2,
0x0bc6, 0x0bc8,
0x0bca, 0x0bcc,
0x0bd0, 0x0bd0,
0x0bd7, 0x0bd7,
0x0c00, 0x0c0c,
0x0c0e, 0x0c10,
0x0c12, 0x0c28,
0x0c2a, 0x0c39,
0x0c3d, 0x0c44,
0x0c46, 0x0c48,
0x0c4a, 0x0c4c,
0x0c55, 0x0c56,
0x0c58, 0x0c5a,
0x0c5d, 0x0c5d,
0x0c60, 0x0c63,
0x0c80, 0x0c83,
0x0c85, 0x0c8c,
0x0c8e, 0x0c90,
0x0c92, 0x0ca8,
0x0caa, 0x0cb3,
0x0cb5, 0x0cb9,
0x0cbd, 0x0cc4,
0x0cc6, 0x0cc8,
0x0cca, 0x0ccc,
0x0cd5, 0x0cd6,
0x0cdd, 0x0cde,
0x0ce0, 0x0ce3,
0x0cf1, 0x0cf3,
0x0d00, 0x0d0c,
0x0d0e, 0x0d10,
0x0d12, 0x0d3a,
0x0d3d, 0x0d44,
0x0d46, 0x0d48,
0x0d4a, 0x0d4c,
0x0d4e, 0x0d4e,
0x0d54, 0x0d57,
0x0d5f, 0x0d63,
0x0d7a, 0x0d7f,
0x0d81, 0x0d83,
0x0d85, 0x0d96,
0x0d9a, 0x0db1,
0x0db3, 0x0dbb,
0x0dbd, 0x0dbd,
0x0dc0, 0x0dc6,
0x0dcf, 0x0dd4,
0x0dd6, 0x0dd6,
0x0dd8, 0x0ddf,
0x0df2, 0x0df3,
0x0e01, 0x0e3a,
0x0e40, 0x0e46,
0x0e4d, 0x0e4d,
0x0e81, 0x0e82,
0x0e84, 0x0e84,
0x0e86, 0x0e8a,
0x0e8c, 0x0ea3,
0x0ea5, 0x0ea5,
0x0ea7, 0x0eb9,
0x0ebb, 0x0ebd,
0x0ec0, 0x0ec4,
0x0ec6, 0x0ec6,
0x0ecd, 0x0ecd,
0x0edc, 0x0edf,
0x0f00, 0x0f00,
0x0f40, 0x0f47,
0x0f49, 0x0f6c,
0x0f71, 0x0f83,
0x0f88, 0x0f97,
0x0f99, 0x0fbc,
0x1000, 0x1036,
0x1038, 0x1038,
0x103b, 0x103f,
0x1050, 0x108f,
0x109a, 0x109d,
0x10a0, 0x10c5,
0x10c7, 0x10c7,
0x10cd, 0x10cd,
0x10d0, 0x10fa,
0x10fc, 0x1248,
0x124a, 0x124d,
0x1250, 0x1256,
0x1258, 0x1258,
0x125a, 0x125d,
0x1260, 0x1288,
0x128a, 0x128d,
0x1290, 0x12b0,
0x12b2, 0x12b5,
0x12b8, 0x12be,
0x12c0, 0x12c0,
0x12c2, 0x12c5,
0x12c8, 0x12d6,
0x12d8, 0x1310,
0x1312, 0x1315,
0x1318, 0x135a,
0x1380, 0x138f,
0x13a0, 0x13f5,
0x13f8, 0x13fd,
0x1401, 0x166c,
0x166f, 0x167f,
0x1681, 0x169a,
0x16a0, 0x16ea,
0x16ee, 0x16f8,
0x1700, 0x1713,
0x171f, 0x1733,
0x1740, 0x1753,
0x1760, 0x176c,
0x176e, 0x1770,
0x1772, 0x1773,
0x1780, 0x17b3,
0x17b6, 0x17c8,
0x17d7, 0x17d7,
0x17dc, 0x17dc,
0x1820, 0x1878,
0x1880, 0x18aa,
0x18b0, 0x18f5,
0x1900, 0x191e,
0x1920, 0x192b,
0x1930, 0x1938,
0x1950, 0x196d,
0x1970, 0x1974,
0x1980, 0x19ab,
0x19b0, 0x19c9,
0x1a00, 0x1a1b,
0x1a20, 0x1a5e,
0x1a61, 0x1a74,
0x1aa7, 0x1aa7,
0x1abf, 0x1ac0,
0x1acc, 0x1ace,
0x1b00, 0x1b33,
0x1b35, 0x1b43,
0x1b45, 0x1b4c,
0x1b80, 0x1ba9,
0x1bac, 0x1baf,
0x1bba, 0x1be5,
0x1be7, 0x1bf1,
0x1c00, 0x1c36,
0x1c4d, 0x1c4f,
0x1c5a, 0x1c7d,
0x1c80, 0x1c8a,
0x1c90, 0x1cba,
0x1cbd, 0x1cbf,
0x1ce9, 0x1cec,
0x1cee, 0x1cf3,
0x1cf5, 0x1cf6,
0x1cfa, 0x1cfa,
0x1d00, 0x1dbf,
0x1dd3, 0x1df4,
0x1e00, 0x1f15,
0x1f18, 0x1f1d,
0x1f20, 0x1f45,
0x1f48, 0x1f4d,
0x1f50, 0x1f57,
0x1f59, 0x1f59,
0x1f5b, 0x1f5b,
0x1f5d, 0x1f5d,
0x1f5f, 0x1f7d,
0x1f80, 0x1fb4,
0x1fb6, 0x1fbc,
0x1fbe, 0x1fbe,
0x1fc2, 0x1fc4,
0x1fc6, 0x1fcc,
0x1fd0, 0x1fd3,
0x1fd6, 0x1fdb,
0x1fe0, 0x1fec,
0x1ff2, 0x1ff4,
0x1ff6, 0x1ffc,
0x2071, 0x2071,
0x207f, 0x207f,
0x2090, 0x209c,
0x2102, 0x2102,
0x2107, 0x2107,
0x210a, 0x2113,
0x2115, 0x2115,
0x2119, 0x211d,
0x2124, 0x2124,
0x2126, 0x2126,
0x2128, 0x2128,
0x212a, 0x212d,
0x212f, 0x2139,
0x213c, 0x213f,
0x2145, 0x2149,
0x214e, 0x214e,
0x2160, 0x2188,
0x24b6, 0x24e9,
0x2c00, 0x2ce4,
0x2ceb, 0x2cee,
0x2cf2, 0x2cf3,
0x2d00, 0x2d25,
0x2d27, 0x2d27,
0x2d2d, 0x2d2d,
0x2d30, 0x2d67,
0x2d6f, 0x2d6f,
0x2d80, 0x2d96,
0x2da0, 0x2da6,
0x2da8, 0x2dae,
0x2db0, 0x2db6,
0x2db8, 0x2dbe,
0x2dc0, 0x2dc6,
0x2dc8, 0x2dce,
0x2dd0, 0x2dd6,
0x2dd8, 0x2dde,
0x2de0, 0x2dff,
0x2e2f, 0x2e2f,
0x3005, 0x3007,
0x3021, 0x3029,
0x3031, 0x3035,
0x3038, 0x303c,
0x3041, 0x3096,
0x309d, 0x309f,
0x30a1, 0x30fa,
0x30fc, 0x30ff,
0x3105, 0x312f,
0x3131, 0x318e,
0x31a0, 0x31bf,
0x31f0, 0x31ff,
0x3400, 0x4dbf,
0x4e00, 0xa48c,
0xa4d0, 0xa4fd,
0xa500, 0xa60c,
0xa610, 0xa61f,
0xa62a, 0xa62b,
0xa640, 0xa66e,
0xa674, 0xa67b,
0xa67f, 0xa6ef,
0xa717, 0xa71f,
0xa722, 0xa788,
0xa78b, 0xa7cd,
0xa7d0, 0xa7d1,
0xa7d3, 0xa7d3,
0xa7d5, 0xa7dc,
0xa7f2, 0xa805,
0xa807, 0xa827,
0xa840, 0xa873,
0xa880, 0xa8c3,
0xa8c5, 0xa8c5,
0xa8f2, 0xa8f7,
0xa8fb, 0xa8fb,
0xa8fd, 0xa8ff,
0xa90a, 0xa92a,
0xa930, 0xa952,
0xa960, 0xa97c,
0xa980, 0xa9b2,
0xa9b4, 0xa9bf,
0xa9cf, 0xa9cf,
0xa9e0, 0xa9ef,
0xa9fa, 0xa9fe,
0xaa00, 0xaa36,
0xaa40, 0xaa4d,
0xaa60, 0xaa76,
0xaa7a, 0xaabe,
0xaac0, 0xaac0,
0xaac2, 0xaac2,
0xaadb, 0xaadd,
0xaae0, 0xaaef,
0xaaf2, 0xaaf5,
0xab01, 0xab06,
0xab09, 0xab0e,
0xab11, 0xab16,
0xab20, 0xab26,
0xab28, 0xab2e,
0xab30, 0xab5a,
0xab5c, 0xab69,
0xab70, 0xabea,
0xac00, 0xd7a3,
0xd7b0, 0xd7c6,
0xd7cb, 0xd7fb,
0xf900, 0xfa6d,
0xfa70, 0xfad9,
0xfb00, 0xfb06,
0xfb13, 0xfb17,
0xfb1d, 0xfb28,
0xfb2a, 0xfb36,
0xfb38, 0xfb3c,
0xfb3e, 0xfb3e,
0xfb40, 0xfb41,
0xfb43, 0xfb44,
0xfb46, 0xfbb1,
0xfbd3, 0xfd3d,
0xfd50, 0xfd8f,
0xfd92, 0xfdc7,
0xfdf0, 0xfdfb,
0xfe70, 0xfe74,
0xfe76, 0xfefc,
0xff21, 0xff3a,
0xff41, 0xff5a,
0xff66, 0xffbe,
0xffc2, 0xffc7,
0xffca, 0xffcf,
0xffd2, 0xffd7,
0xffda, 0xffdc,
0x10000, 0x1000b,
0x1000d, 0x10026,
0x10028, 0x1003a,
0x1003c, 0x1003d,
0x1003f, 0x1004d,
0x10050, 0x1005d,
0x10080, 0x100fa,
0x10140, 0x10174,
0x10280, 0x1029c,
0x102a0, 0x102d0,
0x10300, 0x1031f,
0x1032d, 0x1034a,
0x10350, 0x1037a,
0x10380, 0x1039d,
0x103a0, 0x103c3,
0x103c8, 0x103cf,
0x103d1, 0x103d5,
0x10400, 0x1049d,
0x104b0, 0x104d3,
0x104d8, 0x104fb,
0x10500, 0x10527,
0x10530, 0x10563,
0x10570, 0x1057a,
0x1057c, 0x1058a,
0x1058c, 0x10592,
0x10594, 0x10595,
0x10597, 0x105a1,
0x105a3, 0x105b1,
0x105b3, 0x105b9,
0x105bb, 0x105bc,
0x105c0, 0x105f3,
0x10600, 0x10736,
0x10740, 0x10755,
0x10760, 0x10767,
0x10780, 0x10785,
0x10787, 0x107b0,
0x107b2, 0x107ba,
0x10800, 0x10805,
0x10808, 0x10808,
0x1080a, 0x10835,
0x10837, 0x10838,
0x1083c, 0x1083c,
0x1083f, 0x10855,
0x10860, 0x10876,
0x10880, 0x1089e,
0x108e0, 0x108f2,
0x108f4, 0x108f5,
0x10900, 0x10915,
0x10920, 0x10939,
0x10980, 0x109b7,
0x109be, 0x109bf,
0x10a00, 0x10a03,
0x10a05, 0x10a06,
0x10a0c, 0x10a13,
0x10a15, 0x10a17,
0x10a19, 0x10a35,
0x10a60, 0x10a7c,
0x10a80, 0x10a9c,
0x10ac0, 0x10ac7,
0x10ac9, 0x10ae4,
0x10b00, 0x10b35,
0x10b40, 0x10b55,
0x10b60, 0x10b72,
0x10b80, 0x10b91,
0x10c00, 0x10c48,
0x10c80, 0x10cb2,
0x10cc0, 0x10cf2,
0x10d00, 0x10d27,
0x10d4a, 0x10d65,
0x10d69, 0x10d69,
0x10d6f, 0x10d85,
0x10e80, 0x10ea9,
0x10eab, 0x10eac,
0x10eb0, 0x10eb1,
0x10ec2, 0x10ec4,
0x10efc, 0x10efc,
0x10f00, 0x10f1c,
0x10f27, 0x10f27,
0x10f30, 0x10f45,
0x10f70, 0x10f81,
0x10fb0, 0x10fc4,
0x10fe0, 0x10ff6,
0x11000, 0x11045,
0x11071, 0x11075,
0x11080, 0x110b8,
0x110c2, 0x110c2,
0x110d0, 0x110e8,
0x11100, 0x11132,
0x11144, 0x11147,
0x11150, 0x11172,
0x11176, 0x11176,
0x11180, 0x111bf,
0x111c1, 0x111c4,
0x111ce, 0x111cf,
0x111da, 0x111da,
0x111dc, 0x111dc,
0x11200, 0x11211,
0x11213, 0x11234,
0x11237, 0x11237,
0x1123e, 0x11241,
0x11280, 0x11286,
0x11288, 0x11288,
0x1128a, 0x1128d,
0x1128f, 0x1129d,
0x1129f, 0x112a8,
0x112b0, 0x112e8,
0x11300, 0x11303,
0x11305, 0x1130c,
0x1130f, 0x11310,
0x11313, 0x11328,
0x1132a, 0x11330,
0x11332, 0x11333,
0x11335, 0x11339,
0x1133d, 0x11344,
0x11347, 0x11348,
0x1134b, 0x1134c,
0x11350, 0x11350,
0x11357, 0x11357,
0x1135d, 0x11363,
0x11380, 0x11389,
0x1138b, 0x1138b,
0x1138e, 0x1138e,
0x11390, 0x113b5,
0x113b7, 0x113c0,
0x113c2, 0x113c2,
0x113c5, 0x113c5,
0x113c7, 0x113ca,
0x113cc, 0x113cd,
0x113d1, 0x113d1,
0x113d3, 0x113d3,
0x11400, 0x11441,
0x11443, 0x11445,
0x11447, 0x1144a,
0x1145f, 0x11461,
0x11480, 0x114c1,
0x114c4, 0x114c5,
0x114c7, 0x114c7,
0x11580, 0x115b5,
0x115b8, 0x115be,
0x115d8, 0x115dd,
0x11600, 0x1163e,
0x11640, 0x11640,
0x11644, 0x11644,
0x11680, 0x116b5,
0x116b8, 0x116b8,
0x11700, 0x1171a,
0x1171d, 0x1172a,
0x11740, 0x11746,
0x11800, 0x11838,
0x118a0, 0x118df,
0x118ff, 0x11906,
0x11909, 0x11909,
0x1190c, 0x11913,
0x11915, 0x11916,
0x11918, 0x11935,
0x11937, 0x11938,
0x1193b, 0x1193c,
0x1193f, 0x11942,
0x119a0, 0x119a7,
0x119aa, 0x119d7,
0x119da, 0x119df,
0x119e1, 0x119e1,
0x119e3, 0x119e4,
0x11a00, 0x11a32,
0x11a35, 0x11a3e,
0x11a50, 0x11a97,
0x11a9d, 0x11a9d,
0x11ab0, 0x11af8,
0x11bc0, 0x11be0,
0x11c00, 0x11c08,
0x11c0a, 0x11c36,
0x11c38, 0x11c3e,
0x11c40, 0x11c40,
0x11c72, 0x11c8f,
0x11c92, 0x11ca7,
0x11ca9, 0x11cb6,
0x11d00, 0x11d06,
0x11d08, 0x11d09,
0x11d0b, 0x11d36,
0x11d3a, 0x11d3a,
0x11d3c, 0x11d3d,
0x11d3f, 0x11d41,
0x11d43, 0x11d43,
0x11d46, 0x11d47,
0x11d60, 0x11d65,
0x11d67, 0x11d68,
0x11d6a, 0x11d8e,
0x11d90, 0x11d91,
0x11d93, 0x11d96,
0x11d98, 0x11d98,
0x11ee0, 0x11ef6,
0x11f00, 0x11f10,
0x11f12, 0x11f3a,
0x11f3e, 0x11f40,
0x11fb0, 0x11fb0,
0x12000, 0x12399,
0x12400, 0x1246e,
0x12480, 0x12543,
0x12f90, 0x12ff0,
0x13000, 0x1342f,
0x13441, 0x13446,
0x13460, 0x143fa,
0x14400, 0x14646,
0x16100, 0x1612e,
0x16800, 0x16a38,
0x16a40, 0x16a5e,
0x16a70, 0x16abe,
0x16ad0, 0x16aed,
0x16b00, 0x16b2f,
0x16b40, 0x16b43,
0x16b63, 0x16b77,
0x16b7d, 0x16b8f,
0x16d40, 0x16d6c,
0x16e40, 0x16e7f,
0x16f00, 0x16f4a,
0x16f4f, 0x16f87,
0x16f8f, 0x16f9f,
0x16fe0, 0x16fe1,
0x16fe3, 0x16fe3,
0x16ff0, 0x16ff1,
0x17000, 0x187f7,
0x18800, 0x18cd5,
0x18cff, 0x18d08,
0x1aff0, 0x1aff3,
0x1aff5, 0x1affb,
0x1affd, 0x1affe,
0x1b000, 0x1b122,
0x1b132, 0x1b132,
0x1b150, 0x1b152,
0x1b155, 0x1b155,
0x1b164, 0x1b167,
0x1b170, 0x1b2fb,
0x1bc00, 0x1bc6a,
0x1bc70, 0x1bc7c,
0x1bc80, 0x1bc88,
0x1bc90, 0x1bc99,
0x1bc9e, 0x1bc9e,
0x1d400, 0x1d454,
0x1d456, 0x1d49c,
0x1d49e, 0x1d49f,
0x1d4a2, 0x1d4a2,
0x1d4a5, 0x1d4a6,
0x1d4a9, 0x1d4ac,
0x1d4ae, 0x1d4b9,
0x1d4bb, 0x1d4bb,
0x1d4bd, 0x1d4c3,
0x1d4c5, 0x1d505,
0x1d507, 0x1d50a,
0x1d50d, 0x1d514,
0x1d516, 0x1d51c,
0x1d51e, 0x1d539,
0x1d53b, 0x1d53e,
0x1d540, 0x1d544,
0x1d546, 0x1d546,
0x1d54a, 0x1d550,
0x1d552, 0x1d6a5,
0x1d6a8, 0x1d6c0,
0x1d6c2, 0x1d6da,
0x1d6dc, 0x1d6fa,
0x1d6fc, 0x1d714,
0x1d716, 0x1d734,
0x1d736, 0x1d74e,
0x1d750, 0x1d76e,
0x1d770, 0x1d788,
0x1d78a, 0x1d7a8,
0x1d7aa, 0x1d7c2,
0x1d7c4, 0x1d7cb,
0x1df00, 0x1df1e,
0x1df25, 0x1df2a,
0x1e000, 0x1e006,
0x1e008, 0x1e018,
0x1e01b, 0x1e021,
0x1e023, 0x1e024,
0x1e026, 0x1e02a,
0x1e030, 0x1e06d,
0x1e08f, 0x1e08f,
0x1e100, 0x1e12c,
0x1e137, 0x1e13d,
0x1e14e, 0x1e14e,
0x1e290, 0x1e2ad,
0x1e2c0, 0x1e2eb,
0x1e4d0, 0x1e4eb,
0x1e5d0, 0x1e5ed,
0x1e5f0, 0x1e5f0,
0x1e7e0, 0x1e7e6,
0x1e7e8, 0x1e7eb,
0x1e7ed, 0x1e7ee,
0x1e7f0, 0x1e7fe,
0x1e800, 0x1e8c4,
0x1e900, 0x1e943,
0x1e947, 0x1e947,
0x1e94b, 0x1e94b,
0x1ee00, 0x1ee03,
0x1ee05, 0x1ee1f,
0x1ee21, 0x1ee22,
0x1ee24, 0x1ee24,
0x1ee27, 0x1ee27,
0x1ee29, 0x1ee32,
0x1ee34, 0x1ee37,
0x1ee39, 0x1ee39,
0x1ee3b, 0x1ee3b,
0x1ee42, 0x1ee42,
0x1ee47, 0x1ee47,
0x1ee49, 0x1ee49,
0x1ee4b, 0x1ee4b,
0x1ee4d, 0x1ee4f,
0x1ee51, 0x1ee52,
0x1ee54, 0x1ee54,
0x1ee57, 0x1ee57,
0x1ee59, 0x1ee59,
0x1ee5b, 0x1ee5b,
0x1ee5d, 0x1ee5d,
0x1ee5f, 0x1ee5f,
0x1ee61, 0x1ee62,
0x1ee64, 0x1ee64,
0x1ee67, 0x1ee6a,
0x1ee6c, 0x1ee72,
0x1ee74, 0x1ee77,
0x1ee79, 0x1ee7c,
0x1ee7e, 0x1ee7e,
0x1ee80, 0x1ee89,
0x1ee8b, 0x1ee9b,
0x1eea1, 0x1eea3,
0x1eea5, 0x1eea9,
0x1eeab, 0x1eebb,
0x1f130, 0x1f149,
0x1f150, 0x1f169,
0x1f170, 0x1f189,
0x20000, 0x2a6df,
0x2a700, 0x2b739,
0x2b740, 0x2b81d,
0x2b820, 0x2cea1,
0x2ceb0, 0x2ebe0,
0x2ebf0, 0x2ee5d,
0x2f800, 0x2fa1d,
0x30000, 0x3134a,
0x31350, 0x323af,
}; /* END of CR_Alpha */

/* PROPERTY: 'Blank': POSIX [[:Blank:]] */
static const OnigCodePoint
CR_Blank[] = { 8,
0x0009, 0x0009,
0x0020, 0x0020,
0x00a0, 0x00a0,
0x1680, 0x1680,
0x2000, 0x200a,
0x202f, 0x202f,
0x205f, 0x205f,
0x3000, 0x3000,
}; /* END of CR_Blank */

/* PROPERTY: 'Cntrl': POSIX [[:Cntrl:]] */
static const OnigCodePoint
CR_Cntrl[] = { 2,
0x0000, 0x001f,
0x007f, 0x009f,
}; /* END of CR_Cntrl */

/* PROPERTY: 'Digit': POSIX [[:Digit:]] */
static const OnigCodePoint
CR_Digit[] = { 71,
0x0030, 0x0039,
0x0660, 0x0669,
0x06f0, 0x06f9,
0x07c0, 0x07c9,
0x0966, 0x096f,
0x09e6, 0x09ef,
0x0a66, 0x0a6f,
0x0ae6, 0x0aef,
0x0b66, 0x0b6f,
0x0be6, 0x0bef,
0x0c66, 0x0c6f,
0x0ce6, 0x0cef,
0x0d66, 0x0d6f,
0x0de6, 0x0def,
0x0e50, 0x0e59,
0x0ed0, 0x0ed9,
0x0f20, 0x0f29,
0x1040, 0x1049,
0x1090, 0x1099,
0x17e0, 0x17e9,
0x1810, 0x1819,
0x1946, 0x194f,
0x19d0, 0x19d9,
0x1a80, 0x1a89,
0x1a90, 0x1a99,
0x1b50, 0x1b59,
0x1bb0, 0x1bb9,
0x1c40, 0x1c49,
0x1c50, 0x1c59,
0xa620, 0xa629,
0xa8d0, 0xa8d9,
0xa900, 0xa909,
0xa9d0, 0xa9d9,
0xa9f0, 0xa9f9,
0xaa50, 0xaa59,
0xabf0, 0xabf9,
0xff10, 0xff19,
0x104a0, 0x104a9,
0x10d30, 0x10d39,
0x10d40, 0x10d49,
0x11066, 0x1106f,
0x110f0, 0x110f9,
0x11136, 0x1113f,
0x111d0, 0x111d9,
0x112f0, 0x112f9,
0x11450, 0x11459,
0x114d0, 0x114d9,
0x11650, 0x11659,
0x116c0, 0x116c9,
0x116d0, 0x116e3,
0x11730, 0x11739,
0x118e0, 0x118e9,
0x11950, 0x11959,
0x11bf0, 0x11bf9,
0x11c50, 0x11c59,
0x11d50, 0x11d59,
0x11da0, 0x11da9,
0x11f50, 0x11f59,
0x16130, 0x16139,
0x16a60, 0x16a69,
0x16ac0, 0x16ac9,
0x16b50, 0x16b59,
0x16d70, 0x16d79,
0x1ccf0, 0x1ccf9,
0x1d7ce, 0x1d7ff,
0x1e140, 0x1e149,
0x1e2f0, 0x1e2f9,
0x1e4f0, 0x1e4f9,
0x1e5f1, 0x1e5fa,
0x1e950, 0x1e959,
0x1fbf0, 0x1fbf9,
}; /* END of CR_Digit */

/* PROPERTY: 'Graph': POSIX [[:Graph:]] */
static const OnigCodePoint
CR_Graph[] = { 737,
0x0021, 0x007e,
0x00a1, 0x0377,
0x037a, 0x037f,
0x0384, 0x038a,
0x038c, 0x038c,
0x038e, 0x03a1,
0x03a3, 0x052f,
0x0531, 0x0556,
0x0559, 0x058a,
0x058d, 0x058f,
0x0591, 0x05c7,
0x05d0, 0x05ea,
0x05ef, 0x05f4,
0x0600, 0x070d,
0x070f, 0x074a,
0x074d, 0x07b1,
0x07c0, 0x07fa,
0x07fd, 0x082d,
0x0830, 0x083e,
0x0840, 0x085b,
0x085e, 0x085e,
0x0860, 0x086a,
0x0870, 0x088e,
0x0890, 0x0891,
0x0897, 0x0983,
0x0985, 0x098c,
0x098f, 0x0990,
0x0993, 0x09a8,
0x09aa, 0x09b0,
0x09b2, 0x09b2,
0x09b6, 0x09b9,
0x09bc, 0x09c4,
0x09c7, 0x09c8,
0x09cb, 0x09ce,
0x09d7, 0x09d7,
0x09dc, 0x09dd,
0x09df, 0x09e3,
0x09e6, 0x09fe,
0x0a01, 0x0a03,
0x0a05, 0x0a0a,
0x0a0f, 0x0a10,
0x0a13, 0x0a28,
0x0a2a, 0x0a30,
0x0a32, 0x0a33,
0x0a35, 0x0a36,
0x0a38, 0x0a39,
0x0a3c, 0x0a3c,
0x0a3e, 0x0a42,
0x0a47, 0x0a48,
0x0a4b, 0x0a4d,
0x0a51, 0x0a51,
0x0a59, 0x0a5c,
0x0a5e, 0x0a5e,
0x0a66, 0x0a76,
0x0a81, 0x0a83,
0x0a85, 0x0a8d,
0x0a8f, 0x0a91,
0x0a93, 0x0aa8,
0x0aaa, 0x0ab0,
0x0ab2, 0x0ab3,
0x0ab5, 0x0ab9,
0x0abc, 0x0ac5,
0x0ac7, 0x0ac9,
0x0acb, 0x0acd,
0x0ad0, 0x0ad0,
0x0ae0, 0x0ae3,
0x0ae6, 0x0af1,
0x0af9, 0x0aff,
0x0b01, 0x0b03,
0x0b05, 0x0b0c,
0x0b0f, 0x0b10,
0x0b13, 0x0b28,
0x0b2a, 0x0b30,
0x0b32, 0x0b33,
0x0b35, 0x0b39,
0x0b3c, 0x0b44,
0x0b47, 0x0b48,
0x0b4b, 0x0b4d,
0x0b55, 0x0b57,
0x0b5c, 0x0b5d,
0x0b5f, 0x0b63,
0x0b66, 0x0b77,
0x0b82, 0x0b83,
0x0b85, 0x0b8a,
0x0b8e, 0x0b90,
0x0b92, 0x0b95,
0x0b99, 0x0b9a,
0x0b9c, 0x0b9c,
0x0b9e, 0x0b9f,
0x0ba3, 0x0ba4,
0x0ba8, 0x0baa,
0x0bae, 0x0bb9,
0x0bbe, 0x0bc2,
0x0bc6, 0x0bc8,
0x0bca, 0x0bcd,
0x0bd0, 0x0bd0,
0x0bd7, 0x0bd7,
0x0be6, 0x0bfa,
0x0c00, 0x0c0c,
0x0c0e, 0x0c10,
0x0c12, 0x0c28,
0x0c2a, 0x0c39,
0x0c3c, 0x0c44,
0x0c46, 0x0c48,
0x0c4a, 0x0c4d,
0x0c55, 0x0c56,
0x0c58, 0x0c5a,
0x0c5d, 0x0c5d,
0x0c60, 0x0c63,
0x0c66, 0x0c6f,
0x0c77, 0x0c8c,
0x0c8e, 0x0c90,
0x0c92, 0x0ca8,
0x0caa, 0x0cb3,
0x0cb5, 0x0cb9,
0x0cbc, 0x0cc4,
0x0cc6, 0x0cc8,
0x0cca, 0x0ccd,
0x0cd5, 0x0cd6,
0x0cdd, 0x0cde,
0x0ce0, 0x0ce3,
0x0ce6, 0x0cef,
0x0cf1, 0x0cf3,
0x0d00, 0x0d0c,
0x0d0e, 0x0d10,
0x0d12, 0x0d44,
0x0d46, 0x0d48,
0x0d4a, 0x0d4f,
0x0d54, 0x0d63,
0x0d66, 0x0d7f,
0x0d81, 0x0d83,
0x0d85, 0x0d96,
0x0d9a, 0x0db1,
0x0db3, 0x0dbb,
0x0dbd, 0x0dbd,
0x0dc0, 0x0dc6,
0x0dca, 0x0dca,
0x0dcf, 0x0dd4,
0x0dd6, 0x0dd6,
0x0dd8, 0x0ddf,
0x0de6, 0x0def,
0x0df2, 0x0df4,
0x0e01, 0x0e3a,
0x0e3f, 0x0e5b,
0x0e81, 0x0e82,
0x0e84, 0x0e84,
0x0e86, 0x0e8a,
0x0e8c, 0x0ea3,
0x0ea5, 0x0ea5,
0x0ea7, 0x0ebd,
0x0ec0, 0x0ec4,
0x0ec6, 0x0ec6,
0x0ec8, 0x0ece,
0x0ed0, 0x0ed9,
0x0edc, 0x0edf,
0x0f00, 0x0f47,
0x0f49, 0x0f6c,
0x0f71, 0x0f97,
0x0f99, 0x0fbc,
0x0fbe, 0x0fcc,
0x0fce, 0x0fda,
0x1000, 0x10c5,
0x10c7, 0x10c7,
0x10cd, 0x10cd,
0x10d0, 0x1248,
0x124a, 0x124d,
0x1250, 0x1256,
0x1258, 0x1258,
0x125a, 0x125d,
0x1260, 0x1288,
0x128a, 0x128d,
0x1290, 0x12b0,
0x12b2, 0x12b5,
0x12b8, 0x12be,
0x12c0, 0x12c0,
0x12c2, 0x12c5,
0x12c8, 0x12d6,
0x12d8, 0x1310,
0x1312, 0x1315,
0x1318, 0x135a,
0x135d, 0x137c,
0x1380, 0x1399,
0x13a0, 0x13f5,
0x13f8, 0x13fd,
0x1400, 0x167f,
0x1681, 0x169c,
0x16a0, 0x16f8,
0x1700, 0x1715,
0x171f, 0x1736,
0x1740, 0x1753,
0x1760, 0x176c,
0x176e, 0x1770,
0x1772, 0x1773,
0x1780, 0x17dd,
0x17e0, 0x17e9,
0x17f0, 0x17f9,
0x1800, 0x1819,
0x1820, 0x1878,
0x1880, 0x18aa,
0x18b0, 0x18f5,
0x1900, 0x191e,
0x1920, 0x192b,
0x1930, 0x193b,
0x1940, 0x1940,
0x1944, 0x196d,
0x1970, 0x1974,
0x1980, 0x19ab,
0x19b0, 0x19c9,
0x19d0, 0x19da,
0x19de, 0x1a1b,
0x1a1e, 0x1a5e,
0x1a60, 0x1a7c,
0x1a7f, 0x1a89,
0x1a90, 0x1a99,
0x1aa0, 0x1aad,
0x1ab0, 0x1ace,
0x1b00, 0x1b4c,
0x1b4e, 0x1bf3,
0x1bfc, 0x1c37,
0x1c3b, 0x1c49,
0x1c4d, 0x1c8a,
0x1c90, 0x1cba,
0x1cbd, 0x1cc7,
0x1cd0, 0x1cfa,
0x1d00, 0x1f15,
0x1f18, 0x1f1d,
0x1f20, 0x1f45,
0x1f48, 0x1f4d,
0x1f50, 0x1f57,
0x1f59, 0x1f59,
0x1f5b, 0x1f5b,
0x1f5d, 0x1f5d,
0x1f5f, 0x1f7d,
0x1f80, 0x1fb4,
0x1fb6, 0x1fc4,
0x1fc6, 0x1fd3,
0x1fd6, 0x1fdb,
0x1fdd, 0x1fef,
0x1ff2, 0x1ff4,
0x1ff6, 0x1ffe,
0x200b, 0x2027,
0x202a, 0x202e,
0x2030, 0x205e,
0x2060, 0x2064,
0x2066, 0x2071,
0x2074, 0x208e,
0x2090, 0x209c,
0x20a0, 0x20c0,
0x20d0, 0x20f0,
0x2100, 0x218b,
0x2190, 0x2429,
0x2440, 0x244a,
0x2460, 0x2b73,
0x2b76, 0x2b95,
0x2b97, 0x2cf3,
0x2cf9, 0x2d25,
0x2d27, 0x2d27,
0x2d2d, 0x2d2d,
0x2d30, 0x2d67,
0x2d6f, 0x2d70,
0x2d7f, 0x2d96,
0x2da0, 0x2da6,
0x2da8, 0x2dae,
0x2db0, 0x2db6,
0x2db8, 0x2dbe,
0x2dc0, 0x2dc6,
0x2dc8, 0x2dce,
0x2dd0, 0x2dd6,
0x2dd8, 0x2dde,
0x2de0, 0x2e5d,
0x2e80, 0x2e99,
0x2e9b, 0x2ef3,
0x2f00, 0x2fd5,
0x2ff0, 0x2fff,
0x3001, 0x303f,
0x3041, 0x3096,
0x3099, 0x30ff,
0x3105, 0x312f,
0x3131, 0x318e,
0x3190, 0x31e5,
0x31ef, 0x321e,
0x3220, 0xa48c,
0xa490, 0xa4c6,
0xa4d0, 0xa62b,
0xa640, 0xa6f7,
0xa700, 0xa7cd,
0xa7d0, 0xa7d1,
0xa7d3, 0xa7d3,
0xa7d5, 0xa7dc,
0xa7f2, 0xa82c,
0xa830, 0xa839,
0xa840, 0xa877,
0xa880, 0xa8c5,
0xa8ce, 0xa8d9,
0xa8e0, 0xa953,
0xa95f, 0xa97c,
0xa980, 0xa9cd,
0xa9cf, 0xa9d9,
0xa9de, 0xa9fe,
0xaa00, 0xaa36,
0xaa40, 0xaa4d,
0xaa50, 0xaa59,
0xaa5c, 0xaac2,
0xaadb, 0xaaf6,
0xab01, 0xab06,
0xab09, 0xab0e,
0xab11, 0xab16,
0xab20, 0xab26,
0xab28, 0xab2e,
0xab30, 0xab6b,
0xab70, 0xabed,
0xabf0, 0xabf9,
0xac00, 0xd7a3,
0xd7b0, 0xd7c6,
0xd7cb, 0xd7fb,
0xe000, 0xfa6d,
0xfa70, 0xfad9,
0xfb00, 0xfb06,
0xfb13, 0xfb17,
0xfb1d, 0xfb36,
0xfb38, 0xfb3c,
0xfb3e, 0xfb3e,
0xfb40, 0xfb41,
0xfb43, 0xfb44,
0xfb46, 0xfbc2,
0xfbd3, 0xfd8f,
0xfd92, 0xfdc7,
0xfdcf, 0xfdcf,
0xfdf0, 0xfe19,
0xfe20, 0xfe52,
0xfe54, 0xfe66,
0xfe68, 0xfe6b,
0xfe70, 0xfe74,
0xfe76, 0xfefc,
0xfeff, 0xfeff,
0xff01, 0xffbe,
0xffc2, 0xffc7,
0xffca, 0xffcf,
0xffd2, 0xffd7,
0xffda, 0xffdc,
0xffe0, 0xffe6,
0xffe8, 0xffee,
0xfff9, 0xfffd,
0x10000, 0x1000b,
0x1000d, 0x10026,
0x10028, 0x1003a,
0x1003c, 0x1003d,
0x1003f, 0x1004d,
0x10050, 0x1005d,
0x10080, 0x100fa,
0x10100, 0x10102,
0x10107, 0x10133,
0x10137, 0x1018e,
0x10190, 0x1019c,
0x101a0, 0x101a0,
0x101d0, 0x101fd,
0x10280, 0x1029c,
0x102a0, 0x102d0,
0x102e0, 0x102fb,
0x10300, 0x10323,
0x1032d, 0x1034a,
0x10350, 0x1037a,
0x10380, 0x1039d,
0x1039f, 0x103c3,
0x103c8, 0x103d5,
0x10400, 0x1049d,
0x104a0, 0x104a9,
0x104b0, 0x104d3,
0x104d8, 0x104fb,
0x10500, 0x10527,
0x10530, 0x10563,
0x1056f, 0x1057a,
0x1057c, 0x1058a,
0x1058c, 0x10592,
0x10594, 0x10595,
0x10597, 0x105a1,
0x105a3, 0x105b1,
0x105b3, 0x105b9,
0x105bb, 0x105bc,
0x105c0, 0x105f3,
0x10600, 0x10736,
0x10740, 0x10755,
0x10760, 0x10767,
0x10780, 0x10785,
0x10787, 0x107b0,
0x107b2, 0x107ba,
0x10800, 0x10805,
0x10808, 0x10808,
0x1080a, 0x10835,
0x10837, 0x10838,
0x1083c, 0x1083c,
0x1083f, 0x10855,
0x10857, 0x1089e,
0x108a7, 0x108af,
0x108e0, 0x108f2,
0x108f4, 0x108f5,
0x108fb, 0x1091b,
0x1091f, 0x10939,
0x1093f, 0x1093f,
0x10980, 0x109b7,
0x109bc, 0x109cf,
0x109d2, 0x10a03,
0x10a05, 0x10a06,
0x10a0c, 0x10a13,
0x10a15, 0x10a17,
0x10a19, 0x10a35,
0x10a38, 0x10a3a,
0x10a3f, 0x10a48,
0x10a50, 0x10a58,
0x10a60, 0x10a9f,
0x10ac0, 0x10ae6,
0x10aeb, 0x10af6,
0x10b00, 0x10b35,
0x10b39, 0x10b55,
0x10b58, 0x10b72,
0x10b78, 0x10b91,
0x10b99, 0x10b9c,
0x10ba9, 0x10baf,
0x10c00, 0x10c48,
0x10c80, 0x10cb2,
0x10cc0, 0x10cf2,
0x10cfa, 0x10d27,
0x10d30, 0x10d39,
0x10d40, 0x10d65,
0x10d69, 0x10d85,
0x10d8e, 0x10d8f,
0x10e60, 0x10e7e,
0x10e80, 0x10ea9,
0x10eab, 0x10ead,
0x10eb0, 0x10eb1,
0x10ec2, 0x10ec4,
0x10efc, 0x10f27,
0x10f30, 0x10f59,
0x10f70, 0x10f89,
0x10fb0, 0x10fcb,
0x10fe0, 0x10ff6,
0x11000, 0x1104d,
0x11052, 0x11075,
0x1107f, 0x110c2,
0x110cd, 0x110cd,
0x110d0, 0x110e8,
0x110f0, 0x110f9,
0x11100, 0x11134,
0x11136, 0x11147,
0x11150, 0x11176,
0x11180, 0x111df,
0x111e1, 0x111f4,
0x11200, 0x11211,
0x11213, 0x11241,
0x11280, 0x11286,
0x11288, 0x11288,
0x1128a, 0x1128d,
0x1128f, 0x1129d,
0x1129f, 0x112a9,
0x112b0, 0x112ea,
0x112f0, 0x112f9,
0x11300, 0x11303,
0x11305, 0x1130c,
0x1130f, 0x11310,
0x11313, 0x11328,
0x1132a, 0x11330,
0x11332, 0x11333,
0x11335, 0x11339,
0x1133b, 0x11344,
0x11347, 0x11348,
0x1134b, 0x1134d,
0x11350, 0x11350,
0x11357, 0x11357,
0x1135d, 0x11363,
0x11366, 0x1136c,
0x11370, 0x11374,
0x11380, 0x11389,
0x1138b, 0x1138b,
0x1138e, 0x1138e,
0x11390, 0x113b5,
0x113b7, 0x113c0,
0x113c2, 0x113c2,
0x113c5, 0x113c5,
0x113c7, 0x113ca,
0x113cc, 0x113d5,
0x113d7, 0x113d8,
0x113e1, 0x113e2,
0x11400, 0x1145b,
0x1145d, 0x11461,
0x11480, 0x114c7,
0x114d0, 0x114d9,
0x11580, 0x115b5,
0x115b8, 0x115dd,
0x11600, 0x11644,
0x11650, 0x11659,
0x11660, 0x1166c,
0x11680, 0x116b9,
0x116c0, 0x116c9,
0x116d0, 0x116e3,
0x11700, 0x1171a,
0x1171d, 0x1172b,
0x11730, 0x11746,
0x11800, 0x1183b,
0x118a0, 0x118f2,
0x118ff, 0x11906,
0x11909, 0x11909,
0x1190c, 0x11913,
0x11915, 0x11916,
0x11918, 0x11935,
0x11937, 0x11938,
0x1193b, 0x11946,
0x11950, 0x11959,
0x119a0, 0x119a7,
0x119aa, 0x119d7,
0x119da, 0x119e4,
0x11a00, 0x11a47,
0x11a50, 0x11aa2,
0x11ab0, 0x11af8,
0x11b00, 0x11b09,
0x11bc0, 0x11be1,
0x11bf0, 0x11bf9,
0x11c00, 0x11c08,
0x11c0a, 0x11c36,
0x11c38, 0x11c45,
0x11c50, 0x11c6c,
0x11c70, 0x11c8f,
0x11c92, 0x11ca7,
0x11ca9, 0x11cb6,
0x11d00, 0x11d06,
0x11d08, 0x11d09,
0x11d0b, 0x11d36,
0x11d3a, 0x11d3a,
0x11d3c, 0x11d3d,
0x11d3f, 0x11d47,
0x11d50, 0x11d59,
0x11d60, 0x11d65,
0x11d67, 0x11d68,
0x11d6a, 0x11d8e,
0x11d90, 0x11d91,
0x11d93, 0x11d98,
0x11da0, 0x11da9,
0x11ee0, 0x11ef8,
0x11f00, 0x11f10,
0x11f12, 0x11f3a,
0x11f3e, 0x11f5a,
0x11fb0, 0x11fb0,
0x11fc0, 0x11ff1,
0x11fff, 0x12399,
0x12400, 0x1246e,
0x12470, 0x12474,
0x12480, 0x12543,
0x12f90, 0x12ff2,
0x13000, 0x13455,
0x13460, 0x143fa,
0x14400, 0x14646,
0x16100, 0x16139,
0x16800, 0x16a38,
0x16a40, 0x16a5e,
0x16a60, 0x16a69,
0x16a6e, 0x16abe,
0x16ac0, 0x16ac9,
0x16ad0, 0x16aed,
0x16af0, 0x16af5,
0x16b00, 0x16b45,
0x16b50, 0x16b59,
0x16b5b, 0x16b61,
0x16b63, 0x16b77,
0x16b7d, 0x16b8f,
0x16d40, 0x16d79,
0x16e40, 0x16e9a,
0x16f00, 0x16f4a,
0x16f4f, 0x16f87,
0x16f8f, 0x16f9f,
0x16fe0, 0x16fe4,
0x16ff0, 0x16ff1,
0x17000, 0x187f7,
0x18800, 0x18cd5,
0x18cff, 0x18d08,
0x1aff0, 0x1aff3,
0x1aff5, 0x1affb,
0x1affd, 0x1affe,
0x1b000, 0x1b122,
0x1b132, 0x1b132,
0x1b150, 0x1b152,
0x1b155, 0x1b155,
0x1b164, 0x1b167,
0x1b170, 0x1b2fb,
0x1bc00, 0x1bc6a,
0x1bc70, 0x1bc7c,
0x1bc80, 0x1bc88,
0x1bc90, 0x1bc99,
0x1bc9c, 0x1bca3,
0x1cc00, 0x1ccf9,
0x1cd00, 0x1ceb3,
0x1cf00, 0x1cf2d,
0x1cf30, 0x1cf46,
0x1cf50, 0x1cfc3,
0x1d000, 0x1d0f5,
0x1d100, 0x1d126,
0x1d129, 0x1d1ea,
0x1d200, 0x1d245,
0x1d2c0, 0x1d2d3,
0x1d2e0, 0x1d2f3,
0x1d300, 0x1d356,
0x1d360, 0x1d378,
0x1d400, 0x1d454,
0x1d456, 0x1d49c,
0x1d49e, 0x1d49f,
0x1d4a2, 0x1d4a2,
0x1d4a5, 0x1d4a6,
0x1d4a9, 0x1d4ac,
0x1d4ae, 0x1d4b9,
0x1d4bb, 0x1d4bb,
0x1d4bd, 0x1d4c3,
0x1d4c5, 0x1d505,
0x1d507, 0x1d50a,
0x1d50d, 0x1d514,
0x1d516, 0x1d51c,
0x1d51e, 0x1d539,
0x1d53b, 0x1d53e,
0x1d540, 0x1d544,
0x1d546, 0x1d546,
0x1d54a, 0x1d550,
0x1d552, 0x1d6a5,
0x1d6a8, 0x1d7cb,
0x1d7ce, 0x1da8b,
0x1da9b, 0x1da9f,
0x1daa1, 0x1daaf,
0x1df00, 0x1df1e,
0x1df25, 0x1df2a,
0x1e000, 0x1e006,
0x1e008, 0x1e018,
0x1e01b, 0x1e021,
0x1e023, 0x1e024,
0x1e026, 0x1e02a,
0x1e030, 0x1e06d,
0x1e08f, 0x1e08f,
0x1e100, 0x1e12c,
0x1e130, 0x1e13d,
0x1e140, 0x1e149,
0x1e14e, 0x1e14f,
0x1e290, 0x1e2ae,
0x1e2c0, 0x1e2f9,
0x1e2ff, 0x1e2ff,
0x1e4d0, 0x1e4f9,
0x1e5d0, 0x1e5fa,
0x1e5ff, 0x1e5ff,
0x1e7e0, 0x1e7e6,
0x1e7e8, 0x1e7eb,
0x1e7ed, 0x1e7ee,
0x1e7f0, 0x1e7fe,
0x1e800, 0x1e8c4,
0x1e8c7, 0x1e8d6,
0x1e900, 0x1e94b,
0x1e950, 0x1e959,
0x1e95e, 0x1e95f,
0x1ec71, 0x1ecb4,
0x1ed01, 0x1ed3d,
0x1ee00, 0x1ee03,
0x1ee05, 0x1ee1f,
0x1ee21, 0x1ee22,
0x1ee24, 0x1ee24,
0x1ee27, 0x1ee27,
0x1ee29, 0x1ee32,
0x1ee34, 0x1ee37,
0x1ee39, 0x1ee39,
0x1ee3b, 0x1ee3b,
0x1ee42, 0x1ee42,
0x1ee47, 0x1ee47,
0x1ee49, 0x1ee49,
0x1ee4b, 0x1ee4b,
0x1ee4d, 0x1ee4f,
0x1ee51, 0x1ee52,
0x1ee54, 0x1ee54,
0x1ee57, 0x1ee57,
0x1ee59, 0x1ee59,
0x1ee5b, 0x1ee5b,
0x1ee5d, 0x1ee5d,
0x1ee5f, 0x1ee5f,
0x1ee61, 0x1ee62,
0x1ee64, 0x1ee64,
0x1ee67, 0x1ee6a,
0x1ee6c, 0x1ee72,
0x1ee74, 0x1ee77,
0x1ee79, 0x1ee7c,
0x1ee7e, 0x1ee7e,
0x1ee80, 0x1ee89,
0x1ee8b, 0x1ee9b,
0x1eea1, 0x1eea3,
0x1eea5, 0x1eea9,
0x1eeab, 0x1eebb,
0x1eef0, 0x1eef1,
0x1f000, 0x1f02b,
0x1f030, 0x1f093,
0x1f0a0, 0x1f0ae,
0x1f0b1, 0x1f0bf,
0x1f0c1, 0x1f0cf,
0x1f0d1, 0x1f0f5,
0x1f100, 0x1f1ad,
0x1f1e6, 0x1f202,
0x1f210, 0x1f23b,
0x1f240, 0x1f248,
0x1f250, 0x1f251,
0x1f260, 0x1f265,
0x1f300, 0x1f6d7,
0x1f6dc, 0x1f6ec,
0x1f6f0, 0x1f6fc,
0x1f700, 0x1f776,
0x1f77b, 0x1f7d9,
0x1f7e0, 0x1f7eb,
0x1f7f0, 0x1f7f0,
0x1f800, 0x1f80b,
0x1f810, 0x1f847,
0x1f850, 0x1f859,
0x1f860, 0x1f887,
0x1f890, 0x1f8ad,
0x1f8b0, 0x1f8bb,
0x1f8c0, 0x1f8c1,
0x1f900, 0x1fa53,
0x1fa60, 0x1fa6d,
0x1fa70, 0x1fa7c,
0x1fa80, 0x1fa89,
0x1fa8f, 0x1fac6,
0x1face, 0x1fadc,
0x1fadf, 0x1fae9,
0x1faf0, 0x1faf8,
0x1fb00, 0x1fb92,
0x1fb94, 0x1fbf9,
0x20000, 0x2a6df,
0x2a700, 0x2b739,
0x2b740, 0x2b81d,
0x2b820, 0x2cea1,
0x2ceb0, 0x2ebe0,
0x2ebf0, 0x2ee5d,
0x2f800, 0x2fa1d,
0x30000, 0x3134a,
0x31350, 0x323af,
0xe0001, 0xe0001,
0xe0020, 0xe007f,
0xe0100, 0xe01ef,
0xf0000, 0xffffd,
0x100000, 0x10fffd,
}; /* END of CR_Graph */

/* PROPERTY: 'Lower': POSIX [[:Lower:]] */
static const OnigCodePoint
CR_Lower[] = { 675,
0x0061, 0x007a,
0x00aa, 0x00aa,
0x00b5, 0x00b5,
0x00ba, 0x00ba,
0x00df, 0x00f6,
0x00f8, 0x00ff,
0x0101, 0x0101,
0x0103, 0x0103,
0x0105, 0x0105,
0x0107, 0x0107,
0x0109, 0x0109,
0x010b, 0x010b,
0x010d, 0x010d,
0x010f, 0x010f,
0x0111, 0x0111,
0x0113, 0x0113,
0x0115, 0x0115,
0x0117, 0x0117,
0x0119, 0x0119,
0x011b, 0x011b,
0x011d, 0x011d,
0x011f, 0x011f,
0x0121, 0x0121,
0x0123, 0x0123,
0x0125, 0x0125,
0x0127, 0x0127,
0x0129, 0x0129,
0x012b, 0x012b,
0x012d, 0x012d,
0x012f, 0x012f,
0x0131, 0x0131,
0x0133, 0x0133,
0x0135, 0x0135,
0x0137, 0x0138,
0x013a, 0x013a,
0x013c, 0x013c,
0x013e, 0x013e,
0x0140, 0x0140,
0x0142, 0x0142,
0x0144, 0x0144,
0x0146, 0x0146,
0x0148, 0x0149,
0x014b, 0x014b,
0x014d, 0x014d,
0x014f, 0x014f,
0x0151, 0x0151,
0x0153, 0x0153,
0x0155, 0x0155,
0x0157, 0x0157,
0x0159, 0x0159,
0x015b, 0x015b,
0x015d, 0x015d,
0x015f, 0x015f,
0x0161, 0x0161,
0x0163, 0x0163,
0x0165, 0x0165,
0x0167, 0x0167,
0x0169, 0x0169,
0x016b, 0x016b,
0x016d, 0x016d,
0x016f, 0x016f,
0x0171, 0x0171,
0x0173, 0x0173,
0x0175, 0x0175,
0x0177, 0x0177,
0x017a, 0x017a,
0x017c, 0x017c,
0x017e, 0x0180,
0x0183, 0x0183,
0x0185, 0x0185,
0x0188, 0x0188,
0x018c, 0x018d,
0x0192, 0x0192,
0x0195, 0x0195,
0x0199, 0x019b,
0x019e, 0x019e,
0x01a1, 0x01a1,
0x01a3, 0x01a3,
0x01a5, 0x01a5,
0x01a8, 0x01a8,
0x01aa, 0x01ab,
0x01ad, 0x01ad,
0x01b0, 0x01b0,
0x01b4, 0x01b4,
0x01b6, 0x01b6,
0x01b9, 0x01ba,
0x01bd, 0x01bf,
0x01c6, 0x01c6,
0x01c9, 0x01c9,
0x01cc, 0x01cc,
0x01ce, 0x01ce,
0x01d0, 0x01d0,
0x01d2, 0x01d2,
0x01d4, 0x01d4,
0x01d6, 0x01d6,
0x01d8, 0x01d8,
0x01da, 0x01da,
0x01dc, 0x01dd,
0x01df, 0x01df,
0x01e1, 0x01e1,
0x01e3, 0x01e3,
0x01e5, 0x01e5,
0x01e7, 0x01e7,
0x01e9, 0x01e9,
0x01eb, 0x01eb,
0x01ed, 0x01ed,
0x01ef, 0x01f0,
0x01f3, 0x01f3,
0x01f5, 0x01f5,
0x01f9, 0x01f9,
0x01fb, 0x01fb,
0x01fd, 0x01fd,
0x01ff, 0x01ff,
0x0201, 0x0201,
0x0203, 0x0203,
0x0205, 0x0205,
0x0207, 0x0207,
0x0209, 0x0209,
0x020b, 0x020b,
0x020d, 0x020d,
0x020f, 0x020f,
0x0211, 0x0211,
0x0213, 0x0213,
0x0215, 0x0215,
0x0217, 0x0217,
0x0219, 0x0219,
0x021b, 0x021b,
0x021d, 0x021d,
0x021f, 0x021f,
0x0221, 0x0221,
0x0223, 0x0223,
0x0225, 0x0225,
0x0227, 0x0227,
0x0229, 0x0229,
0x022b, 0x022b,
0x022d, 0x022d,
0x022f, 0x022f,
0x0231, 0x0231,
0x0233, 0x0239,
0x023c, 0x023c,
0x023f, 0x0240,
0x0242, 0x0242,
0x0247, 0x0247,
0x0249, 0x0249,
0x024b, 0x024b,
0x024d, 0x024d,
0x024f, 0x0293,
0x0295, 0x02b8,
0x02c0, 0x02c1,
0x02e0, 0x02e4,
0x0345, 0x0345,
0x0371, 0x0371,
0x0373, 0x0373,
0x0377, 0x0377,
0x037a, 0x037d,
0x0390, 0x0390,
0x03ac, 0x03ce,
0x03d0, 0x03d1,
0x03d5, 0x03d7,
0x03d9, 0x03d9,
0x03db, 0x03db,
0x03dd, 0x03dd,
0x03df, 0x03df,
0x03e1, 0x03e1,
0x03e3, 0x03e3,
0x03e5, 0x03e5,
0x03e7, 0x03e7,
0x03e9, 0x03e9,
0x03eb, 0x03eb,
0x03ed, 0x03ed,
0x03ef, 0x03f3,
0x03f5, 0x03f5,
0x03f8, 0x03f8,
0x03fb, 0x03fc,
0x0430, 0x045f,
0x0461, 0x0461,
0x0463, 0x0463,
0x0465, 0x0465,
0x0467, 0x0467,
0x0469, 0x0469,
0x046b, 0x046b,
0x046d, 0x046d,
0x046f, 0x046f,
0x0471, 0x0471,
0x0473, 0x0473,
0x0475, 0x0475,
0x0477, 0x0477,
0x0479, 0x0479,
0x047b, 0x047b,
0x047d, 0x047d,
0x047f, 0x047f,
0x0481, 0x0481,
0x048b, 0x048b,
0x048d, 0x048d,
0x048f, 0x048f,
0x0491, 0x0491,
0x0493, 0x0493,
0x0495, 0x0495,
0x0497, 0x0497,
0x0499, 0x0499,
0x049b, 0x049b,
0x049d, 0x049d,
0x049f, 0x049f,
0x04a1, 0x04a1,
0x04a3, 0x04a3,
0x04a5, 0x04a5,
0x04a7, 0x04a7,
0x04a9, 0x04a9,
0x04ab, 0x04ab,
0x04ad, 0x04ad,
0x04af, 0x04af,
0x04b1, 0x04b1,
0x04b3, 0x04b3,
0x04b5, 0x04b5,
0x04b7, 0x04b7,
0x04b9, 0x04b9,
0x04bb, 0x04bb,
0x04bd, 0x04bd,
0x04bf, 0x04bf,
0x04c2, 0x04c2,
0x04c4, 0x04c4,
0x04c6, 0x04c6,
0x04c8, 0x04c8,
0x04ca, 0x04ca,
0x04cc, 0x04cc,
0x04ce, 0x04cf,
0x04d1, 0x04d1,
0x04d3, 0x04d3,
0x04d5, 0x04d5,
0x04d7, 0x04d7,
0x04d9, 0x04d9,
0x04db, 0x04db,
0x04dd, 0x04dd,
0x04df, 0x04df,
0x04e1, 0x04e1,
0x04e3, 0x04e3,
0x04e5, 0x04e5,
0x04e7, 0x04e7,
0x04e9, 0x04e9,
0x04eb, 0x04eb,
0x04ed, 0x04ed,
0x04ef, 0x04ef,
0x04f1, 0x04f1,
0x04f3, 0x04f3,
0x04f5, 0x04f5,
0x04f7, 0x04f7,
0x04f9, 0x04f9,
0x04fb, 0x04fb,
0x04fd, 0x04fd,
0x04ff, 0x04ff,
0x0501, 0x0501,
0x0503, 0x0503,
0x0505, 0x0505,
0x0507, 0x0507,
0x0509, 0x0509,
0x050b, 0x050b,
0x050d, 0x050d,
0x050f, 0x050f,
0x0511, 0x0511,
0x0513, 0x0513,
0x0515, 0x0515,
0x0517, 0x0517,
0x0519, 0x0519,
0x051b, 0x051b,
0x051d, 0x051d,
0x051f, 0x051f,
0x0521, 0x0521,
0x0523, 0x0523,
0x0525, 0x0525,
0x0527, 0x0527,
0x0529, 0x0529,
0x052b, 0x052b,
0x052d, 0x052d,
0x052f, 0x052f,
0x0560, 0x0588,
0x10d0, 0x10fa,
0x10fc, 0x10ff,
0x13f8, 0x13fd,
0x1c80, 0x1c88,
0x1c8a, 0x1c8a,
0x1d00, 0x1dbf,
0x1e01, 0x1e01,
0x1e03, 0x1e03,
0x1e05, 0x1e05,
0x1e07, 0x1e07,
0x1e09, 0x1e09,
0x1e0b, 0x1e0b,
0x1e0d, 0x1e0d,
0x1e0f, 0x1e0f,
0x1e11, 0x1e11,
0x1e13, 0x1e13,
0x1e15, 0x1e15,
0x1e17, 0x1e17,
0x1e19, 0x1e19,
0x1e1b, 0x1e1b,
0x1e1d, 0x1e1d,
0x1e1f, 0x1e1f,
0x1e21, 0x1e21,
0x1e23, 0x1e23,
0x1e25, 0x1e25,
0x1e27, 0x1e27,
0x1e29, 0x1e29,
0x1e2b, 0x1e2b,
0x1e2d, 0x1e2d,
0x1e2f, 0x1e2f,
0x1e31, 0x1e31,
0x1e33, 0x1e33,
0x1e35, 0x1e35,
0x1e37, 0x1e37,
0x1e39, 0x1e39,
0x1e3b, 0x1e3b,
0x1e3d, 0x1e3d,
0x1e3f, 0x1e3f,
0x1e41, 0x1e41,
0x1e43, 0x1e43,
0x1e45, 0x1e45,
0x1e47, 0x1e47,
0x1e49, 0x1e49,
0x1e4b, 0x1e4b,
0x1e4d, 0x1e4d,
0x1e4f, 0x1e4f,
0x1e51, 0x1e51,
0x1e53, 0x1e53,
0x1e55, 0x1e55,
0x1e57, 0x1e57,
0x1e59, 0x1e59,
0x1e5b, 0x1e5b,
0x1e5d, 0x1e5d,
0x1e5f, 0x1e5f,
0x1e61, 0x1e61,
0x1e63, 0x1e63,
0x1e65, 0x1e65,
0x1e67, 0x1e67,
0x1e69, 0x1e69,
0x1e6b, 0x1e6b,
0x1e6d, 0x1e6d,
0x1e6f, 0x1e6f,
0x1e71, 0x1e71,
0x1e73, 0x1e73,
0x1e75, 0x1e75,
0x1e77, 0x1e77,
0x1e79, 0x1e79,
0x1e7b, 0x1e7b,
0x1e7d, 0x1e7d,
0x1e7f, 0x1e7f,
0x1e81, 0x1e81,
0x1e83, 0x1e83,
0x1e85, 0x1e85,
0x1e87, 0x1e87,
0x1e89, 0x1e89,
0x1e8b, 0x1e8b,
0x1e8d, 0x1e8d,
0x1e8f, 0x1e8f,
0x1e91, 0x1e91,
0x1e93, 0x1e93,
0x1e95, 0x1e9d,
0x1e9f, 0x1e9f,
0x1ea1, 0x1ea1,
0x1ea3, 0x1ea3,
0x1ea5, 0x1ea5,
0x1ea7, 0x1ea7,
0x1ea9, 0x1ea9,
0x1eab, 0x1eab,
0x1ead, 0x1ead,
0x1eaf, 0x1eaf,
0x1eb1, 0x1eb1,
0x1eb3, 0x1eb3,
0x1eb5, 0x1eb5,
0x1eb7, 0x1eb7,
0x1eb9, 0x1eb9,
0x1ebb, 0x1ebb,
0x1ebd, 0x1ebd,
0x1ebf, 0x1ebf,
0x1ec1, 0x1ec1,
0x1ec3, 0x1ec3,
0x1ec5, 0x1ec5,
0x1ec7, 0x1ec7,
0x1ec9, 0x1ec9,
0x1ecb, 0x1ecb,
0x1ecd, 0x1ecd,
0x1ecf, 0x1ecf,
0x1ed1, 0x1ed1,
0x1ed3, 0x1ed3,
0x1ed5, 0x1ed5,
0x1ed7, 0x1ed7,
0x1ed9, 0x1ed9,
0x1edb, 0x1edb,
0x1edd, 0x1edd,
0x1edf, 0x1edf,
0x1ee1, 0x1ee1,
0x1ee3, 0x1ee3,
0x1ee5, 0x1ee5,
0x1ee7, 0x1ee7,
0x1ee9, 0x1ee9,
0x1eeb, 0x1eeb,
0x1eed, 0x1eed,
0x1eef, 0x1eef,
0x1ef1, 0x1ef1,
0x1ef3, 0x1ef3,
0x1ef5, 0x1ef5,
0x1ef7, 0x1ef7,
0x1ef9, 0x1ef9,
0x1efb, 0x1efb,
0x1efd, 0x1efd,
0x1eff, 0x1f07,
0x1f10, 0x1f15,
0x1f20, 0x1f27,
0x1f30, 0x1f37,
0x1f40, 0x1f45,
0x1f50, 0x1f57,
0x1f60, 0x1f67,
0x1f70, 0x1f7d,
0x1f80, 0x1f87,
0x1f90, 0x1f97,
0x1fa0, 0x1fa7,
0x1fb0, 0x1fb4,
0x1fb6, 0x1fb7,
0x1fbe, 0x1fbe,
0x1fc2, 0x1fc4,
0x1fc6, 0x1fc7,
0x1fd0, 0x1fd3,
0x1fd6, 0x1fd7,
0x1fe0, 0x1fe7,
0x1ff2, 0x1ff4,
0x1ff6, 0x1ff7,
0x2071, 0x2071,
0x207f, 0x207f,
0x2090, 0x209c,
0x210a, 0x210a,
0x210e, 0x210f,
0x2113, 0x2113,
0x212f, 0x212f,
0x2134, 0x2134,
0x2139, 0x2139,
0x213c, 0x213d,
0x2146, 0x2149,
0x214e, 0x214e,
0x2170, 0x217f,
0x2184, 0x2184,
0x24d0, 0x24e9,
0x2c30, 0x2c5f,
0x2c61, 0x2c61,
0x2c65, 0x2c66,
0x2c68, 0x2c68,
0x2c6a, 0x2c6a,
0x2c6c, 0x2c6c,
0x2c71, 0x2c71,
0x2c73, 0x2c74,
0x2c76, 0x2c7d,
0x2c81, 0x2c81,
0x2c83, 0x2c83,
0x2c85, 0x2c85,
0x2c87, 0x2c87,
0x2c89, 0x2c89,
0x2c8b, 0x2c8b,
0x2c8d, 0x2c8d,
0x2c8f, 0x2c8f,
0x2c91, 0x2c91,
0x2c93, 0x2c93,
0x2c95, 0x2c95,
0x2c97, 0x2c97,
0x2c99, 0x2c99,
0x2c9b, 0x2c9b,
0x2c9d, 0x2c9d,
0x2c9f, 0x2c9f,
0x2ca1, 0x2ca1,
0x2ca3, 0x2ca3,
0x2ca5, 0x2ca5,
0x2ca7, 0x2ca7,
0x2ca9, 0x2ca9,
0x2cab, 0x2cab,
0x2cad, 0x2cad,
0x2caf, 0x2caf,
0x2cb1, 0x2cb1,
0x2cb3, 0x2cb3,
0x2cb5, 0x2cb5,
0x2cb7, 0x2cb7,
0x2cb9, 0x2cb9,
0x2cbb, 0x2cbb,
0x2cbd, 0x2cbd,
0x2cbf, 0x2cbf,
0x2cc1, 0x2cc1,
0x2cc3, 0x2cc3,
0x2cc5, 0x2cc5,
0x2cc7, 0x2cc7,
0x2cc9, 0x2cc9,
0x2ccb, 0x2ccb,
0x2ccd, 0x2ccd,
0x2ccf, 0x2ccf,
0x2cd1, 0x2cd1,
0x2cd3, 0x2cd3,
0x2cd5, 0x2cd5,
0x2cd7, 0x2cd7,
0x2cd9, 0x2cd9,
0x2cdb, 0x2cdb,
0x2cdd, 0x2cdd,
0x2cdf, 0x2cdf,
0x2ce1, 0x2ce1,
0x2ce3, 0x2ce4,
0x2cec, 0x2cec,
0x2cee, 0x2cee,
0x2cf3, 0x2cf3,
0x2d00, 0x2d25,
0x2d27, 0x2d27,
0x2d2d, 0x2d2d,
0xa641, 0xa641,
0xa643, 0xa643,
0xa645, 0xa645,
0xa647, 0xa647,
0xa649, 0xa649,
0xa64b, 0xa64b,
0xa64d, 0xa64d,
0xa64f, 0xa64f,
0xa651, 0xa651,
0xa653, 0xa653,
0xa655, 0xa655,
0xa657, 0xa657,
0xa659, 0xa659,
0xa65b, 0xa65b,
0xa65d, 0xa65d,
0xa65f, 0xa65f,
0xa661, 0xa661,
0xa663, 0xa663,
0xa665, 0xa665,
0xa667, 0xa667,
0xa669, 0xa669,
0xa66b, 0xa66b,
0xa66d, 0xa66d,
0xa681, 0xa681,
0xa683, 0xa683,
0xa685, 0xa685,
0xa687, 0xa687,
0xa689, 0xa689,
0xa68b, 0xa68b,
0xa68d, 0xa68d,
0xa68f, 0xa68f,
0xa691, 0xa691,
0xa693, 0xa693,
0xa695, 0xa695,
0xa697, 0xa697,
0xa699, 0xa699,
0xa69b, 0xa69d,
0xa723, 0xa723,
0xa725, 0xa725,
0xa727, 0xa727,
0xa729, 0xa729,
0xa72b, 0xa72b,
0xa72d, 0xa72d,
0xa72f, 0xa731,
0xa733, 0xa733,
0xa735, 0xa735,
0xa737, 0xa737,
0xa739, 0xa739,
0xa73b, 0xa73b,
0xa73d, 0xa73d,
0xa73f, 0xa73f,
0xa741, 0xa741,
0xa743, 0xa743,
0xa745, 0xa745,
0xa747, 0xa747,
0xa749, 0xa749,
0xa74b, 0xa74b,
0xa74d, 0xa74d,
0xa74f, 0xa74f,
0xa751, 0xa751,
0xa753, 0xa753,
0xa755, 0xa755,
0xa757, 0xa757,
0xa759, 0xa759,
0xa75b, 0xa75b,
0xa75d, 0xa75d,
0xa75f, 0xa75f,
0xa761, 0xa761,
0xa763, 0xa763,
0xa765, 0xa765,
0xa767, 0xa767,
0xa769, 0xa769,
0xa76b, 0xa76b,
0xa76d, 0xa76d,
0xa76f, 0xa778,
0xa77a, 0xa77a,
0xa77c, 0xa77c,
0xa77f, 0xa77f,
0xa781, 0xa781,
0xa783, 0xa783,
0xa785, 0xa785,
0xa787, 0xa787,
0xa78c, 0xa78c,
0xa78e, 0xa78e,
0xa791, 0xa791,
0xa793, 0xa795,
0xa797, 0xa797,
0xa799, 0xa799,
0xa79b, 0xa79b,
0xa79d, 0xa79d,
0xa79f, 0xa79f,
0xa7a1, 0xa7a1,
0xa7a3, 0xa7a3,
0xa7a5, 0xa7a5,
0xa7a7, 0xa7a7,
0xa7a9, 0xa7a9,
0xa7af, 0xa7af,
0xa7b5, 0xa7b5,
0xa7b7, 0xa7b7,
0xa7b9, 0xa7b9,
0xa7bb, 0xa7bb,
0xa7bd, 0xa7bd,
0xa7bf, 0xa7bf,
0xa7c1, 0xa7c1,
0xa7c3, 0xa7c3,
0xa7c8, 0xa7c8,
0xa7ca, 0xa7ca,
0xa7cd, 0xa7cd,
0xa7d1, 0xa7d1,
0xa7d3, 0xa7d3,
0xa7d5, 0xa7d5,
0xa7d7, 0xa7d7,
0xa7d9, 0xa7d9,
0xa7db, 0xa7db,
0xa7f2, 0xa7f4,
0xa7f6, 0xa7f6,
0xa7f8, 0xa7fa,
0xab30, 0xab5a,
0xab5c, 0xab69,
0xab70, 0xabbf,
0xfb00, 0xfb06,
0xfb13, 0xfb17,
0xff41, 0xff5a,
0x10428, 0x1044f,
0x104d8, 0x104fb,
0x10597, 0x105a1,
0x105a3, 0x105b1,
0x105b3, 0x105b9,
0x105bb, 0x105bc,
0x10780, 0x10780,
0x10783, 0x10785,
0x10787, 0x107b0,
0x107b2, 0x107ba,
0x10cc0, 0x10cf2,
0x10d70, 0x10d85,
0x118c0, 0x118df,
0x16e60, 0x16e7f,
0x1d41a, 0x1d433,
0x1d44e, 0x1d454,
0x1d456, 0x1d467,
0x1d482, 0x1d49b,
0x1d4b6, 0x1d4b9,
0x1d4bb, 0x1d4bb,
0x1d4bd, 0x1d4c3,
0x1d4c5, 0x1d4cf,
0x1d4ea, 0x1d503,
0x1d51e, 0x1d537,
0x1d552, 0x1d56b,
0x1d586, 0x1d59f,
0x1d5ba, 0x1d5d3,
0x1d5ee, 0x1d607,
0x1d622, 0x1d63b,
0x1d656, 0x1d66f,
0x1d68a, 0x1d6a5,
0x1d6c2, 0x1d6da,
0x1d6dc, 0x1d6e1,
0x1d6fc, 0x1d714,
0x1d716, 0x1d71b,
0x1d736, 0x1d74e,
0x1d750, 0x1d755,
0x1d770, 0x1d788,
0x1d78a, 0x1d78f,
0x1d7aa, 0x1d7c2,
0x1d7c4, 0x1d7c9,
0x1d7cb, 0x1d7cb,
0x1df00, 0x1df09,
0x1df0b, 0x1df1e,
0x1df25, 0x1df2a,
0x1e030, 0x1e06d,
0x1e922, 0x1e943,
}; /* END of CR_Lower */

/* PROPERTY: 'Print': POSIX [[:Print:]] */
static const OnigCodePoint
CR_Print[] = { 733,
0x0020, 0x007e,
0x00a0, 0x0377,
0x037a, 0x037f,
0x0384, 0x038a,
0x038c, 0x038c,
0x038e, 0x03a1,
0x03a3, 0x052f,
0x0531, 0x0556,
0x0559, 0x058a,
0x058d, 0x058f,
0x0591, 0x05c7,
0x05d0, 0x05ea,
0x05ef, 0x05f4,
0x0600, 0x070d,
0x070f, 0x074a,
0x074d, 0x07b1,
0x07c0, 0x07fa,
0x07fd, 0x082d,
0x0830, 0x083e,
0x0840, 0x085b,
0x085e, 0x085e,
0x0860, 0x086a,
0x0870, 0x088e,
0x0890, 0x0891,
0x0897, 0x0983,
0x0985, 0x098c,
0x098f, 0x0990,
0x0993, 0x09a8,
0x09aa, 0x09b0,
0x09b2, 0x09b2,
0x09b6, 0x09b9,
0x09bc, 0x09c4,
0x09c7, 0x09c8,
0x09cb, 0x09ce,
0x09d7, 0x09d7,
0x09dc, 0x09dd,
0x09df, 0x09e3,
0x09e6, 0x09fe,
0x0a01, 0x0a03,
0x0a05, 0x0a0a,
0x0a0f, 0x0a10,
0x0a13, 0x0a28,
0x0a2a, 0x0a30,
0x0a32, 0x0a33,
0x0a35, 0x0a36,
0x0a38, 0x0a39,
0x0a3c, 0x0a3c,
0x0a3e, 0x0a42,
0x0a47, 0x0a48,
0x0a4b, 0x0a4d,
0x0a51, 0x0a51,
0x0a59, 0x0a5c,
0x0a5e, 0x0a5e,
0x0a66, 0x0a76,
0x0a81, 0x0a83,
0x0a85, 0x0a8d,
0x0a8f, 0x0a91,
0x0a93, 0x0aa8,
0x0aaa, 0x0ab0,
0x0ab2, 0x0ab3,
0x0ab5, 0x0ab9,
0x0abc, 0x0ac5,
0x0ac7, 0x0ac9,
0x0acb, 0x0acd,
0x0ad0, 0x0ad0,
0x0ae0, 0x0ae3,
0x0ae6, 0x0af1,
0x0af9, 0x0aff,
0x0b01, 0x0b03,
0x0b05, 0x0b0c,
0x0b0f, 0x0b10,
0x0b13, 0x0b28,
0x0b2a, 0x0b30,
0x0b32, 0x0b33,
0x0b35, 0x0b39,
0x0b3c, 0x0b44,
0x0b47, 0x0b48,
0x0b4b, 0x0b4d,
0x0b55, 0x0b57,
0x0b5c, 0x0b5d,
0x0b5f, 0x0b63,
0x0b66, 0x0b77,
0x0b82, 0x0b83,
0x0b85, 0x0b8a,
0x0b8e, 0x0b90,
0x0b92, 0x0b95,
0x0b99, 0x0b9a,
0x0b9c, 0x0b9c,
0x0b9e, 0x0b9f,
0x0ba3, 0x0ba4,
0x0ba8, 0x0baa,
0x0bae, 0x0bb9,
0x0bbe, 0x0bc2,
0x0bc6, 0x0bc8,
0x0bca, 0x0bcd,
0x0bd0, 0x0bd0,
0x0bd7, 0x0bd7,
0x0be6, 0x0bfa,
0x0c00, 0x0c0c,
0x0c0e, 0x0c10,
0x0c12, 0x0c28,
0x0c2a, 0x0c39,
0x0c3c, 0x0c44,
0x0c46, 0x0c48,
0x0c4a, 0x0c4d,
0x0c55, 0x0c56,
0x0c58, 0x0c5a,
0x0c5d, 0x0c5d,
0x0c60, 0x0c63,
0x0c66, 0x0c6f,
0x0c77, 0x0c8c,
0x0c8e, 0x0c90,
0x0c92, 0x0ca8,
0x0caa, 0x0cb3,
0x0cb5, 0x0cb9,
0x0cbc, 0x0cc4,
0x0cc6, 0x0cc8,
0x0cca, 0x0ccd,
0x0cd5, 0x0cd6,
0x0cdd, 0x0cde,
0x0ce0, 0x0ce3,
0x0ce6, 0x0cef,
0x0cf1, 0x0cf3,
0x0d00, 0x0d0c,
0x0d0e, 0x0d10,
0x0d12, 0x0d44,
0x0d46, 0x0d48,
0x0d4a, 0x0d4f,
0x0d54, 0x0d63,
0x0d66, 0x0d7f,
0x0d81, 0x0d83,
0x0d85, 0x0d96,
0x0d9a, 0x0db1,
0x0db3, 0x0dbb,
0x0dbd, 0x0dbd,
0x0dc0, 0x0dc6,
0x0dca, 0x0dca,
0x0dcf, 0x0dd4,
0x0dd6, 0x0dd6,
0x0dd8, 0x0ddf,
0x0de6, 0x0def,
0x0df2, 0x0df4,
0x0e01, 0x0e3a,
0x0e3f, 0x0e5b,
0x0e81, 0x0e82,
0x0e84, 0x0e84,
0x0e86, 0x0e8a,
0x0e8c, 0x0ea3,
0x0ea5, 0x0ea5,
0x0ea7, 0x0ebd,
0x0ec0, 0x0ec4,
0x0ec6, 0x0ec6,
0x0ec8, 0x0ece,
0x0ed0, 0x0ed9,
0x0edc, 0x0edf,
0x0f00, 0x0f47,
0x0f49, 0x0f6c,
0x0f71, 0x0f97,
0x0f99, 0x0fbc,
0x0fbe, 0x0fcc,
0x0fce, 0x0fda,
0x1000, 0x10c5,
0x10c7, 0x10c7,
0x10cd, 0x10cd,
0x10d0, 0x1248,
0x124a, 0x124d,
0x1250, 0x1256,
0x1258, 0x1258,
0x125a, 0x125d,
0x1260, 0x1288,
0x128a, 0x128d,
0x1290, 0x12b0,
0x12b2, 0x12b5,
0x12b8, 0x12be,
0x12c0, 0x12c0,
0x12c2, 0x12c5,
0x12c8, 0x12d6,
0x12d8, 0x1310,
0x1312, 0x1315,
0x1318, 0x135a,
0x135d, 0x137c,
0x1380, 0x1399,
0x13a0, 0x13f5,
0x13f8, 0x13fd,
0x1400, 0x169c,
0x16a0, 0x16f8,
0x1700, 0x1715,
0x171f, 0x1736,
0x1740, 0x1753,
0x1760, 0x176c,
0x176e, 0x1770,
0x1772, 0x1773,
0x1780, 0x17dd,
0x17e0, 0x17e9,
0x17f0, 0x17f9,
0x1800, 0x1819,
0x1820, 0x1878,
0x1880, 0x18aa,
0x18b0, 0x18f5,
0x1900, 0x191e,
0x1920, 0x192b,
0x1930, 0x193b,
0x1940, 0x1940,
0x1944, 0x196d,
0x1970, 0x1974,
0x1980, 0x19ab,
0x19b0, 0x19c9,
0x19d0, 0x19da,
0x19de, 0x1a1b,
0x1a1e, 0x1a5e,
0x1a60, 0x1a7c,
0x1a7f, 0x1a89,
0x1a90, 0x1a99,
0x1aa0, 0x1aad,
0x1ab0, 0x1ace,
0x1b00, 0x1b4c,
0x1b4e, 0x1bf3,
0x1bfc, 0x1c37,
0x1c3b, 0x1c49,
0x1c4d, 0x1c8a,
0x1c90, 0x1cba,
0x1cbd, 0x1cc7,
0x1cd0, 0x1cfa,
0x1d00, 0x1f15,
0x1f18, 0x1f1d,
0x1f20, 0x1f45,
0x1f48, 0x1f4d,
0x1f50, 0x1f57,
0x1f59, 0x1f59,
0x1f5b, 0x1f5b,
0x1f5d, 0x1f5d,
0x1f5f, 0x1f7d,
0x1f80, 0x1fb4,
0x1fb6, 0x1fc4,
0x1fc6, 0x1fd3,
0x1fd6, 0x1fdb,
0x1fdd, 0x1fef,
0x1ff2, 0x1ff4,
0x1ff6, 0x1ffe,
0x2000, 0x2027,
0x202a, 0x2064,
0x2066, 0x2071,
0x2074, 0x208e,
0x2090, 0x209c,
0x20a0, 0x20c0,
0x20d0, 0x20f0,
0x2100, 0x218b,
0x2190, 0x2429,
0x2440, 0x244a,
0x2460, 0x2b73,
0x2b76, 0x2b95,
0x2b97, 0x2cf3,
0x2cf9, 0x2d25,
0x2d27, 0x2d27,
0x2d2d, 0x2d2d,
0x2d30, 0x2d67,
0x2d6f, 0x2d70,
0x2d7f, 0x2d96,
0x2da0, 0x2da6,
0x2da8, 0x2dae,
0x2db0, 0x2db6,
0x2db8, 0x2dbe,
0x2dc0, 0x2dc6,
0x2dc8, 0x2dce,
0x2dd0, 0x2dd6,
0x2dd8, 0x2dde,
0x2de0, 0x2e5d,
0x2e80, 0x2e99,
0x2e9b, 0x2ef3,
0x2f00, 0x2fd5,
0x2ff0, 0x303f,
0x3041, 0x3096,
0x3099, 0x30ff,
0x3105, 0x312f,
0x3131, 0x318e,
0x3190, 0x31e5,
0x31ef, 0x321e,
0x3220, 0xa48c,
0xa490, 0xa4c6,
0xa4d0, 0xa62b,
0xa640, 0xa6f7,
0xa700, 0xa7cd,
0xa7d0, 0xa7d1,
0xa7d3, 0xa7d3,
0xa7d5, 0xa7dc,
0xa7f2, 0xa82c,
0xa830, 0xa839,
0xa840, 0xa877,
0xa880, 0xa8c5,
0xa8ce, 0xa8d9,
0xa8e0, 0xa953,
0xa95f, 0xa97c,
0xa980, 0xa9cd,
0xa9cf, 0xa9d9,
0xa9de, 0xa9fe,
0xaa00, 0xaa36,
0xaa40, 0xaa4d,
0xaa50, 0xaa59,
0xaa5c, 0xaac2,
0xaadb, 0xaaf6,
0xab01, 0xab06,
0xab09, 0xab0e,
0xab11, 0xab16,
0xab20, 0xab26,
0xab28, 0xab2e,
0xab30, 0xab6b,
0xab70, 0xabed,
0xabf0, 0xabf9,
0xac00, 0xd7a3,
0xd7b0, 0xd7c6,
0xd7cb, 0xd7fb,
0xe000, 0xfa6d,
0xfa70, 0xfad9,
0xfb00, 0xfb06,
0xfb13, 0xfb17,
0xfb1d, 0xfb36,
0xfb38, 0xfb3c,
0xfb3e, 0xfb3e,
0xfb40, 0xfb41,
0xfb43, 0xfb44,
0xfb46, 0xfbc2,
0xfbd3, 0xfd8f,
0xfd92, 0xfdc7,
0xfdcf, 0xfdcf,
0xfdf0, 0xfe19,
0xfe20, 0xfe52,
0xfe54, 0xfe66,
0xfe68, 0xfe6b,
0xfe70, 0xfe74,
0xfe76, 0xfefc,
0xfeff, 0xfeff,
0xff01, 0xffbe,
0xffc2, 0xffc7,
0xffca, 0xffcf,
0xffd2, 0xffd7,
0xffda, 0xffdc,
0xffe0, 0xffe6,
0xffe8, 0xffee,
0xfff9, 0xfffd,
0x10000, 0x1000b,
0x1000d, 0x10026,
0x10028, 0x1003a,
0x1003c, 0x1003d,
0x1003f, 0x1004d,
0x10050, 0x1005d,
0x10080, 0x100fa,
0x10100, 0x10102,
0x10107, 0x10133,
0x10137, 0x1018e,
0x10190, 0x1019c,
0x101a0, 0x101a0,
0x101d0, 0x101fd,
0x10280, 0x1029c,
0x102a0, 0x102d0,
0x102e0, 0x102fb,
0x10300, 0x10323,
0x1032d, 0x1034a,
0x10350, 0x1037a,
0x10380, 0x1039d,
0x1039f, 0x103c3,
0x103c8, 0x103d5,
0x10400, 0x1049d,
0x104a0, 0x104a9,
0x104b0, 0x104d3,
0x104d8, 0x104fb,
0x10500, 0x10527,
0x10530, 0x10563,
0x1056f, 0x1057a,
0x1057c, 0x1058a,
0x1058c, 0x10592,
0x10594, 0x10595,
0x10597, 0x105a1,
0x105a3, 0x105b1,
0x105b3, 0x105b9,
0x105bb, 0x105bc,
0x105c0, 0x105f3,
0x10600, 0x10736,
0x10740, 0x10755,
0x10760, 0x10767,
0x10780, 0x10785,
0x10787, 0x107b0,
0x107b2, 0x107ba,
0x10800, 0x10805,
0x10808, 0x10808,
0x1080a, 0x10835,
0x10837, 0x10838,
0x1083c, 0x1083c,
0x1083f, 0x10855,
0x10857, 0x1089e,
0x108a7, 0x108af,
0x108e0, 0x108f2,
0x108f4, 0x108f5,
0x108fb, 0x1091b,
0x1091f, 0x10939,
0x1093f, 0x1093f,
0x10980, 0x109b7,
0x109bc, 0x109cf,
0x109d2, 0x10a03,
0x10a05, 0x10a06,
0x10a0c, 0x10a13,
0x10a15, 0x10a17,
0x10a19, 0x10a35,
0x10a38, 0x10a3a,
0x10a3f, 0x10a48,
0x10a50, 0x10a58,
0x10a60, 0x10a9f,
0x10ac0, 0x10ae6,
0x10aeb, 0x10af6,
0x10b00, 0x10b35,
0x10b39, 0x10b55,
0x10b58, 0x10b72,
0x10b78, 0x10b91,
0x10b99, 0x10b9c,
0x10ba9, 0x10baf,
0x10c00, 0x10c48,
0x10c80, 0x10cb2,
0x10cc0, 0x10cf2,
0x10cfa, 0x10d27,
0x10d30, 0x10d39,
0x10d40, 0x10d65,
0x10d69, 0x10d85,
0x10d8e, 0x10d8f,
0x10e60, 0x10e7e,
0x10e80, 0x10ea9,
0x10eab, 0x10ead,
0x10eb0, 0x10eb1,
0x10ec2, 0x10ec4,
0x10efc, 0x10f27,
0x10f30, 0x10f59,
0x10f70, 0x10f89,
0x10fb0, 0x10fcb,
0x10fe0, 0x10ff6,
0x11000, 0x1104d,
0x11052, 0x11075,
0x1107f, 0x110c2,
0x110cd, 0x110cd,
0x110d0, 0x110e8,
0x110f0, 0x110f9,
0x11100, 0x11134,
0x11136, 0x11147,
0x11150, 0x11176,
0x11180, 0x111df,
0x111e1, 0x111f4,
0x11200, 0x11211,
0x11213, 0x11241,
0x11280, 0x11286,
0x11288, 0x11288,
0x1128a, 0x1128d,
0x1128f, 0x1129d,
0x1129f, 0x112a9,
0x112b0, 0x112ea,
0x112f0, 0x112f9,
0x11300, 0x11303,
0x11305, 0x1130c,
0x1130f, 0x11310,
0x11313, 0x11328,
0x1132a, 0x11330,
0x11332, 0x11333,
0x11335, 0x11339,
0x1133b, 0x11344,
0x11347, 0x11348,
0x1134b, 0x1134d,
0x11350, 0x11350,
0x11357, 0x11357,
0x1135d, 0x11363,
0x11366, 0x1136c,
0x11370, 0x11374,
0x11380, 0x11389,
0x1138b, 0x1138b,
0x1138e, 0x1138e,
0x11390, 0x113b5,
0x113b7, 0x113c0,
0x113c2, 0x113c2,
0x113c5, 0x113c5,
0x113c7, 0x113ca,
0x113cc, 0x113d5,
0x113d7, 0x113d8,
0x113e1, 0x113e2,
0x11400, 0x1145b,
0x1145d, 0x11461,
0x11480, 0x114c7,
0x114d0, 0x114d9,
0x11580, 0x115b5,
0x115b8, 0x115dd,
0x11600, 0x11644,
0x11650, 0x11659,
0x11660, 0x1166c,
0x11680, 0x116b9,
0x116c0, 0x116c9,
0x116d0, 0x116e3,
0x11700, 0x1171a,
0x1171d, 0x1172b,
0x11730, 0x11746,
0x11800, 0x1183b,
0x118a0, 0x118f2,
0x118ff, 0x11906,
0x11909, 0x11909,
0x1190c, 0x11913,
0x11915, 0x11916,
0x11918, 0x11935,
0x11937, 0x11938,
0x1193b, 0x11946,
0x11950, 0x11959,
0x119a0, 0x119a7,
0x119aa, 0x119d7,
0x119da, 0x119e4,
0x11a00, 0x11a47,
0x11a50, 0x11aa2,
0x11ab0, 0x11af8,
0x11b00, 0x11b09,
0x11bc0, 0x11be1,
0x11bf0, 0x11bf9,
0x11c00, 0x11c08,
0x11c0a, 0x11c36,
0x11c38, 0x11c45,
0x11c50, 0x11c6c,
0x11c70, 0x11c8f,
0x11c92, 0x11ca7,
0x11ca9, 0x11cb6,
0x11d00, 0x11d06,
0x11d08, 0x11d09,
0x11d0b, 0x11d36,
0x11d3a, 0x11d3a,
0x11d3c, 0x11d3d,
0x11d3f, 0x11d47,
0x11d50, 0x11d59,
0x11d60, 0x11d65,
0x11d67, 0x11d68,
0x11d6a, 0x11d8e,
0x11d90, 0x11d91,
0x11d93, 0x11d98,
0x11da0, 0x11da9,
0x11ee0, 0x11ef8,
0x11f00, 0x11f10,
0x11f12, 0x11f3a,
0x11f3e, 0x11f5a,
0x11fb0, 0x11fb0,
0x11fc0, 0x11ff1,
0x11fff, 0x12399,
0x12400, 0x1246e,
0x12470, 0x12474,
0x12480, 0x12543,
0x12f90, 0x12ff2,
0x13000, 0x13455,
0x13460, 0x143fa,
0x14400, 0x14646,
0x16100, 0x16139,
0x16800, 0x16a38,
0x16a40, 0x16a5e,
0x16a60, 0x16a69,
0x16a6e, 0x16abe,
0x16ac0, 0x16ac9,
0x16ad0, 0x16aed,
0x16af0, 0x16af5,
0x16b00, 0x16b45,
0x16b50, 0x16b59,
0x16b5b, 0x16b61,
0x16b63, 0x16b77,
0x16b7d, 0x16b8f,
0x16d40, 0x16d79,
0x16e40, 0x16e9a,
0x16f00, 0x16f4a,
0x16f4f, 0x16f87,
0x16f8f, 0x16f9f,
0x16fe0, 0x16fe4,
0x16ff0, 0x16ff1,
0x17000, 0x187f7,
0x18800, 0x18cd5,
0x18cff, 0x18d08,
0x1aff0, 0x1aff3,
0x1aff5, 0x1affb,
0x1affd, 0x1affe,
0x1b000, 0x1b122,
0x1b132, 0x1b132,
0x1b150, 0x1b152,
0x1b155, 0x1b155,
0x1b164, 0x1b167,
0x1b170, 0x1b2fb,
0x1bc00, 0x1bc6a,
0x1bc70, 0x1bc7c,
0x1bc80, 0x1bc88,
0x1bc90, 0x1bc99,
0x1bc9c, 0x1bca3,
0x1cc00, 0x1ccf9,
0x1cd00, 0x1ceb3,
0x1cf00, 0x1cf2d,
0x1cf30, 0x1cf46,
0x1cf50, 0x1cfc3,
0x1d000, 0x1d0f5,
0x1d100, 0x1d126,
0x1d129, 0x1d1ea,
0x1d200, 0x1d245,
0x1d2c0, 0x1d2d3,
0x1d2e0, 0x1d2f3,
0x1d300, 0x1d356,
0x1d360, 0x1d378,
0x1d400, 0x1d454,
0x1d456, 0x1d49c,
0x1d49e, 0x1d49f,
0x1d4a2, 0x1d4a2,
0x1d4a5, 0x1d4a6,
0x1d4a9, 0x1d4ac,
0x1d4ae, 0x1d4b9,
0x1d4bb, 0x1d4bb,
0x1d4bd, 0x1d4c3,
0x1d4c5, 0x1d505,
0x1d507, 0x1d50a,
0x1d50d, 0x1d514,
0x1d516, 0x1d51c,
0x1d51e, 0x1d539,
0x1d53b, 0x1d53e,
0x1d540, 0x1d544,
0x1d546, 0x1d546,
0x1d54a, 0x1d550,
0x1d552, 0x1d6a5,
0x1d6a8, 0x1d7cb,
0x1d7ce, 0x1da8b,
0x1da9b, 0x1da9f,
0x1daa1, 0x1daaf,
0x1df00, 0x1df1e,
0x1df25, 0x1df2a,
0x1e000, 0x1e006,
0x1e008, 0x1e018,
0x1e01b, 0x1e021,
0x1e023, 0x1e024,
0x1e026, 0x1e02a,
0x1e030, 0x1e06d,
0x1e08f, 0x1e08f,
0x1e100, 0x1e12c,
0x1e130, 0x1e13d,
0x1e140, 0x1e149,
0x1e14e, 0x1e14f,
0x1e290, 0x1e2ae,
0x1e2c0, 0x1e2f9,
0x1e2ff, 0x1e2ff,
0x1e4d0, 0x1e4f9,
0x1e5d0, 0x1e5fa,
0x1e5ff, 0x1e5ff,
0x1e7e0, 0x1e7e6,
0x1e7e8, 0x1e7eb,
0x1e7ed, 0x1e7ee,
0x1e7f0, 0x1e7fe,
0x1e800, 0x1e8c4,
0x1e8c7, 0x1e8d6,
0x1e900, 0x1e94b,
0x1e950, 0x1e959,
0x1e95e, 0x1e95f,
0x1ec71, 0x1ecb4,
0x1ed01, 0x1ed3d,
0x1ee00, 0x1ee03,
0x1ee05, 0x1ee1f,
0x1ee21, 0x1ee22,
0x1ee24, 0x1ee24,
0x1ee27, 0x1ee27,
0x1ee29, 0x1ee32,
0x1ee34, 0x1ee37,
0x1ee39, 0x1ee39,
0x1ee3b, 0x1ee3b,
0x1ee42, 0x1ee42,
0x1ee47, 0x1ee47,
0x1ee49, 0x1ee49,
0x1ee4b, 0x1ee4b,
0x1ee4d, 0x1ee4f,
0x1ee51, 0x1ee52,
0x1ee54, 0x1ee54,
0x1ee57, 0x1ee57,
0x1ee59, 0x1ee59,
0x1ee5b, 0x1ee5b,
0x1ee5d, 0x1ee5d,
0x1ee5f, 0x1ee5f,
0x1ee61, 0x1ee62,
0x1ee64, 0x1ee64,
0x1ee67, 0x1ee6a,
0x1ee6c, 0x1ee72,
0x1ee74, 0x1ee77,
0x1ee79, 0x1ee7c,
0x1ee7e, 0x1ee7e,
0x1ee80, 0x1ee89,
0x1ee8b, 0x1ee9b,
0x1eea1, 0x1eea3,
0x1eea5, 0x1eea9,
0x1eeab, 0x1eebb,
0x1eef0, 0x1eef1,
0x1f000, 0x1f02b,
0x1f030, 0x1f093,
0x1f0a0, 0x1f0ae,
0x1f0b1, 0x1f0bf,
0x1f0c1, 0x1f0cf,
0x1f0d1, 0x1f0f5,
0x1f100, 0x1f1ad,
0x1f1e6, 0x1f202,
0x1f210, 0x1f23b,
0x1f240, 0x1f248,
0x1f250, 0x1f251,
0x1f260, 0x1f265,
0x1f300, 0x1f6d7,
0x1f6dc, 0x1f6ec,
0x1f6f0, 0x1f6fc,
0x1f700, 0x1f776,
0x1f77b, 0x1f7d9,
0x1f7e0, 0x1f7eb,
0x1f7f0, 0x1f7f0,
0x1f800, 0x1f80b,
0x1f810, 0x1f847,
0x1f850, 0x1f859,
0x1f860, 0x1f887,
0x1f890, 0x1f8ad,
0x1f8b0, 0x1f8bb,
0x1f8c0, 0x1f8c1,
0x1f900, 0x1fa53,
0x1fa60, 0x1fa6d,
0x1fa70, 0x1fa7c,
0x1fa80, 0x1fa89,
0x1fa8f, 0x1fac6,
0x1face, 0x1fadc,
0x1fadf, 0x1fae9,
0x1faf0, 0x1faf8,
0x1fb00, 0x1fb92,
0x1fb94, 0x1fbf9,
0x20000, 0x2a6df,
0x2a700, 0x2b739,
0x2b740, 0x2b81d,
0x2b820, 0x2cea1,
0x2ceb0, 0x2ebe0,
0x2ebf0, 0x2ee5d,
0x2f800, 0x2fa1d,
0x30000, 0x3134a,
0x31350, 0x323af,
0xe0001, 0xe0001,
0xe0020, 0xe007f,
0xe0100, 0xe01ef,
0xf0000, 0xffffd,
0x100000, 0x10fffd,
}; /* END of CR_Print */

/* PROPERTY: 'PosixPunct': POSIX [[:punct:]] */
static const OnigCodePoint
CR_PosixPunct[] = { 349,
0x0021, 0x002f,
0x003a, 0x0040,
0x005b, 0x0060,
0x007b, 0x007e,
0x00a1, 0x00a9,
0x00ab, 0x00ac,
0x00ae, 0x00b1,
0x00b4, 0x00b4,
0x00b6, 0x00b8,
0x00bb, 0x00bb,
0x00bf, 0x00bf,
0x00d7, 0x00d7,
0x00f7, 0x00f7,
0x02c2, 0x02c5,
0x02d2, 0x02df,
0x02e5, 0x02eb,
0x02ed, 0x02ed,
0x02ef, 0x02ff,
0x0375, 0x0375,
0x037e, 0x037e,
0x0384, 0x0385,
0x0387, 0x0387,
0x03f6, 0x03f6,
0x0482, 0x0482,
0x055a, 0x055f,
0x0589, 0x058a,
0x058d, 0x058f,
0x05be, 0x05be,
0x05c0, 0x05c0,
0x05c3, 0x05c3,
0x05c6, 0x05c6,
0x05f3, 0x05f4,
0x0606, 0x060f,
0x061b, 0x061b,
0x061d, 0x061f,
0x066a, 0x066d,
0x06d4, 0x06d4,
0x06de, 0x06de,
0x06e9, 0x06e9,
0x06fd, 0x06fe,
0x0700, 0x070d,
0x07f6, 0x07f9,
0x07fe, 0x07ff,
0x0830, 0x083e,
0x085e, 0x085e,
0x0888, 0x0888,
0x0964, 0x0965,
0x0970, 0x0970,
0x09f2, 0x09f3,
0x09fa, 0x09fb,
0x09fd, 0x09fd,
0x0a76, 0x0a76,
0x0af0, 0x0af1,
0x0b70, 0x0b70,
0x0bf3, 0x0bfa,
0x0c77, 0x0c77,
0x0c7f, 0x0c7f,
0x0c84, 0x0c84,
0x0d4f, 0x0d4f,
0x0d79, 0x0d79,
0x0df4, 0x0df4,
0x0e3f, 0x0e3f,
0x0e4f, 0x0e4f,
0x0e5a, 0x0e5b,
0x0f01, 0x0f17,
0x0f1a, 0x0f1f,
0x0f34, 0x0f34,
0x0f36, 0x0f36,
0x0f38, 0x0f38,
0x0f3a, 0x0f3d,
0x0f85, 0x0f85,
0x0fbe, 0x0fc5,
0x0fc7, 0x0fcc,
0x0fce, 0x0fda,
0x104a, 0x104f,
0x109e, 0x109f,
0x10fb, 0x10fb,
0x1360, 0x1368,
0x1390, 0x1399,
0x1400, 0x1400,
0x166d, 0x166e,
0x169b, 0x169c,
0x16eb, 0x16ed,
0x1735, 0x1736,
0x17d4, 0x17d6,
0x17d8, 0x17db,
0x1800, 0x180a,
0x1940, 0x1940,
0x1944, 0x1945,
0x19de, 0x19ff,
0x1a1e, 0x1a1f,
0x1aa0, 0x1aa6,
0x1aa8, 0x1aad,
0x1b4e, 0x1b4f,
0x1b5a, 0x1b6a,
0x1b74, 0x1b7f,
0x1bfc, 0x1bff,
0x1c3b, 0x1c3f,
0x1c7e, 0x1c7f,
0x1cc0, 0x1cc7,
0x1cd3, 0x1cd3,
0x1fbd, 0x1fbd,
0x1fbf, 0x1fc1,
0x1fcd, 0x1fcf,
0x1fdd, 0x1fdf,
0x1fed, 0x1fef,
0x1ffd, 0x1ffe,
0x2010, 0x2027,
0x2030, 0x205e,
0x207a, 0x207e,
0x208a, 0x208e,
0x20a0, 0x20c0,
0x2100, 0x2101,
0x2103, 0x2106,
0x2108, 0x2109,
0x2114, 0x2114,
0x2116, 0x2118,
0x211e, 0x2123,
0x2125, 0x2125,
0x2127, 0x2127,
0x2129, 0x2129,
0x212e, 0x212e,
0x213a, 0x213b,
0x2140, 0x2144,
0x214a, 0x214d,
0x214f, 0x214f,
0x218a, 0x218b,
0x2190, 0x2429,
0x2440, 0x244a,
0x249c, 0x24e9,
0x2500, 0x2775,
0x2794, 0x2b73,
0x2b76, 0x2b95,
0x2b97, 0x2bff,
0x2ce5, 0x2cea,
0x2cf9, 0x2cfc,
0x2cfe, 0x2cff,
0x2d70, 0x2d70,
0x2e00, 0x2e2e,
0x2e30, 0x2e5d,
0x2e80, 0x2e99,
0x2e9b, 0x2ef3,
0x2f00, 0x2fd5,
0x2ff0, 0x2fff,
0x3001, 0x3004,
0x3008, 0x3020,
0x3030, 0x3030,
0x3036, 0x3037,
0x303d, 0x303f,
0x309b, 0x309c,
0x30a0, 0x30a0,
0x30fb, 0x30fb,
0x3190, 0x3191,
0x3196, 0x319f,
0x31c0, 0x31e5,
0x31ef, 0x31ef,
0x3200, 0x321e,
0x322a, 0x3247,
0x3250, 0x3250,
0x3260, 0x327f,
0x328a, 0x32b0,
0x32c0, 0x33ff,
0x4dc0, 0x4dff,
0xa490, 0xa4c6,
0xa4fe, 0xa4ff,
0xa60d, 0xa60f,
0xa673, 0xa673,
0xa67e, 0xa67e,
0xa6f2, 0xa6f7,
0xa700, 0xa716,
0xa720, 0xa721,
0xa789, 0xa78a,
0xa828, 0xa82b,
0xa836, 0xa839,
0xa874, 0xa877,
0xa8ce, 0xa8cf,
0xa8f8, 0xa8fa,
0xa8fc, 0xa8fc,
0xa92e, 0xa92f,
0xa95f, 0xa95f,
0xa9c1, 0xa9cd,
0xa9de, 0xa9df,
0xaa5c, 0xaa5f,
0xaa77, 0xaa79,
0xaade, 0xaadf,
0xaaf0, 0xaaf1,
0xab5b, 0xab5b,
0xab6a, 0xab6b,
0xabeb, 0xabeb,
0xfb29, 0xfb29,
0xfbb2, 0xfbc2,
0xfd3e, 0xfd4f,
0xfdcf, 0xfdcf,
0xfdfc, 0xfdff,
0xfe10, 0xfe19,
0xfe30, 0xfe52,
0xfe54, 0xfe66,
0xfe68, 0xfe6b,
0xff01, 0xff0f,
0xff1a, 0xff20,
0xff3b, 0xff40,
0xff5b, 0xff65,
0xffe0, 0xffe6,
0xffe8, 0xffee,
0xfffc, 0xfffd,
0x10100, 0x10102,
0x10137, 0x1013f,
0x10179, 0x10189,
0x1018c, 0x1018e,
0x10190, 0x1019c,
0x101a0, 0x101a0,
0x101d0, 0x101fc,
0x1039f, 0x1039f,
0x103d0, 0x103d0,
0x1056f, 0x1056f,
0x10857, 0x10857,
0x10877, 0x10878,
0x1091f, 0x1091f,
0x1093f, 0x1093f,
0x10a50, 0x10a58,
0x10a7f, 0x10a7f,
0x10ac8, 0x10ac8,
0x10af0, 0x10af6,
0x10b39, 0x10b3f,
0x10b99, 0x10b9c,
0x10d6e, 0x10d6e,
0x10d8e, 0x10d8f,
0x10ead, 0x10ead,
0x10f55, 0x10f59,
0x10f86, 0x10f89,
0x11047, 0x1104d,
0x110bb, 0x110bc,
0x110be, 0x110c1,
0x11140, 0x11143,
0x11174, 0x11175,
0x111c5, 0x111c8,
0x111cd, 0x111cd,
0x111db, 0x111db,
0x111dd, 0x111df,
0x11238, 0x1123d,
0x112a9, 0x112a9,
0x113d4, 0x113d5,
0x113d7, 0x113d8,
0x1144b, 0x1144f,
0x1145a, 0x1145b,
0x1145d, 0x1145d,
0x114c6, 0x114c6,
0x115c1, 0x115d7,
0x11641, 0x11643,
0x11660, 0x1166c,
0x116b9, 0x116b9,
0x1173c, 0x1173f,
0x1183b, 0x1183b,
0x11944, 0x11946,
0x119e2, 0x119e2,
0x11a3f, 0x11a46,
0x11a9a, 0x11a9c,
0x11a9e, 0x11aa2,
0x11b00, 0x11b09,
0x11be1, 0x11be1,
0x11c41, 0x11c45,
0x11c70, 0x11c71,
0x11ef7, 0x11ef8,
0x11f43, 0x11f4f,
0x11fd5, 0x11ff1,
0x11fff, 0x11fff,
0x12470, 0x12474,
0x12ff1, 0x12ff2,
0x16a6e, 0x16a6f,
0x16af5, 0x16af5,
0x16b37, 0x16b3f,
0x16b44, 0x16b45,
0x16d6d, 0x16d6f,
0x16e97, 0x16e9a,
0x16fe2, 0x16fe2,
0x1bc9c, 0x1bc9c,
0x1bc9f, 0x1bc9f,
0x1cc00, 0x1ccef,
0x1cd00, 0x1ceb3,
0x1cf50, 0x1cfc3,
0x1d000, 0x1d0f5,
0x1d100, 0x1d126,
0x1d129, 0x1d164,
0x1d16a, 0x1d16c,
0x1d183, 0x1d184,
0x1d18c, 0x1d1a9,
0x1d1ae, 0x1d1ea,
0x1d200, 0x1d241,
0x1d245, 0x1d245,
0x1d300, 0x1d356,
0x1d6c1, 0x1d6c1,
0x1d6db, 0x1d6db,
0x1d6fb, 0x1d6fb,
0x1d715, 0x1d715,
0x1d735, 0x1d735,
0x1d74f, 0x1d74f,
0x1d76f, 0x1d76f,
0x1d789, 0x1d789,
0x1d7a9, 0x1d7a9,
0x1d7c3, 0x1d7c3,
0x1d800, 0x1d9ff,
0x1da37, 0x1da3a,
0x1da6d, 0x1da74,
0x1da76, 0x1da83,
0x1da85, 0x1da8b,
0x1e14f, 0x1e14f,
0x1e2ff, 0x1e2ff,
0x1e5ff, 0x1e5ff,
0x1e95e, 0x1e95f,
0x1ecac, 0x1ecac,
0x1ecb0, 0x1ecb0,
0x1ed2e, 0x1ed2e,
0x1eef0, 0x1eef1,
0x1f000, 0x1f02b,
0x1f030, 0x1f093,
0x1f0a0, 0x1f0ae,
0x1f0b1, 0x1f0bf,
0x1f0c1, 0x1f0cf,
0x1f0d1, 0x1f0f5,
0x1f10d, 0x1f1ad,
0x1f1e6, 0x1f202,
0x1f210, 0x1f23b,
0x1f240, 0x1f248,
0x1f250, 0x1f251,
0x1f260, 0x1f265,
0x1f300, 0x1f6d7,
0x1f6dc, 0x1f6ec,
0x1f6f0, 0x1f6fc,
0x1f700, 0x1f776,
0x1f77b, 0x1f7d9,
0x1f7e0, 0x1f7eb,
0x1f7f0, 0x1f7f0,
0x1f800, 0x1f80b,
0x1f810, 0x1f847,
0x1f850, 0x1f859,
0x1f860, 0x1f887,
0x1f890, 0x1f8ad,
0x1f8b0, 0x1f8bb,
0x1f8c0, 0x1f8c1,
0x1f900, 0x1fa53,
0x1fa60, 0x1fa6d,
0x1fa70, 0x1fa7c,
0x1fa80, 0x1fa89,
0x1fa8f, 0x1fac6,
0x1face, 0x1fadc,
0x1fadf, 0x1fae9,
0x1faf0, 0x1faf8,
0x1fb00, 0x1fb92,
0x1fb94, 0x1fbef,
}; /* END of CR_PosixPunct */

/* PROPERTY: 'Space': POSIX [[:Space:]] */
static const OnigCodePoint
CR_Space[] = { 10,
0x0009, 0x000d,
0x0020, 0x0020,
0x0085, 0x0085,
0x00a0, 0x00a0,
0x1680, 0x1680,
0x2000, 0x200a,
0x2028, 0x2029,
0x202f, 0x202f,
0x205f, 0x205f,
0x3000, 0x3000,
}; /* END of CR_Space */

/* PROPERTY: 'Upper': POSIX [[:Upper:]] */
static const OnigCodePoint
CR_Upper[] = { 656,
0x0041, 0x005a,
0x00c0, 0x00d6,
0x00d8, 0x00de,
0x0100, 0x0100,
0x0102, 0x0102,
0x0104, 0x0104,
0x0106, 0x0106,
0x0108, 0x0108,
0x010a, 0x010a,
0x010c, 0x010c,
0x010e, 0x010e,
0x0110, 0x0110,
0x0112, 0x0112,
0x0114, 0x0114,
0x0116, 0x0116,
0x0118, 0x0118,
0x011a, 0x011a,
0x011c, 0x011c,
0x011e, 0x011e,
0x0120, 0x0120,
0x0122, 0x0122,
0x0124, 0x0124,
0x0126, 0x0126,
0x0128, 0x0128,
0x012a, 0x012a,
0x012c, 0x012c,
0x012e, 0x012e,
0x0130, 0x0130,
0x0132, 0x0132,
0x0134, 0x0134,
0x0136, 0x0136,
0x0139, 0x0139,
0x013b, 0x013b,
0x013d, 0x013d,
0x013f, 0x013f,
0x0141, 0x0141,
0x0143, 0x0143,
0x0145, 0x0145,
0x0147, 0x0147,
0x014a, 0x014a,
0x014c, 0x014c,
0x014e, 0x014e,
0x0150, 0x0150,
0x0152, 0x0152,
0x0154, 0x0154,
0x0156, 0x0156,
0x0158, 0x0158,
0x015a, 0x015a,
0x015c, 0x015c,
0x015e, 0x015e,
0x0160, 0x0160,
0x0162, 0x0162,
0x0164, 0x0164,
0x0166, 0x0166,
0x0168, 0x0168,
0x016a, 0x016a,
0x016c, 0x016c,
0x016e, 0x016e,
0x0170, 0x0170,
0x0172, 0x0172,
0x0174, 0x0174,
0x0176, 0x0176,
0x0178, 0x0179,
0x017b, 0x017b,
0x017d, 0x017d,
0x0181, 0x0182,
0x0184, 0x0184,
0x0186, 0x0187,
0x0189, 0x018b,
0x018e, 0x0191,
0x0193, 0x0194,
0x0196, 0x0198,
0x019c, 0x019d,
0x019f, 0x01a0,
0x01a2, 0x01a2,
0x01a4, 0x01a4,
0x01a6, 0x01a7,
0x01a9, 0x01a9,
0x01ac, 0x01ac,
0x01ae, 0x01af,
0x01b1, 0x01b3,
0x01b5, 0x01b5,
0x01b7, 0x01b8,
0x01bc, 0x01bc,
0x01c4, 0x01c4,
0x01c7, 0x01c7,
0x01ca, 0x01ca,
0x01cd, 0x01cd,
0x01cf, 0x01cf,
0x01d1, 0x01d1,
0x01d3, 0x01d3,
0x01d5, 0x01d5,
0x01d7, 0x01d7,
0x01d9, 0x01d9,
0x01db, 0x01db,
0x01de, 0x01de,
0x01e0, 0x01e0,
0x01e2, 0x01e2,
0x01e4, 0x01e4,
0x01e6, 0x01e6,
0x01e8, 0x01e8,
0x01ea, 0x01ea,
0x01ec, 0x01ec,
0x01ee, 0x01ee,
0x01f1, 0x01f1,
0x01f4, 0x01f4,
0x01f6, 0x01f8,
0x01fa, 0x01fa,
0x01fc, 0x01fc,
0x01fe, 0x01fe,
0x0200, 0x0200,
0x0202, 0x0202,
0x0204, 0x0204,
0x0206, 0x0206,
0x0208, 0x0208,
0x020a, 0x020a,
0x020c, 0x020c,
0x020e, 0x020e,
0x0210, 0x0210,
0x0212, 0x0212,
0x0214, 0x0214,
0x0216, 0x0216,
0x0218, 0x0218,
0x021a, 0x021a,
0x021c, 0x021c,
0x021e, 0x021e,
0x0220, 0x0220,
0x0222, 0x0222,
0x0224, 0x0224,
0x0226, 0x0226,
0x0228, 0x0228,
0x022a, 0x022a,
0x022c, 0x022c,
0x022e, 0x022e,
0x0230, 0x0230,
0x0232, 0x0232,
0x023a, 0x023b,
0x023d, 0x023e,
0x0241, 0x0241,
0x0243, 0x0246,
0x0248, 0x0248,
0x024a, 0x024a,
0x024c, 0x024c,
0x024e, 0x024e,
0x0370, 0x0370,
0x0372, 0x0372,
0x0376, 0x0376,
0x037f, 0x037f,
0x0386, 0x0386,
0x0388, 0x038a,
0x038c, 0x038c,
0x038e, 0x038f,
0x0391, 0x03a1,
0x03a3, 0x03ab,
0x03cf, 0x03cf,
0x03d2, 0x03d4,
0x03d8, 0x03d8,
0x03da, 0x03da,
0x03dc, 0x03dc,
0x03de, 0x03de,
0x03e0, 0x03e0,
0x03e2, 0x03e2,
0x03e4, 0x03e4,
0x03e6, 0x03e6,
0x03e8, 0x03e8,
0x03ea, 0x03ea,
0x03ec, 0x03ec,
0x03ee, 0x03ee,
0x03f4, 0x03f4,
0x03f7, 0x03f7,
0x03f9, 0x03fa,
0x03fd, 0x042f,
0x0460, 0x0460,
0x0462, 0x0462,
0x0464, 0x0464,
0x0466, 0x0466,
0x0468, 0x0468,
0x046a, 0x046a,
0x046c, 0x046c,
0x046e, 0x046e,
0x0470, 0x0470,
0x0472, 0x0472,
0x0474, 0x0474,
0x0476, 0x0476,
0x0478, 0x0478,
0x047a, 0x047a,
0x047c, 0x047c,
0x047e, 0x047e,
0x0480, 0x0480,
0x048a, 0x048a,
0x048c, 0x048c,
0x048e, 0x048e,
0x0490, 0x0490,
0x0492, 0x0492,
0x0494, 0x0494,
0x0496, 0x0496,
0x0498, 0x0498,
0x049a, 0x049a,
0x049c, 0x049c,
0x049e, 0x049e,
0x04a0, 0x04a0,
0x04a2, 0x04a2,
0x04a4, 0x04a4,
0x04a6, 0x04a6,
0x04a8, 0x04a8,
0x04aa, 0x04aa,
0x04ac, 0x04ac,
0x04ae, 0x04ae,
0x04b0, 0x04b0,
0x04b2, 0x04b2,
0x04b4, 0x04b4,
0x04b6, 0x04b6,
0x04b8, 0x04b8,
0x04ba, 0x04ba,
0x04bc, 0x04bc,
0x04be, 0x04be,
0x04c0, 0x04c1,
0x04c3, 0x04c3,
0x04c5, 0x04c5,
0x04c7, 0x04c7,
0x04c9, 0x04c9,
0x04cb, 0x04cb,
0x04cd, 0x04cd,
0x04d0, 0x04d0,
0x04d2, 0x04d2,
0x04d4, 0x04d4,
0x04d6, 0x04d6,
0x04d8, 0x04d8,
0x04da, 0x04da,
0x04dc, 0x04dc,
0x04de, 0x04de,
0x04e0, 0x04e0,
0x04e2, 0x04e2,
0x04e4, 0x04e4,
0x04e6, 0x04e6,
0x04e8, 0x04e8,
0x04ea, 0x04ea,
0x04ec, 0x04ec,
0x04ee, 0x04ee,
0x04f0, 0x04f0,
0x04f2, 0x04f2,
0x04f4, 0x04f4,
0x04f6, 0x04f6,
0x04f8, 0x04f8,
0x04fa, 0x04fa,
0x04fc, 0x04fc,
0x04fe, 0x04fe,
0x0500, 0x0500,
0x0502, 0x0502,
0x0504, 0x0504,
0x0506, 0x0506,
0x0508, 0x0508,
0x050a, 0x050a,
0x050c, 0x050c,
0x050e, 0x050e,
0x0510, 0x0510,
0x0512, 0x0512,
0x0514, 0x0514,
0x0516, 0x0516,
0x0518, 0x0518,
0x051a, 0x051a,
0x051c, 0x051c,
0x051e, 0x051e,
0x0520, 0x0520,
0x0522, 0x0522,
0x0524, 0x0524,
0x0526, 0x0526,
0x0528, 0x0528,
0x052a, 0x052a,
0x052c, 0x052c,
0x052e, 0x052e,
0x0531, 0x0556,
0x10a0, 0x10c5,
0x10c7, 0x10c7,
0x10cd, 0x10cd,
0x13a0, 0x13f5,
0x1c89, 0x1c89,
0x1c90, 0x1cba,
0x1cbd, 0x1cbf,
0x1e00, 0x1e00,
0x1e02, 0x1e02,
0x1e04, 0x1e04,
0x1e06, 0x1e06,
0x1e08, 0x1e08,
0x1e0a, 0x1e0a,
0x1e0c, 0x1e0c,
0x1e0e, 0x1e0e,
0x1e10, 0x1e10,
0x1e12, 0x1e12,
0x1e14, 0x1e14,
0x1e16, 0x1e16,
0x1e18, 0x1e18,
0x1e1a, 0x1e1a,
0x1e1c, 0x1e1c,
0x1e1e, 0x1e1e,
0x1e20, 0x1e20,
0x1e22, 0x1e22,
0x1e24, 0x1e24,
0x1e26, 0x1e26,
0x1e28, 0x1e28,
0x1e2a, 0x1e2a,
0x1e2c, 0x1e2c,
0x1e2e, 0x1e2e,
0x1e30, 0x1e30,
0x1e32, 0x1e32,
0x1e34, 0x1e34,
0x1e36, 0x1e36,
0x1e38, 0x1e38,
0x1e3a, 0x1e3a,
0x1e3c, 0x1e3c,
0x1e3e, 0x1e3e,
0x1e40, 0x1e40,
0x1e42, 0x1e42,
0x1e44, 0x1e44,
0x1e46, 0x1e46,
0x1e48, 0x1e48,
0x1e4a, 0x1e4a,
0x1e4c, 0x1e4c,
0x1e4e, 0x1e4e,
0x1e50, 0x1e50,
0x1e52, 0x1e52,
0x1e54, 0x1e54,
0x1e56, 0x1e56,
0x1e58, 0x1e58,
0x1e5a, 0x1e5a,
0x1e5c, 0x1e5c,
0x1e5e, 0x1e5e,
0x1e60, 0x1e60,
0x1e62, 0x1e62,
0x1e64, 0x1e64,
0x1e66, 0x1e66,
0x1e68, 0x1e68,
0x1e6a, 0x1e6a,
0x1e6c, 0x1e6c,
0x1e6e, 0x1e6e,
0x1e70, 0x1e70,
0x1e72, 0x1e72,
0x1e74, 0x1e74,
0x1e76, 0x1e76,
0x1e78, 0x1e78,
0x1e7a, 0x1e7a,
0x1e7c, 0x1e7c,
0x1e7e, 0x1e7e,
0x1e80, 0x1e80,
0x1e82, 0x1e82,
0x1e84, 0x1e84,
0x1e86, 0x1e86,
0x1e88, 0x1e88,
0x1e8a, 0x1e8a,
0x1e8c, 0x1e8c,
0x1e8e, 0x1e8e,
0x1e90, 0x1e90,
0x1e92, 0x1e92,
0x1e94, 0x1e94,
0x1e9e, 0x1e9e,
0x1ea0, 0x1ea0,
0x1ea2, 0x1ea2,
0x1ea4, 0x1ea4,
0x1ea6, 0x1ea6,
0x1ea8, 0x1ea8,
0x1eaa, 0x1eaa,
0x1eac, 0x1eac,
0x1eae, 0x1eae,
0x1eb0, 0x1eb0,
0x1eb2, 0x1eb2,
0x1eb4, 0x1eb4,
0x1eb6, 0x1eb6,
0x1eb8, 0x1eb8,
0x1eba, 0x1eba,
0x1ebc, 0x1ebc,
0x1ebe, 0x1ebe,
0x1ec0, 0x1ec0,
0x1ec2, 0x1ec2,
0x1ec4, 0x1ec4,
0x1ec6, 0x1ec6,
0x1ec8, 0x1ec8,
0x1eca, 0x1eca,
0x1ecc, 0x1ecc,
0x1ece, 0x1ece,
0x1ed0, 0x1ed0,
0x1ed2, 0x1ed2,
0x1ed4, 0x1ed4,
0x1ed6, 0x1ed6,
0x1ed8, 0x1ed8,
0x1eda, 0x1eda,
0x1edc, 0x1edc,
0x1ede, 0x1ede,
0x1ee0, 0x1ee0,
0x1ee2, 0x1ee2,
0x1ee4, 0x1ee4,
0x1ee6, 0x1ee6,
0x1ee8, 0x1ee8,
0x1eea, 0x1eea,
0x1eec, 0x1eec,
0x1eee, 0x1eee,
0x1ef0, 0x1ef0,
0x1ef2, 0x1ef2,
0x1ef4, 0x1ef4,
0x1ef6, 0x1ef6,
0x1ef8, 0x1ef8,
0x1efa, 0x1efa,
0x1efc, 0x1efc,
0x1efe, 0x1efe,
0x1f08, 0x1f0f,
0x1f18, 0x1f1d,
0x1f28, 0x1f2f,
0x1f38, 0x1f3f,
0x1f48, 0x1f4d,
0x1f59, 0x1f59,
0x1f5b, 0x1f5b,
0x1f5d, 0x1f5d,
0x1f5f, 0x1f5f,
0x1f68, 0x1f6f,
0x1fb8, 0x1fbb,
0x1fc8, 0x1fcb,
0x1fd8, 0x1fdb,
0x1fe8, 0x1fec,
0x1ff8, 0x1ffb,
0x2102, 0x2102,
0x2107, 0x2107,
0x210b, 0x210d,
0x2110, 0x2112,
0x2115, 0x2115,
0x2119, 0x211d,
0x2124, 0x2124,
0x2126, 0x2126,
0x2128, 0x2128,
0x212a, 0x212d,
0x2130, 0x2133,
0x213e, 0x213f,
0x2145, 0x2145,
0x2160, 0x216f,
0x2183, 0x2183,
0x24b6, 0x24cf,
0x2c00, 0x2c2f,
0x2c60, 0x2c60,
0x2c62, 0x2c64,
0x2c67, 0x2c67,
0x2c69, 0x2c69,
0x2c6b, 0x2c6b,
0x2c6d, 0x2c70,
0x2c72, 0x2c72,
0x2c75, 0x2c75,
0x2c7e, 0x2c80,
0x2c82, 0x2c82,
0x2c84, 0x2c84,
0x2c86, 0x2c86,
0x2c88, 0x2c88,
0x2c8a, 0x2c8a,
0x2c8c, 0x2c8c,
0x2c8e, 0x2c8e,
0x2c90, 0x2c90,
0x2c92, 0x2c92,
0x2c94, 0x2c94,
0x2c96, 0x2c96,
0x2c98, 0x2c98,
0x2c9a, 0x2c9a,
0x2c9c, 0x2c9c,
0x2c9e, 0x2c9e,
0x2ca0, 0x2ca0,
0x2ca2, 0x2ca2,
0x2ca4, 0x2ca4,
0x2ca6, 0x2ca6,
0x2ca8, 0x2ca8,
0x2caa, 0x2caa,
0x2cac, 0x2cac,
0x2cae, 0x2cae,
0x2cb0, 0x2cb0,
0x2cb2, 0x2cb2,
0x2cb4, 0x2cb4,
0x2cb6, 0x2cb6,
0x2cb8, 0x2cb8,
0x2cba, 0x2cba,
0x2cbc, 0x2cbc,
0x2cbe, 0x2cbe,
0x2cc0, 0x2cc0,
0x2cc2, 0x2cc2,
0x2cc4, 0x2cc4,
0x2cc6, 0x2cc6,
0x2cc8, 0x2cc8,
0x2cca, 0x2cca,
0x2ccc, 0x2ccc,
0x2cce, 0x2cce,
0x2cd0, 0x2cd0,
0x2cd2, 0x2cd2,
0x2cd4, 0x2cd4,
0x2cd6, 0x2cd6,
0x2cd8, 0x2cd8,
0x2cda, 0x2cda,
0x2cdc, 0x2cdc,
0x2cde, 0x2cde,
0x2ce0, 0x2ce0,
0x2ce2, 0x2ce2,
0x2ceb, 0x2ceb,
0x2ced, 0x2ced,
0x2cf2, 0x2cf2,
0xa640, 0xa640,
0xa642, 0xa642,
0xa644, 0xa644,
0xa646, 0xa646,
0xa648, 0xa648,
0xa64a, 0xa64a,
0xa64c, 0xa64c,
0xa64e, 0xa64e,
0xa650, 0xa650,
0xa652, 0xa652,
0xa654, 0xa654,
0xa656, 0xa656,
0xa658, 0xa658,
0xa65a, 0xa65a,
0xa65c, 0xa65c,
0xa65e, 0xa65e,
0xa660, 0xa660,
0xa662, 0xa662,
0xa664, 0xa664,
0xa666, 0xa666,
0xa668, 0xa668,
0xa66a, 0xa66a,
0xa66c, 0xa66c,
0xa680, 0xa680,
0xa682, 0xa682,
0xa684, 0xa684,
0xa686, 0xa686,
0xa688, 0xa688,
0xa68a, 0xa68a,
0xa68c, 0xa68c,
0xa68e, 0xa68e,
0xa690, 0xa690,
0xa692, 0xa692,
0xa694, 0xa694,
0xa696, 0xa696,
0xa698, 0xa698,
0xa69a, 0xa69a,
0xa722, 0xa722,
0xa724, 0xa724,
0xa726, 0xa726,
0xa728, 0xa728,
0xa72a, 0xa72a,
0xa72c, 0xa72c,
0xa72e, 0xa72e,
0xa732, 0xa732,
0xa734, 0xa734,
0xa736, 0xa736,
0xa738, 0xa738,
0xa73a, 0xa73a,
0xa73c, 0xa73c,
0xa73e, 0xa73e,
0xa740, 0xa740,
0xa742, 0xa742,
0xa744, 0xa744,
0xa746, 0xa746,
0xa748, 0xa748,
0xa74a, 0xa74a,
0xa74c, 0xa74c,
0xa74e, 0xa74e,
0xa750, 0xa750,
0xa752, 0xa752,
0xa754, 0xa754,
0xa756, 0xa756,
0xa758, 0xa758,
0xa75a, 0xa75a,
0xa75c, 0xa75c,
0xa75e, 0xa75e,
0xa760, 0xa760,
0xa762, 0xa762,
0xa764, 0xa764,
0xa766, 0xa766,
0xa768, 0xa768,
0xa76a, 0xa76a,
0xa76c, 0xa76c,
0xa76e, 0xa76e,
0xa779, 0xa779,
0xa77b, 0xa77b,
0xa77d, 0xa77e,
0xa780, 0xa780,
0xa782, 0xa782,
0xa784, 0xa784,
0xa786, 0xa786,
0xa78b, 0xa78b,
0xa78d, 0xa78d,
0xa790, 0xa790,
0xa792, 0xa792,
0xa796, 0xa796,
0xa798, 0xa798,
0xa79a, 0xa79a,
0xa79c, 0xa79c,
0xa79e, 0xa79e,
0xa7a0, 0xa7a0,
0xa7a2, 0xa7a2,
0xa7a4, 0xa7a4,
0xa7a6, 0xa7a6,
0xa7a8, 0xa7a8,
0xa7aa, 0xa7ae,
0xa7b0, 0xa7b4,
0xa7b6, 0xa7b6,
0xa7b8, 0xa7b8,
0xa7ba, 0xa7ba,
0xa7bc, 0xa7bc,
0xa7be, 0xa7be,
0xa7c0, 0xa7c0,
0xa7c2, 0xa7c2,
0xa7c4, 0xa7c7,
0xa7c9, 0xa7c9,
0xa7cb, 0xa7cc,
0xa7d0, 0xa7d0,
0xa7d6, 0xa7d6,
0xa7d8, 0xa7d8,
0xa7da, 0xa7da,
0xa7dc, 0xa7dc,
0xa7f5, 0xa7f5,
0xff21, 0xff3a,
0x10400, 0x10427,
0x104b0, 0x104d3,
0x10570, 0x1057a,
0x1057c, 0x1058a,
0x1058c, 0x10592,
0x10594, 0x10595,
0x10c80, 0x10cb2,
0x10d50, 0x10d65,
0x118a0, 0x118bf,
0x16e40, 0x16e5f,
0x1d400, 0x1d419,
0x1d434, 0x1d44d,
0x1d468, 0x1d481,
0x1d49c, 0x1d49c,
0x1d49e, 0x1d49f,
0x1d4a2, 0x1d4a2,
0x1d4a5, 0x1d4a6,
0x1d4a9, 0x1d4ac,
0x1d4ae, 0x1d4b5,
0x1d4d0, 0x1d4e9,
0x1d504, 0x1d505,
0x1d507, 0x1d50a,
0x1d50d, 0x1d514,
0x1d516, 0x1d51c,
0x1d538, 0x1d539,
0x1d53b, 0x1d53e,
0x1d540, 0x1d544,
0x1d546, 0x1d546,
0x1d54a, 0x1d550,
0x1d56c, 0x1d585,
0x1d5a0, 0x1d5b9,
0x1d5d4, 0x1d5ed,
0x1d608, 0x1d621,
0x1d63c, 0x1d655,
0x1d670, 0x1d689,
0x1d6a8, 0x1d6c0,
0x1d6e2, 0x1d6fa,
0x1d71c, 0x1d734,
0x1d756, 0x1d76e,
0x1d790, 0x1d7a8,
0x1d7ca, 0x1d7ca,
0x1e900, 0x1e921,
0x1f130, 0x1f149,
0x1f150, 0x1f169,
0x1f170, 0x1f189,
}; /* END of CR_Upper */

/* PROPERTY: 'XDigit': POSIX [[:XDigit:]] */
static const OnigCodePoint
CR_XDigit[] = { 3,
0x0030, 0x0039,
0x0041, 0x0046,
0x0061, 0x0066,
}; /* END of CR_XDigit */

/* PROPERTY: 'Word': POSIX [[:Word:]] */
static const OnigCodePoint
CR_Word[] = { 795,
0x0030, 0x0039,
0x0041, 0x005a,
0x005f, 0x005f,
0x0061, 0x007a,
0x00aa, 0x00aa,
0x00b5, 0x00b5,
0x00ba, 0x00ba,
0x00c0, 0x00d6,
0x00d8, 0x00f6,
0x00f8, 0x02c1,
0x02c6, 0x02d1,
0x02e0, 0x02e4,
0x02ec, 0x02ec,
0x02ee, 0x02ee,
0x0300, 0x0374,
0x0376, 0x0377,
0x037a, 0x037d,
0x037f, 0x037f,
0x0386, 0x0386,
0x0388, 0x038a,
0x038c, 0x038c,
0x038e, 0x03a1,
0x03a3, 0x03f5,
0x03f7, 0x0481,
0x0483, 0x052f,
0x0531, 0x0556,
0x0559, 0x0559,
0x0560, 0x0588,
0x0591, 0x05bd,
0x05bf, 0x05bf,
0x05c1, 0x05c2,
0x05c4, 0x05c5,
0x05c7, 0x05c7,
0x05d0, 0x05ea,
0x05ef, 0x05f2,
0x0610, 0x061a,
0x0620, 0x0669,
0x066e, 0x06d3,
0x06d5, 0x06dc,
0x06df, 0x06e8,
0x06ea, 0x06fc,
0x06ff, 0x06ff,
0x0710, 0x074a,
0x074d, 0x07b1,
0x07c0, 0x07f5,
0x07fa, 0x07fa,
0x07fd, 0x07fd,
0x0800, 0x082d,
0x0840, 0x085b,
0x0860, 0x086a,
0x0870, 0x0887,
0x0889, 0x088e,
0x0897, 0x08e1,
0x08e3, 0x0963,
0x0966, 0x096f,
0x0971, 0x0983,
0x0985, 0x098c,
0x098f, 0x0990,
0x0993, 0x09a8,
0x09aa, 0x09b0,
0x09b2, 0x09b2,
0x09b6, 0x09b9,
0x09bc, 0x09c4,
0x09c7, 0x09c8,
0x09cb, 0x09ce,
0x09d7, 0x09d7,
0x09dc, 0x09dd,
0x09df, 0x09e3,
0x09e6, 0x09f1,
0x09fc, 0x09fc,
0x09fe, 0x09fe,
0x0a01, 0x0a03,
0x0a05, 0x0a0a,
0x0a0f, 0x0a10,
0x0a13, 0x0a28,
0x0a2a, 0x0a30,
0x0a32, 0x0a33,
0x0a35, 0x0a36,
0x0a38, 0x0a39,
0x0a3c, 0x0a3c,
0x0a3e, 0x0a42,
0x0a47, 0x0a48,
0x0a4b, 0x0a4d,
0x0a51, 0x0a51,
0x0a59, 0x0a5c,
0x0a5e, 0x0a5e,
0x0a66, 0x0a75,
0x0a81, 0x0a83,
0x0a85, 0x0a8d,
0x0a8f, 0x0a91,
0x0a93, 0x0aa8,
0x0aaa, 0x0ab0,
0x0ab2, 0x0ab3,
0x0ab5, 0x0ab9,
0x0abc, 0x0ac5,
0x0ac7, 0x0ac9,
0x0acb, 0x0acd,
0x0ad0, 0x0ad0,
0x0ae0, 0x0ae3,
0x0ae6, 0x0aef,
0x0af9, 0x0aff,
0x0b01, 0x0b03,
0x0b05, 0x0b0c,
0x0b0f, 0x0b10,
0x0b13, 0x0b28,
0x0b2a, 0x0b30,
0x0b32, 0x0b33,
0x0b35, 0x0b39,
0x0b3c, 0x0b44,
0x0b47, 0x0b48,
0x0b4b, 0x0b4d,
0x0b55, 0x0b57,
0x0b5c, 0x0b5d,
0x0b5f, 0x0b63,
0x0b66, 0x0b6f,
0x0b71, 0x0b71,
0x0b82, 0x0b83,
0x0b85, 0x0b8a,
0x0b8e, 0x0b90,
0x0b92, 0x0b95,
0x0b99, 0x0b9a,
0x0b9c, 0x0b9c,
0x0b9e, 0x0b9f,
0x0ba3, 0x0ba4,
0x0ba8, 0x0baa,
0x0bae, 0x0bb9,
0x0bbe, 0x0bc2,
0x0bc6, 0x0bc8,
0x0bca, 0x0bcd,
0x0bd0, 0x0bd0,
0x0bd7, 0x0bd7,
0x0be6, 0x0bef,
0x0c00, 0x0c0c,
0x0c0e, 0x0c10,
0x0c12, 0x0c28,
0x0c2a, 0x0c39,
0x0c3c, 0x0c44,
0x0c46, 0x0c48,
0x0c4a, 0x0c4d,
0x0c55, 0x0c56,
0x0c58, 0x0c5a,
0x0c5d, 0x0c5d,
0x0c60, 0x0c63,
0x0c66, 0x0c6f,
0x0c80, 0x0c83,
0x0c85, 0x0c8c,
0x0c8e, 0x0c90,
0x0c92, 0x0ca8,
0x0caa, 0x0cb3,
0x0cb5, 0x0cb9,
0x0cbc, 0x0cc4,
0x0cc6, 0x0cc8,
0x0cca, 0x0ccd,
0x0cd5, 0x0cd6,
0x0cdd, 0x0cde,
0x0ce0, 0x0ce3,
0x0ce6, 0x0cef,
0x0cf1, 0x0cf3,
0x0d00, 0x0d0c,
0x0d0e, 0x0d10,
0x0d12, 0x0d44,
0x0d46, 0x0d48,
0x0d4a, 0x0d4e,
0x0d54, 0x0d57,
0x0d5f, 0x0d63,
0x0d66, 0x0d6f,
0x0d7a, 0x0d7f,
0x0d81, 0x0d83,
0x0d85, 0x0d96,
0x0d9a, 0x0db1,
0x0db3, 0x0dbb,
0x0dbd, 0x0dbd,
0x0dc0, 0x0dc6,
0x0dca, 0x0dca,
0x0dcf, 0x0dd4,
0x0dd6, 0x0dd6,
0x0dd8, 0x0ddf,
0x0de6, 0x0def,
0x0df2, 0x0df3,
0x0e01, 0x0e3a,
0x0e40, 0x0e4e,
0x0e50, 0x0e59,
0x0e81, 0x0e82,
0x0e84, 0x0e84,
0x0e86, 0x0e8a,
0x0e8c, 0x0ea3,
0x0ea5, 0x0ea5,
0x0ea7, 0x0ebd,
0x0ec0, 0x0ec4,
0x0ec6, 0x0ec6,
0x0ec8, 0x0ece,
0x0ed0, 0x0ed9,
0x0edc, 0x0edf,
0x0f00, 0x0f00,
0x0f18, 0x0f19,
0x0f20, 0x0f29,
0x0f35, 0x0f35,
0x0f37, 0x0f37,
0x0f39, 0x0f39,
0x0f3e, 0x0f47,
0x0f49, 0x0f6c,
0x0f71, 0x0f84,
0x0f86, 0x0f97,
0x0f99, 0x0fbc,
0x0fc6, 0x0fc6,
0x1000, 0x1049,
0x1050, 0x109d,
0x10a0, 0x10c5,
0x10c7, 0x10c7,
0x10cd, 0x10cd,
0x10d0, 0x10fa,
0x10fc, 0x1248,
0x124a, 0x124d,
0x1250, 0x1256,
0x1258, 0x1258,
0x125a, 0x125d,
0x1260, 0x1288,
0x128a, 0x128d,
0x1290, 0x12b0,
0x12b2, 0x12b5,
0x12b8, 0x12be,
0x12c0, 0x12c0,
0x12c2, 0x12c5,
0x12c8, 0x12d6,
0x12d8, 0x1310,
0x1312, 0x1315,
0x1318, 0x135a,
0x135d, 0x135f,
0x1380, 0x138f,
0x13a0, 0x13f5,
0x13f8, 0x13fd,
0x1401, 0x166c,
0x166f, 0x167f,
0x1681, 0x169a,
0x16a0, 0x16ea,
0x16ee, 0x16f8,
0x1700, 0x1715,
0x171f, 0x1734,
0x1740, 0x1753,
0x1760, 0x176c,
0x176e, 0x1770,
0x1772, 0x1773,
0x1780, 0x17d3,
0x17d7, 0x17d7,
0x17dc, 0x17dd,
0x17e0, 0x17e9,
0x180b, 0x180d,
0x180f, 0x1819,
0x1820, 0x1878,
0x1880, 0x18aa,
0x18b0, 0x18f5,
0x1900, 0x191e,
0x1920, 0x192b,
0x1930, 0x193b,
0x1946, 0x196d,
0x1970, 0x1974,
0x1980, 0x19ab,
0x19b0, 0x19c9,
0x19d0, 0x19d9,
0x1a00, 0x1a1b,
0x1a20, 0x1a5e,
0x1a60, 0x1a7c,
0x1a7f, 0x1a89,
0x1a90, 0x1a99,
0x1aa7, 0x1aa7,
0x1ab0, 0x1ace,
0x1b00, 0x1b4c,
0x1b50, 0x1b59,
0x1b6b, 0x1b73,
0x1b80, 0x1bf3,
0x1c00, 0x1c37,
0x1c40, 0x1c49,
0x1c4d, 0x1c7d,
0x1c80, 0x1c8a,
0x1c90, 0x1cba,
0x1cbd, 0x1cbf,
0x1cd0, 0x1cd2,
0x1cd4, 0x1cfa,
0x1d00, 0x1f15,
0x1f18, 0x1f1d,
0x1f20, 0x1f45,
0x1f48, 0x1f4d,
0x1f50, 0x1f57,
0x1f59, 0x1f59,
0x1f5b, 0x1f5b,
0x1f5d, 0x1f5d,
0x1f5f, 0x1f7d,
0x1f80, 0x1fb4,
0x1fb6, 0x1fbc,
0x1fbe, 0x1fbe,
0x1fc2, 0x1fc4,
0x1fc6, 0x1fcc,
0x1fd0, 0x1fd3,
0x1fd6, 0x1fdb,
0x1fe0, 0x1fec,
0x1ff2, 0x1ff4,
0x1ff6, 0x1ffc,
0x203f, 0x2040,
0x2054, 0x2054,
0x2071, 0x2071,
0x207f, 0x207f,
0x2090, 0x209c,
0x20d0, 0x20f0,
0x2102, 0x2102,
0x2107, 0x2107,
0x210a, 0x2113,
0x2115, 0x2115,
0x2119, 0x211d,
0x2124, 0x2124,
0x2126, 0x2126,
0x2128, 0x2128,
0x212a, 0x212d,
0x212f, 0x2139,
0x213c, 0x213f,
0x2145, 0x2149,
0x214e, 0x214e,
0x2160, 0x2188,
0x24b6, 0x24e9,
0x2c00, 0x2ce4,
0x2ceb, 0x2cf3,
0x2d00, 0x2d25,
0x2d27, 0x2d27,
0x2d2d, 0x2d2d,
0x2d30, 0x2d67,
0x2d6f, 0x2d6f,
0x2d7f, 0x2d96,
0x2da0, 0x2da6,
0x2da8, 0x2dae,
0x2db0, 0x2db6,
0x2db8, 0x2dbe,
0x2dc0, 0x2dc6,
0x2dc8, 0x2dce,
0x2dd0, 0x2dd6,
0x2dd8, 0x2dde,
0x2de0, 0x2dff,
0x2e2f, 0x2e2f,
0x3005, 0x3007,
0x3021, 0x302f,
0x3031, 0x3035,
0x3038, 0x303c,
0x3041, 0x3096,
0x3099, 0x309a,
0x309d, 0x309f,
0x30a1, 0x30fa,
0x30fc, 0x30ff,
0x3105, 0x312f,
0x3131, 0x318e,
0x31a0, 0x31bf,
0x31f0, 0x31ff,
0x3400, 0x4dbf,
0x4e00, 0xa48c,
0xa4d0, 0xa4fd,
0xa500, 0xa60c,
0xa610, 0xa62b,
0xa640, 0xa672,
0xa674, 0xa67d,
0xa67f, 0xa6f1,
0xa717, 0xa71f,
0xa722, 0xa788,
0xa78b, 0xa7cd,
0xa7d0, 0xa7d1,
0xa7d3, 0xa7d3,
0xa7d5, 0xa7dc,
0xa7f2, 0xa827,
0xa82c, 0xa82c,
0xa840, 0xa873,
0xa880, 0xa8c5,
0xa8d0, 0xa8d9,
0xa8e0, 0xa8f7,
0xa8fb, 0xa8fb,
0xa8fd, 0xa92d,
0xa930, 0xa953,
0xa960, 0xa97c,
0xa980, 0xa9c0,
0xa9cf, 0xa9d9,
0xa9e0, 0xa9fe,
0xaa00, 0xaa36,
0xaa40, 0xaa4d,
0xaa50, 0xaa59,
0xaa60, 0xaa76,
0xaa7a, 0xaac2,
0xaadb, 0xaadd,
0xaae0, 0xaaef,
0xaaf2, 0xaaf6,
0xab01, 0xab06,
0xab09, 0xab0e,
0xab11, 0xab16,
0xab20, 0xab26,
0xab28, 0xab2e,
0xab30, 0xab5a,
0xab5c, 0xab69,
0xab70, 0xabea,
0xabec, 0xabed,
0xabf0, 0xabf9,
0xac00, 0xd7a3,
0xd7b0, 0xd7c6,
0xd7cb, 0xd7fb,
0xf900, 0xfa6d,
0xfa70, 0xfad9,
0xfb00, 0xfb06,
0xfb13, 0xfb17,
0xfb1d, 0xfb28,
0xfb2a, 0xfb36,
0xfb38, 0xfb3c,
0xfb3e, 0xfb3e,
0xfb40, 0xfb41,
0xfb43, 0xfb44,
0xfb46, 0xfbb1,
0xfbd3, 0xfd3d,
0xfd50, 0xfd8f,
0xfd92, 0xfdc7,
0xfdf0, 0xfdfb,
0xfe00, 0xfe0f,
0xfe20, 0xfe2f,
0xfe33, 0xfe34,
0xfe4d, 0xfe4f,
0xfe70, 0xfe74,
0xfe76, 0xfefc,
0xff10, 0xff19,
0xff21, 0xff3a,
0xff3f, 0xff3f,
0xff41, 0xff5a,
0xff66, 0xffbe,
0xffc2, 0xffc7,
0xffca, 0xffcf,
0xffd2, 0xffd7,
0xffda, 0xffdc,
0x10000, 0x1000b,
0x1000d, 0x10026,
0x10028, 0x1003a,
0x1003c, 0x1003d,
0x1003f, 0x1004d,
0x10050, 0x1005d,
0x10080, 0x100fa,
0x10140, 0x10174,
0x101fd, 0x101fd,
0x10280, 0x1029c,
0x102a0, 0x102d0,
0x102e0, 0x102e0,
0x10300, 0x1031f,
0x1032d, 0x1034a,
0x10350, 0x1037a,
0x10380, 0x1039d,
0x103a0, 0x103c3,
0x103c8, 0x103cf,
0x103d1, 0x103d5,
0x10400, 0x1049d,
0x104a0, 0x104a9,
0x104b0, 0x104d3,
0x104d8, 0x104fb,
0x10500, 0x10527,
0x10530, 0x10563,
0x10570, 0x1057a,
0x1057c, 0x1058a,
0x1058c, 0x10592,
0x10594, 0x10595,
0x10597, 0x105a1,
0x105a3, 0x105b1,
0x105b3, 0x105b9,
0x105bb, 0x105bc,
0x105c0, 0x105f3,
0x10600, 0x10736,
0x10740, 0x10755,
0x10760, 0x10767,
0x10780, 0x10785,
0x10787, 0x107b0,
0x107b2, 0x107ba,
0x10800, 0x10805,
0x10808, 0x10808,
0x1080a, 0x10835,
0x10837, 0x10838,
0x1083c, 0x1083c,
0x1083f, 0x10855,
0x10860, 0x10876,
0x10880, 0x1089e,
0x108e0, 0x108f2,
0x108f4, 0x108f5,
0x10900, 0x10915,
0x10920, 0x10939,
0x10980, 0x109b7,
0x109be, 0x109bf,
0x10a00, 0x10a03,
0x10a05, 0x10a06,
0x10a0c, 0x10a13,
0x10a15, 0x10a17,
0x10a19, 0x10a35,
0x10a38, 0x10a3a,
0x10a3f, 0x10a3f,
0x10a60, 0x10a7c,
0x10a80, 0x10a9c,
0x10ac0, 0x10ac7,
0x10ac9, 0x10ae6,
0x10b00, 0x10b35,
0x10b40, 0x10b55,
0x10b60, 0x10b72,
0x10b80, 0x10b91,
0x10c00, 0x10c48,
0x10c80, 0x10cb2,
0x10cc0, 0x10cf2,
0x10d00, 0x10d27,
0x10d30, 0x10d39,
0x10d40, 0x10d65,
0x10d69, 0x10d6d,
0x10d6f, 0x10d85,
0x10e80, 0x10ea9,
0x10eab, 0x10eac,
0x10eb0, 0x10eb1,
0x10ec2, 0x10ec4,
0x10efc, 0x10f1c,
0x10f27, 0x10f27,
0x10f30, 0x10f50,
0x10f70, 0x10f85,
0x10fb0, 0x10fc4,
0x10fe0, 0x10ff6,
0x11000, 0x11046,
0x11066, 0x11075,
0x1107f, 0x110ba,
0x110c2, 0x110c2,
0x110d0, 0x110e8,
0x110f0, 0x110f9,
0x11100, 0x11134,
0x11136, 0x1113f,
0x11144, 0x11147,
0x11150, 0x11173,
0x11176, 0x11176,
0x11180, 0x111c4,
0x111c9, 0x111cc,
0x111ce, 0x111da,
0x111dc, 0x111dc,
0x11200, 0x11211,
0x11213, 0x11237,
0x1123e, 0x11241,
0x11280, 0x11286,
0x11288, 0x11288,
0x1128a, 0x1128d,
0x1128f, 0x1129d,
0x1129f, 0x112a8,
0x112b0, 0x112ea,
0x112f0, 0x112f9,
0x11300, 0x11303,
0x11305, 0x1130c,
0x1130f, 0x11310,
0x11313, 0x11328,
0x1132a, 0x11330,
0x11332, 0x11333,
0x11335, 0x11339,
0x1133b, 0x11344,
0x11347, 0x11348,
0x1134b, 0x1134d,
0x11350, 0x11350,
0x11357, 0x11357,
0x1135d, 0x11363,
0x11366, 0x1136c,
0x11370, 0x11374,
0x11380, 0x11389,
0x1138b, 0x1138b,
0x1138e, 0x1138e,
0x11390, 0x113b5,
0x113b7, 0x113c0,
0x113c2, 0x113c2,
0x113c5, 0x113c5,
0x113c7, 0x113ca,
0x113cc, 0x113d3,
0x113e1, 0x113e2,
0x11400, 0x1144a,
0x11450, 0x11459,
0x1145e, 0x11461,
0x11480, 0x114c5,
0x114c7, 0x114c7,
0x114d0, 0x114d9,
0x11580, 0x115b5,
0x115b8, 0x115c0,
0x115d8, 0x115dd,
0x11600, 0x11640,
0x11644, 0x11644,
0x11650, 0x11659,
0x11680, 0x116b8,
0x116c0, 0x116c9,
0x116d0, 0x116e3,
0x11700, 0x1171a,
0x1171d, 0x1172b,
0x11730, 0x11739,
0x11740, 0x11746,
0x11800, 0x1183a,
0x118a0, 0x118e9,
0x118ff, 0x11906,
0x11909, 0x11909,
0x1190c, 0x11913,
0x11915, 0x11916,
0x11918, 0x11935,
0x11937, 0x11938,
0x1193b, 0x11943,
0x11950, 0x11959,
0x119a0, 0x119a7,
0x119aa, 0x119d7,
0x119da, 0x119e1,
0x119e3, 0x119e4,
0x11a00, 0x11a3e,
0x11a47, 0x11a47,
0x11a50, 0x11a99,
0x11a9d, 0x11a9d,
0x11ab0, 0x11af8,
0x11bc0, 0x11be0,
0x11bf0, 0x11bf9,
0x11c00, 0x11c08,
0x11c0a, 0x11c36,
0x11c38, 0x11c40,
0x11c50, 0x11c59,
0x11c72, 0x11c8f,
0x11c92, 0x11ca7,
0x11ca9, 0x11cb6,
0x11d00, 0x11d06,
0x11d08, 0x11d09,
0x11d0b, 0x11d36,
0x11d3a, 0x11d3a,
0x11d3c, 0x11d3d,
0x11d3f, 0x11d47,
0x11d50, 0x11d59,
0x11d60, 0x11d65,
0x11d67, 0x11d68,
0x11d6a, 0x11d8e,
0x11d90, 0x11d91,
0x11d93, 0x11d98,
0x11da0, 0x11da9,
0x11ee0, 0x11ef6,
0x11f00, 0x11f10,
0x11f12, 0x11f3a,
0x11f3e, 0x11f42,
0x11f50, 0x11f5a,
0x11fb0, 0x11fb0,
0x12000, 0x12399,
0x12400, 0x1246e,
0x12480, 0x12543,
0x12f90, 0x12ff0,
0x13000, 0x1342f,
0x13440, 0x13455,
0x13460, 0x143fa,
0x14400, 0x14646,
0x16100, 0x16139,
0x16800, 0x16a38,
0x16a40, 0x16a5e,
0x16a60, 0x16a69,
0x16a70, 0x16abe,
0x16ac0, 0x16ac9,
0x16ad0, 0x16aed,
0x16af0, 0x16af4,
0x16b00, 0x16b36,
0x16b40, 0x16b43,
0x16b50, 0x16b59,
0x16b63, 0x16b77,
0x16b7d, 0x16b8f,
0x16d40, 0x16d6c,
0x16d70, 0x16d79,
0x16e40, 0x16e7f,
0x16f00, 0x16f4a,
0x16f4f, 0x16f87,
0x16f8f, 0x16f9f,
0x16fe0, 0x16fe1,
0x16fe3, 0x16fe4,
0x16ff0, 0x16ff1,
0x17000, 0x187f7,
0x18800, 0x18cd5,
0x18cff, 0x18d08,
0x1aff0, 0x1aff3,
0x1aff5, 0x1affb,
0x1affd, 0x1affe,
0x1b000, 0x1b122,
0x1b132, 0x1b132,
0x1b150, 0x1b152,
0x1b155, 0x1b155,
0x1b164, 0x1b167,
0x1b170, 0x1b2fb,
0x1bc00, 0x1bc6a,
0x1bc70, 0x1bc7c,
0x1bc80, 0x1bc88,
0x1bc90, 0x1bc99,
0x1bc9d, 0x1bc9e,
0x1ccf0, 0x1ccf9,
0x1cf00, 0x1cf2d,
0x1cf30, 0x1cf46,
0x1d165, 0x1d169,
0x1d16d, 0x1d172,
0x1d17b, 0x1d182,
0x1d185, 0x1d18b,
0x1d1aa, 0x1d1ad,
0x1d242, 0x1d244,
0x1d400, 0x1d454,
0x1d456, 0x1d49c,
0x1d49e, 0x1d49f,
0x1d4a2, 0x1d4a2,
0x1d4a5, 0x1d4a6,
0x1d4a9, 0x1d4ac,
0x1d4ae, 0x1d4b9,
0x1d4bb, 0x1d4bb,
0x1d4bd, 0x1d4c3,
0x1d4c5, 0x1d505,
0x1d507, 0x1d50a,
0x1d50d, 0x1d514,
0x1d516, 0x1d51c,
0x1d51e, 0x1d539,
0x1d53b, 0x1d53e,
0x1d540, 0x1d544,
0x1d546, 0x1d546,
0x1d54a, 0x1d550,
0x1d552, 0x1d6a5,
0x1d6a8, 0x1d6c0,
0x1d6c2, 0x1d6da,
0x1d6dc, 0x1d6fa,
0x1d6fc, 0x1d714,
0x1d716, 0x1d734,
0x1d736, 0x1d74e,
0x1d750, 0x1d76e,
0x1d770, 0x1d788,
0x1d78a, 0x1d7a8,
0x1d7aa, 0x1d7c2,
0x1d7c4, 0x1d7cb,
0x1d7ce, 0x1d7ff,
0x1da00, 0x1da36,
0x1da3b, 0x1da6c,
0x1da75, 0x1da75,
0x1da84, 0x1da84,
0x1da9b, 0x1da9f,
0x1daa1, 0x1daaf,
0x1df00, 0x1df1e,
0x1df25, 0x1df2a,
0x1e000, 0x1e006,
0x1e008, 0x1e018,
0x1e01b, 0x1e021,
0x1e023, 0x1e024,
0x1e026, 0x1e02a,
0x1e030, 0x1e06d,
0x1e08f, 0x1e08f,
0x1e100, 0x1e12c,
0x1e130, 0x1e13d,
0x1e140, 0x1e149,
0x1e14e, 0x1e14e,
0x1e290, 0x1e2ae,
0x1e2c0, 0x1e2f9,
0x1e4d0, 0x1e4f9,
0x1e5d0, 0x1e5fa,
0x1e7e0, 0x1e7e6,
0x1e7e8, 0x1e7eb,
0x1e7ed, 0x1e7ee,
0x1e7f0, 0x1e7fe,
0x1e800, 0x1e8c4,
0x1e8d0, 0x1e8d6,
0x1e900, 0x1e94b,
0x1e950, 0x1e959,
0x1ee00, 0x1ee03,
0x1ee05, 0x1ee1f,
0x1ee21, 0x1ee22,
0x1ee24, 0x1ee24,
0x1ee27, 0x1ee27,
0x1ee29, 0x1ee32,
0x1ee34, 0x1ee37,
0x1ee39, 0x1ee39,
0x1ee3b, 0x1ee3b,
0x1ee42, 0x1ee42,
0x1ee47, 0x1ee47,
0x1ee49, 0x1ee49,
0x1ee4b, 0x1ee4b,
0x1ee4d, 0x1ee4f,
0x1ee51, 0x1ee52,
0x1ee54, 0x1ee54,
0x1ee57, 0x1ee57,
0x1ee59, 0x1ee59,
0x1ee5b, 0x1ee5b,
0x1ee5d, 0x1ee5d,
0x1ee5f, 0x1ee5f,
0x1ee61, 0x1ee62,
0x1ee64, 0x1ee64,
0x1ee67, 0x1ee6a,
0x1ee6c, 0x1ee72,
0x1ee74, 0x1ee77,
0x1ee79, 0x1ee7c,
0x1ee7e, 0x1ee7e,
0x1ee80, 0x1ee89,
0x1ee8b, 0x1ee9b,
0x1eea1, 0x1eea3,
0x1eea5, 0x1eea9,
0x1eeab, 0x1eebb,
0x1f130, 0x1f149,
0x1f150, 0x1f169,
0x1f170, 0x1f189,
0x1fbf0, 0x1fbf9,
0x20000, 0x2a6df,
0x2a700, 0x2b739,
0x2b740, 0x2b81d,
0x2b820, 0x2cea1,
0x2ceb0, 0x2ebe0,
0x2ebf0, 0x2ee5d,
0x2f800, 0x2fa1d,
0x30000, 0x3134a,
0x31350, 0x323af,
0xe0100, 0xe01ef,
}; /* END of CR_Word */

/* PROPERTY: 'Alnum': POSIX [[:Alnum:]] */
static const OnigCodePoint
CR_Alnum[] = { 802,
0x0030, 0x0039,
0x0041, 0x005a,
0x0061, 0x007a,
0x00aa, 0x00aa,
0x00b5, 0x00b5,
0x00ba, 0x00ba,
0x00c0, 0x00d6,
0x00d8, 0x00f6,
0x00f8, 0x02c1,
0x02c6, 0x02d1,
0x02e0, 0x02e4,
0x02ec, 0x02ec,
0x02ee, 0x02ee,
0x0345, 0x0345,
0x0363, 0x0374,
0x0376, 0x0377,
0x037a, 0x037d,
0x037f, 0x037f,
0x0386, 0x0386,
0x0388, 0x038a,
0x038c, 0x038c,
0x038e, 0x03a1,
0x03a3, 0x03f5,
0x03f7, 0x0481,
0x048a, 0x052f,
0x0531, 0x0556,
0x0559, 0x0559,
0x0560, 0x0588,
0x05b0, 0x05bd,
0x05bf, 0x05bf,
0x05c1, 0x05c2,
0x05c4, 0x05c5,
0x05c7, 0x05c7,
0x05d0, 0x05ea,
0x05ef, 0x05f2,
0x0610, 0x061a,
0x0620, 0x0657,
0x0659, 0x0669,
0x066e, 0x06d3,
0x06d5, 0x06dc,
0x06e1, 0x06e8,
0x06ed, 0x06fc,
0x06ff, 0x06ff,
0x0710, 0x073f,
0x074d, 0x07b1,
0x07c0, 0x07ea,
0x07f4, 0x07f5,
0x07fa, 0x07fa,
0x0800, 0x0817,
0x081a, 0x082c,
0x0840, 0x0858,
0x0860, 0x086a,
0x0870, 0x0887,
0x0889, 0x088e,
0x0897, 0x0897,
0x08a0, 0x08c9,
0x08d4, 0x08df,
0x08e3, 0x08e9,
0x08f0, 0x093b,
0x093d, 0x094c,
0x094e, 0x0950,
0x0955, 0x0963,
0x0966, 0x096f,
0x0971, 0x0983,
0x0985, 0x098c,
0x098f, 0x0990,
0x0993, 0x09a8,
0x09aa, 0x09b0,
0x09b2, 0x09b2,
0x09b6, 0x09b9,
0x09bd, 0x09c4,
0x09c7, 0x09c8,
0x09cb, 0x09cc,
0x09ce, 0x09ce,
0x09d7, 0x09d7,
0x09dc, 0x09dd,
0x09df, 0x09e3,
0x09e6, 0x09f1,
0x09fc, 0x09fc,
0x0a01, 0x0a03,
0x0a05, 0x0a0a,
0x0a0f, 0x0a10,
0x0a13, 0x0a28,
0x0a2a, 0x0a30,
0x0a32, 0x0a33,
0x0a35, 0x0a36,
0x0a38, 0x0a39,
0x0a3e, 0x0a42,
0x0a47, 0x0a48,
0x0a4b, 0x0a4c,
0x0a51, 0x0a51,
0x0a59, 0x0a5c,
0x0a5e, 0x0a5e,
0x0a66, 0x0a75,
0x0a81, 0x0a83,
0x0a85, 0x0a8d,
0x0a8f, 0x0a91,
0x0a93, 0x0aa8,
0x0aaa, 0x0ab0,
0x0ab2, 0x0ab3,
0x0ab5, 0x0ab9,
0x0abd, 0x0ac5,
0x0ac7, 0x0ac9,
0x0acb, 0x0acc,
0x0ad0, 0x0ad0,
0x0ae0, 0x0ae3,
0x0ae6, 0x0aef,
0x0af9, 0x0afc,
0x0b01, 0x0b03,
0x0b05, 0x0b0c,
0x0b0f, 0x0b10,
0x0b13, 0x0b28,
0x0b2a, 0x0b30,
0x0b32, 0x0b33,
0x0b35, 0x0b39,
0x0b3d, 0x0b44,
0x0b47, 0x0b48,
0x0b4b, 0x0b4c,
0x0b56, 0x0b57,
0x0b5c, 0x0b5d,
0x0b5f, 0x0b63,
0x0b66, 0x0b6f,
0x0b71, 0x0b71,
0x0b82, 0x0b83,
0x0b85, 0x0b8a,
0x0b8e, 0x0b90,
0x0b92, 0x0b95,
0x0b99, 0x0b9a,
0x0b9c, 0x0b9c,
0x0b9e, 0x0b9f,
0x0ba3, 0x0ba4,
0x0ba8, 0x0baa,
0x0bae, 0x0bb9,
0x0bbe, 0x0bc2,
0x0bc6, 0x0bc8,
0x0bca, 0x0bcc,
0x0bd0, 0x0bd0,
0x0bd7, 0x0bd7,
0x0be6, 0x0bef,
0x0c00, 0x0c0c,
0x0c0e, 0x0c10,
0x0c12, 0x0c28,
0x0c2a, 0x0c39,
0x0c3d, 0x0c44,
0x0c46, 0x0c48,
0x0c4a, 0x0c4c,
0x0c55, 0x0c56,
0x0c58, 0x0c5a,
0x0c5d, 0x0c5d,
0x0c60, 0x0c63,
0x0c66, 0x0c6f,
0x0c80, 0x0c83,
0x0c85, 0x0c8c,
0x0c8e, 0x0c90,
0x0c92, 0x0ca8,
0x0caa, 0x0cb3,
0x0cb5, 0x0cb9,
0x0cbd, 0x0cc4,
0x0cc6, 0x0cc8,
0x0cca, 0x0ccc,
0x0cd5, 0x0cd6,
0x0cdd, 0x0cde,
0x0ce0, 0x0ce3,
0x0ce6, 0x0cef,
0x0cf1, 0x0cf3,
0x0d00, 0x0d0c,
0x0d0e, 0x0d10,
0x0d12, 0x0d3a,
0x0d3d, 0x0d44,
0x0d46, 0x0d48,
0x0d4a, 0x0d4c,
0x0d4e, 0x0d4e,
0x0d54, 0x0d57,
0x0d5f, 0x0d63,
0x0d66, 0x0d6f,
0x0d7a, 0x0d7f,
0x0d81, 0x0d83,
0x0d85, 0x0d96,
0x0d9a, 0x0db1,
0x0db3, 0x0dbb,
0x0dbd, 0x0dbd,
0x0dc0, 0x0dc6,
0x0dcf, 0x0dd4,
0x0dd6, 0x0dd6,
0x0dd8, 0x0ddf,
0x0de6, 0x0def,
0x0df2, 0x0df3,
0x0e01, 0x0e3a,
0x0e40, 0x0e46,
0x0e4d, 0x0e4d,
0x0e50, 0x0e59,
0x0e81, 0x0e82,
0x0e84, 0x0e84,
0x0e86, 0x0e8a,
0x0e8c, 0x0ea3,
0x0ea5, 0x0ea5,
0x0ea7, 0x0eb9,
0x0ebb, 0x0ebd,
0x0ec0, 0x0ec4,
0x0ec6, 0x0ec6,
0x0ecd, 0x0ecd,
0x0ed0, 0x0ed9,
0x0edc, 0x0edf,
0x0f00, 0x0f00,
0x0f20, 0x0f29,
0x0f40, 0x0f47,
0x0f49, 0x0f6c,
0x0f71, 0x0f83,
0x0f88, 0x0f97,
0x0f99, 0x0fbc,
0x1000, 0x1036,
0x1038, 0x1038,
0x103b, 0x1049,
0x1050, 0x109d,
0x10a0, 0x10c5,
0x10c7, 0x10c7,
0x10cd, 0x10cd,
0x10d0, 0x10fa,
0x10fc, 0x1248,
0x124a, 0x124d,
0x1250, 0x1256,
0x1258, 0x1258,
0x125a, 0x125d,
0x1260, 0x1288,
0x128a, 0x128d,
0x1290, 0x12b0,
0x12b2, 0x12b5,
0x12b8, 0x12be,
0x12c0, 0x12c0,
0x12c2, 0x12c5,
0x12c8, 0x12d6,
0x12d8, 0x1310,
0x1312, 0x1315,
0x1318, 0x135a,
0x1380, 0x138f,
0x13a0, 0x13f5,
0x13f8, 0x13fd,
0x1401, 0x166c,
0x166f, 0x167f,
0x1681, 0x169a,
0x16a0, 0x16ea,
0x16ee, 0x16f8,
0x1700, 0x1713,
0x171f, 0x1733,
0x1740, 0x1753,
0x1760, 0x176c,
0x176e, 0x1770,
0x1772, 0x1773,
0x1780, 0x17b3,
0x17b6, 0x17c8,
0x17d7, 0x17d7,
0x17dc, 0x17dc,
0x17e0, 0x17e9,
0x1810, 0x1819,
0x1820, 0x1878,
0x1880, 0x18aa,
0x18b0, 0x18f5,
0x1900, 0x191e,
0x1920, 0x192b,
0x1930, 0x1938,
0x1946, 0x196d,
0x1970, 0x1974,
0x1980, 0x19ab,
0x19b0, 0x19c9,
0x19d0, 0x19d9,
0x1a00, 0x1a1b,
0x1a20, 0x1a5e,
0x1a61, 0x1a74,
0x1a80, 0x1a89,
0x1a90, 0x1a99,
0x1aa7, 0x1aa7,
0x1abf, 0x1ac0,
0x1acc, 0x1ace,
0x1b00, 0x1b33,
0x1b35, 0x1b43,
0x1b45, 0x1b4c,
0x1b50, 0x1b59,
0x1b80, 0x1ba9,
0x1bac, 0x1be5,
0x1be7, 0x1bf1,
0x1c00, 0x1c36,
0x1c40, 0x1c49,
0x1c4d, 0x1c7d,
0x1c80, 0x1c8a,
0x1c90, 0x1cba,
0x1cbd, 0x1cbf,
0x1ce9, 0x1cec,
0x1cee, 0x1cf3,
0x1cf5, 0x1cf6,
0x1cfa, 0x1cfa,
0x1d00, 0x1dbf,
0x1dd3, 0x1df4,
0x1e00, 0x1f15,
0x1f18, 0x1f1d,
0x1f20, 0x1f45,
0x1f48, 0x1f4d,
0x1f50, 0x1f57,
0x1f59, 0x1f59,
0x1f5b, 0x1f5b,
0x1f5d, 0x1f5d,
0x1f5f, 0x1f7d,
0x1f80, 0x1fb4,
0x1fb6, 0x1fbc,
0x1fbe, 0x1fbe,
0x1fc2, 0x1fc4,
0x1fc6, 0x1fcc,
0x1fd0, 0x1fd3,
0x1fd6, 0x1fdb,
0x1fe0, 0x1fec,
0x1ff2, 0x1ff4,
0x1ff6, 0x1ffc,
0x2071, 0x2071,
0x207f, 0x207f,
0x2090, 0x209c,
0x2102, 0x2102,
0x2107, 0x2107,
0x210a, 0x2113,
0x2115, 0x2115,
0x2119, 0x211d,
0x2124, 0x2124,
0x2126, 0x2126,
0x2128, 0x2128,
0x212a, 0x212d,
0x212f, 0x2139,
0x213c, 0x213f,
0x2145, 0x2149,
0x214e, 0x214e,
0x2160, 0x2188,
0x24b6, 0x24e9,
0x2c00, 0x2ce4,
0x2ceb, 0x2cee,
0x2cf2, 0x2cf3,
0x2d00, 0x2d25,
0x2d27, 0x2d27,
0x2d2d, 0x2d2d,
0x2d30, 0x2d67,
0x2d6f, 0x2d6f,
0x2d80, 0x2d96,
0x2da0, 0x2da6,
0x2da8, 0x2dae,
0x2db0, 0x2db6,
0x2db8, 0x2dbe,
0x2dc0, 0x2dc6,
0x2dc8, 0x2dce,
0x2dd0, 0x2dd6,
0x2dd8, 0x2dde,
0x2de0, 0x2dff,
0x2e2f, 0x2e2f,
0x3005, 0x3007,
0x3021, 0x3029,
0x3031, 0x3035,
0x3038, 0x303c,
0x3041, 0x3096,
0x309d, 0x309f,
0x30a1, 0x30fa,
0x30fc, 0x30ff,
0x3105, 0x312f,
0x3131, 0x318e,
0x31a0, 0x31bf,
0x31f0, 0x31ff,
0x3400, 0x4dbf,
0x4e00, 0xa48c,
0xa4d0, 0xa4fd,
0xa500, 0xa60c,
0xa610, 0xa62b,
0xa640, 0xa66e,
0xa674, 0xa67b,
0xa67f, 0xa6ef,
0xa717, 0xa71f,
0xa722, 0xa788,
0xa78b, 0xa7cd,
0xa7d0, 0xa7d1,
0xa7d3, 0xa7d3,
0xa7d5, 0xa7dc,
0xa7f2, 0xa805,
0xa807, 0xa827,
0xa840, 0xa873,
0xa880, 0xa8c3,
0xa8c5, 0xa8c5,
0xa8d0, 0xa8d9,
0xa8f2, 0xa8f7,
0xa8fb, 0xa8fb,
0xa8fd, 0xa92a,
0xa930, 0xa952,
0xa960, 0xa97c,
0xa980, 0xa9b2,
0xa9b4, 0xa9bf,
0xa9cf, 0xa9d9,
0xa9e0, 0xa9fe,
0xaa00, 0xaa36,
0xaa40, 0xaa4d,
0xaa50, 0xaa59,
0xaa60, 0xaa76,
0xaa7a, 0xaabe,
0xaac0, 0xaac0,
0xaac2, 0xaac2,
0xaadb, 0xaadd,
0xaae0, 0xaaef,
0xaaf2, 0xaaf5,
0xab01, 0xab06,
0xab09, 0xab0e,
0xab11, 0xab16,
0xab20, 0xab26,
0xab28, 0xab2e,
0xab30, 0xab5a,
0xab5c, 0xab69,
0xab70, 0xabea,
0xabf0, 0xabf9,
0xac00, 0xd7a3,
0xd7b0, 0xd7c6,
0xd7cb, 0xd7fb,
0xf900, 0xfa6d,
0xfa70, 0xfad9,
0xfb00, 0xfb06,
0xfb13, 0xfb17,
0xfb1d, 0xfb28,
0xfb2a, 0xfb36,
0xfb38, 0xfb3c,
0xfb3e, 0xfb3e,
0xfb40, 0xfb41,
0xfb43, 0xfb44,
0xfb46, 0xfbb1,
0xfbd3, 0xfd3d,
0xfd50, 0xfd8f,
0xfd92, 0xfdc7,
0xfdf0, 0xfdfb,
0xfe70, 0xfe74,
0xfe76, 0xfefc,
0xff10, 0xff19,
0xff21, 0xff3a,
0xff41, 0xff5a,
0xff66, 0xffbe,
0xffc2, 0xffc7,
0xffca, 0xffcf,
0xffd2, 0xffd7,
0xffda, 0xffdc,
0x10000, 0x1000b,
0x1000d, 0x10026,
0x10028, 0x1003a,
0x1003c, 0x1003d,
0x1003f, 0x1004d,
0x10050, 0x1005d,
0x10080, 0x100fa,
0x10140, 0x10174,
0x10280, 0x1029c,
0x102a0, 0x102d0,
0x10300, 0x1031f,
0x1032d, 0x1034a,
0x10350, 0x1037a,
0x10380, 0x1039d,
0x103a0, 0x103c3,
0x103c8, 0x103cf,
0x103d1, 0x103d5,
0x10400, 0x1049d,
0x104a0, 0x104a9,
0x104b0, 0x104d3,
0x104d8, 0x104fb,
0x10500, 0x10527,
0x10530, 0x10563,
0x10570, 0x1057a,
0x1057c, 0x1058a,
0x1058c, 0x10592,
0x10594, 0x10595,
0x10597, 0x105a1,
0x105a3, 0x105b1,
0x105b3, 0x105b9,
0x105bb, 0x105bc,
0x105c0, 0x105f3,
0x10600, 0x10736,
0x10740, 0x10755,
0x10760, 0x10767,
0x10780, 0x10785,
0x10787, 0x107b0,
0x107b2, 0x107ba,
0x10800, 0x10805,
0x10808, 0x10808,
0x1080a, 0x10835,
0x10837, 0x10838,
0x1083c, 0x1083c,
0x1083f, 0x10855,
0x10860, 0x10876,
0x10880, 0x1089e,
0x108e0, 0x108f2,
0x108f4, 0x108f5,
0x10900, 0x10915,
0x10920, 0x10939,
0x10980, 0x109b7,
0x109be, 0x109bf,
0x10a00, 0x10a03,
0x10a05, 0x10a06,
0x10a0c, 0x10a13,
0x10a15, 0x10a17,
0x10a19, 0x10a35,
0x10a60, 0x10a7c,
0x10a80, 0x10a9c,
0x10ac0, 0x10ac7,
0x10ac9, 0x10ae4,
0x10b00, 0x10b35,
0x10b40, 0x10b55,
0x10b60, 0x10b72,
0x10b80, 0x10b91,
0x10c00, 0x10c48,
0x10c80, 0x10cb2,
0x10cc0, 0x10cf2,
0x10d00, 0x10d27,
0x10d30, 0x10d39,
0x10d40, 0x10d65,
0x10d69, 0x10d69,
0x10d6f, 0x10d85,
0x10e80, 0x10ea9,
0x10eab, 0x10eac,
0x10eb0, 0x10eb1,
0x10ec2, 0x10ec4,
0x10efc, 0x10efc,
0x10f00, 0x10f1c,
0x10f27, 0x10f27,
0x10f30, 0x10f45,
0x10f70, 0x10f81,
0x10fb0, 0x10fc4,
0x10fe0, 0x10ff6,
0x11000, 0x11045,
0x11066, 0x1106f,
0x11071, 0x11075,
0x11080, 0x110b8,
0x110c2, 0x110c2,
0x110d0, 0x110e8,
0x110f0, 0x110f9,
0x11100, 0x11132,
0x11136, 0x1113f,
0x11144, 0x11147,
0x11150, 0x11172,
0x11176, 0x11176,
0x11180, 0x111bf,
0x111c1, 0x111c4,
0x111ce, 0x111da,
0x111dc, 0x111dc,
0x11200, 0x11211,
0x11213, 0x11234,
0x11237, 0x11237,
0x1123e, 0x11241,
0x11280, 0x11286,
0x11288, 0x11288,
0x1128a, 0x1128d,
0x1128f, 0x1129d,
0x1129f, 0x112a8,
0x112b0, 0x112e8,
0x112f0, 0x112f9,
0x11300, 0x11303,
0x11305, 0x1130c,
0x1130f, 0x11310,
0x11313, 0x11328,
0x1132a, 0x11330,
0x11332, 0x11333,
0x11335, 0x11339,
0x1133d, 0x11344,
0x11347, 0x11348,
0x1134b, 0x1134c,
0x11350, 0x11350,
0x11357, 0x11357,
0x1135d, 0x11363,
0x11380, 0x11389,
0x1138b, 0x1138b,
0x1138e, 0x1138e,
0x11390, 0x113b5,
0x113b7, 0x113c0,
0x113c2, 0x113c2,
0x113c5, 0x113c5,
0x113c7, 0x113ca,
0x113cc, 0x113cd,
0x113d1, 0x113d1,
0x113d3, 0x113d3,
0x11400, 0x11441,
0x11443, 0x11445,
0x11447, 0x1144a,
0x11450, 0x11459,
0x1145f, 0x11461,
0x11480, 0x114c1,
0x114c4, 0x114c5,
0x114c7, 0x114c7,
0x114d0, 0x114d9,
0x11580, 0x115b5,
0x115b8, 0x115be,
0x115d8, 0x115dd,
0x11600, 0x1163e,
0x11640, 0x11640,
0x11644, 0x11644,
0x11650, 0x11659,
0x11680, 0x116b5,
0x116b8, 0x116b8,
0x116c0, 0x116c9,
0x116d0, 0x116e3,
0x11700, 0x1171a,
0x1171d, 0x1172a,
0x11730, 0x11739,
0x11740, 0x11746,
0x11800, 0x11838,
0x118a0, 0x118e9,
0x118ff, 0x11906,
0x11909, 0x11909,
0x1190c, 0x11913,
0x11915, 0x11916,
0x11918, 0x11935,
0x11937, 0x11938,
0x1193b, 0x1193c,
0x1193f, 0x11942,
0x11950, 0x11959,
0x119a0, 0x119a7,
0x119aa, 0x119d7,
0x119da, 0x119df,
0x119e1, 0x119e1,
0x119e3, 0x119e4,
0x11a00, 0x11a32,
0x11a35, 0x11a3e,
0x11a50, 0x11a97,
0x11a9d, 0x11a9d,
0x11ab0, 0x11af8,
0x11bc0, 0x11be0,
0x11bf0, 0x11bf9,
0x11c00, 0x11c08,
0x11c0a, 0x11c36,
0x11c38, 0x11c3e,
0x11c40, 0x11c40,
0x11c50, 0x11c59,
0x11c72, 0x11c8f,
0x11c92, 0x11ca7,
0x11ca9, 0x11cb6,
0x11d00, 0x11d06,
0x11d08, 0x11d09,
0x11d0b, 0x11d36,
0x11d3a, 0x11d3a,
0x11d3c, 0x11d3d,
0x11d3f, 0x11d41,
0x11d43, 0x11d43,
0x11d46, 0x11d47,
0x11d50, 0x11d59,
0x11d60, 0x11d65,
0x11d67, 0x11d68,
0x11d6a, 0x11d8e,
0x11d90, 0x11d91,
0x11d93, 0x11d96,
0x11d98, 0x11d98,
0x11da0, 0x11da9,
0x11ee0, 0x11ef6,
0x11f00, 0x11f10,
0x11f12, 0x11f3a,
0x11f3e, 0x11f40,
0x11f50, 0x11f59,
0x11fb0, 0x11fb0,
0x12000, 0x12399,
0x12400, 0x1246e,
0x12480, 0x12543,
0x12f90, 0x12ff0,
0x13000, 0x1342f,
0x13441, 0x13446,
0x13460, 0x143fa,
0x14400, 0x14646,
0x16100, 0x1612e,
0x16130, 0x16139,
0x16800, 0x16a38,
0x16a40, 0x16a5e,
0x16a60, 0x16a69,
0x16a70, 0x16abe,
0x16ac0, 0x16ac9,
0x16ad0, 0x16aed,
0x16b00, 0x16b2f,
0x16b40, 0x16b43,
0x16b50, 0x16b59,
0x16b63, 0x16b77,
0x16b7d, 0x16b8f,
0x16d40, 0x16d6c,
0x16d70, 0x16d79,
0x16e40, 0x16e7f,
0x16f00, 0x16f4a,
0x16f4f, 0x16f87,
0x16f8f, 0x16f9f,
0x16fe0, 0x16fe1,
0x16fe3, 0x16fe3,
0x16ff0, 0x16ff1,
0x17000, 0x187f7,
0x18800, 0x18cd5,
0x18cff, 0x18d08,
0x1aff0, 0x1aff3,
0x1aff5, 0x1affb,
0x1affd, 0x1affe,
0x1b000, 0x1b122,
0x1b132, 0x1b132,
0x1b150, 0x1b152,
0x1b155, 0x1b155,
0x1b164, 0x1b167,
0x1b170, 0x1b2fb,
0x1bc00, 0x1bc6a,
0x1bc70, 0x1bc7c,
0x1bc80, 0x1bc88,
0x1bc90, 0x1bc99,
0x1bc9e, 0x1bc9e,
0x1ccf0, 0x1ccf9,
0x1d400, 0x1d454,
0x1d456, 0x1d49c,
0x1d49e, 0x1d49f,
0x1d4a2, 0x1d4a2,
0x1d4a5, 0x1d4a6,
0x1d4a9, 0x1d4ac,
0x1d4ae, 0x1d4b9,
0x1d4bb, 0x1d4bb,
0x1d4bd, 0x1d4c3,
0x1d4c5, 0x1d505,
0x1d507, 0x1d50a,
0x1d50d, 0x1d514,
0x1d516, 0x1d51c,
0x1d51e, 0x1d539,
0x1d53b, 0x1d53e,
0x1d540, 0x1d544,
0x1d546, 0x1d546,
0x1d54a, 0x1d550,
0x1d552, 0x1d6a5,
0x1d6a8, 0x1d6c0,
0x1d6c2, 0x1d6da,
0x1d6dc, 0x1d6fa,
0x1d6fc, 0x1d714,
0x1d716, 0x1d734,
0x1d736, 0x1d74e,
0x1d750, 0x1d76e,
0x1d770, 0x1d788,
0x1d78a, 0x1d7a8,
0x1d7aa, 0x1d7c2,
0x1d7c4, 0x1d7cb,
0x1d7ce, 0x1d7ff,
0x1df00, 0x1df1e,
0x1df25, 0x1df2a,
0x1e000, 0x1e006,
0x1e008, 0x1e018,
0x1e01b, 0x1e021,
0x1e023, 0x1e024,
0x1e026, 0x1e02a,
0x1e030, 0x1e06d,
0x1e08f, 0x1e08f,
0x1e100, 0x1e12c,
0x1e137, 0x1e13d,
0x1e140, 0x1e149,
0x1e14e, 0x1e14e,
0x1e290, 0x1e2ad,
0x1e2c0, 0x1e2eb,
0x1e2f0, 0x1e2f9,
0x1e4d0, 0x1e4eb,
0x1e4f0, 0x1e4f9,
0x1e5d0, 0x1e5ed,
0x1e5f0, 0x1e5fa,
0x1e7e0, 0x1e7e6,
0x1e7e8, 0x1e7eb,
0x1e7ed, 0x1e7ee,
0x1e7f0, 0x1e7fe,
0x1e800, 0x1e8c4,
0x1e900, 0x1e943,
0x1e947, 0x1e947,
0x1e94b, 0x1e94b,
0x1e950, 0x1e959,
0x1ee00, 0x1ee03,
0x1ee05, 0x1ee1f,
0x1ee21, 0x1ee22,
0x1ee24, 0x1ee24,
0x1ee27, 0x1ee27,
0x1ee29, 0x1ee32,
0x1ee34, 0x1ee37,
0x1ee39, 0x1ee39,
0x1ee3b, 0x1ee3b,
0x1ee42, 0x1ee42,
0x1ee47, 0x1ee47,
0x1ee49, 0x1ee49,
0x1ee4b, 0x1ee4b,
0x1ee4d, 0x1ee4f,
0x1ee51, 0x1ee52,
0x1ee54, 0x1ee54,
0x1ee57, 0x1ee57,
0x1ee59, 0x1ee59,
0x1ee5b, 0x1ee5b,
0x1ee5d, 0x1ee5d,
0x1ee5f, 0x1ee5f,
0x1ee61, 0x1ee62,
0x1ee64, 0x1ee64,
0x1ee67, 0x1ee6a,
0x1ee6c, 0x1ee72,
0x1ee74, 0x1ee77,
0x1ee79, 0x1ee7c,
0x1ee7e, 0x1ee7e,
0x1ee80, 0x1ee89,
0x1ee8b, 0x1ee9b,
0x1eea1, 0x1eea3,
0x1eea5, 0x1eea9,
0x1eeab, 0x1eebb,
0x1f130, 0x1f149,
0x1f150, 0x1f169,
0x1f170, 0x1f189,
0x1fbf0, 0x1fbf9,
0x20000, 0x2a6df,
0x2a700, 0x2b739,
0x2b740, 0x2b81d,
0x2b820, 0x2cea1,
0x2ceb0, 0x2ebe0,
0x2ebf0, 0x2ee5d,
0x2f800, 0x2fa1d,
0x30000, 0x3134a,
0x31350, 0x323af,
}; /* END of CR_Alnum */

/* PROPERTY: 'ASCII': POSIX [[:ASCII:]] */
static const OnigCodePoint
CR_ASCII[] = { 1,
0x0000, 0x007f,
}; /* END of CR_ASCII */


static const OnigCodePoint*
const CodeRanges[] = {
  CR_NEWLINE,
  CR_Alpha,
  CR_Blank,
  CR_Cntrl,
  CR_Digit,
  CR_Graph,
  CR_Lower,
  CR_Print,
  CR_PosixPunct,
  CR_Space,
  CR_Upper,
  CR_XDigit,
  CR_Word,
  CR_Alnum,
  CR_ASCII,
};

#define pool_offset(s) offsetof(struct unicode_prop_name_pool_t, unicode_prop_name_pool_str##s)


#define TOTAL_KEYWORDS 15
#define MIN_WORD_LENGTH 4
#define MAX_WORD_LENGTH 10
#define MIN_HASH_VALUE 5
#define MAX_HASH_VALUE 19
/* maximum key range = 15, duplicates = 0 */

#ifndef GPERF_DOWNCASE
#define GPERF_DOWNCASE 1
static unsigned char gperf_downcase[256] =
  {
      0,   1,   2,   3,   4,   5,   6,   7,   8,   9,  10,  11,  12,  13,  14,
     15,  16,  17,  18,  19,  20,  21,  22,  23,  24,  25,  26,  27,  28,  29,
     30,  31,  32,  33,  34,  35,  36,  37,  38,  39,  40,  41,  42,  43,  44,
     45,  46,  47,  48,  49,  50,  51,  52,  53,  54,  55,  56,  57,  58,  59,
     60,  61,  62,  63,  64,  97,  98,  99, 100, 101, 102, 103, 104, 105, 106,
    107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121,
    122,  91,  92,  93,  94,  95,  96,  97,  98,  99, 100, 101, 102, 103, 104,
    105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119,
    120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134,
    135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149,
    150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164,
    165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179,
    180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194,
    195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209,
    210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224,
    225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239,
    240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254,
    255
  };
#endif

#ifndef GPERF_CASE_STRNCMP
#define GPERF_CASE_STRNCMP 1
static int
gperf_case_strncmp (register const char *s1, register const char *s2, register size_t n)
{
  for (; n > 0;)
    {
      unsigned char c1 = gperf_downcase[(unsigned char)*s1++];
      unsigned char c2 = gperf_downcase[(unsigned char)*s2++];
      if (c1 != 0 && c1 == c2)
        {
          n--;
          continue;
        }
      return (int)c1 - (int)c2;
    }
  return 0;
}
#endif

#ifdef __GNUC__
__inline
#else
#ifdef __cplusplus
inline
#endif
#endif
static unsigned int
hash (register const char *str, register size_t len)
{
  static const unsigned char asso_values[] =
    {
      20, 20, 20, 20, 20, 20, 20, 20, 20, 20,
      20, 20, 20, 20, 20, 20, 20, 20, 20, 20,
      20, 20, 20, 20, 20, 20, 20, 20, 20, 20,
      20, 20, 20, 20, 20, 20, 20, 20, 20, 20,
      20, 20, 20, 20, 20, 20, 20, 20, 20, 20,
      20, 20, 20, 20, 20, 20, 20, 20, 20, 20,
      20, 20, 20, 20, 20,  0, 14,  8,  1, 20,
      20, 12, 20,  1, 20, 20, 10, 20,  4, 20,
       1, 20, 10,  0,  1,  4, 20,  1,  1, 20,
      20, 20, 20, 20, 20, 20, 20,  0, 14,  8,
       1, 20, 20, 12, 20,  1, 20, 20, 10, 20,
       4, 20,  1, 20, 10,  0,  1,  4, 20,  1,
       1, 20, 20, 20, 20, 20, 20, 20, 20, 20,
      20, 20, 20, 20, 20, 20, 20, 20, 20, 20,
      20, 20, 20, 20, 20, 20, 20, 20, 20, 20,
      20, 20, 20, 20, 20, 20, 20, 20, 20, 20,
      20, 20, 20, 20, 20, 20, 20, 20, 20, 20,
      20, 20, 20, 20, 20, 20, 20, 20, 20, 20,
      20, 20, 20, 20, 20, 20, 20, 20, 20, 20,
      20, 20, 20, 20, 20, 20, 20, 20, 20, 20,
      20, 20, 20, 20, 20, 20, 20, 20, 20, 20,
      20, 20, 20, 20, 20, 20, 20, 20, 20, 20,
      20, 20, 20, 20, 20, 20, 20, 20, 20, 20,
      20, 20, 20, 20, 20, 20, 20, 20, 20, 20,
      20, 20, 20, 20, 20, 20, 20, 20, 20, 20,
      20, 20, 20, 20, 20, 20
    };
  return len + asso_values[(unsigned char)str[2]] + asso_values[(unsigned char)str[0]];
}

struct unicode_prop_name_pool_t
  {
    char unicode_prop_name_pool_str5[sizeof("space")];
    char unicode_prop_name_pool_str6[sizeof("alpha")];
    char unicode_prop_name_pool_str7[sizeof("print")];
    char unicode_prop_name_pool_str8[sizeof("xdigit")];
    char unicode_prop_name_pool_str9[sizeof("alnum")];
    char unicode_prop_name_pool_str10[sizeof("upper")];
    char unicode_prop_name_pool_str11[sizeof("posixpunct")];
    char unicode_prop_name_pool_str12[sizeof("newline")];
    char unicode_prop_name_pool_str13[sizeof("ascii")];
    char unicode_prop_name_pool_str14[sizeof("cntrl")];
    char unicode_prop_name_pool_str15[sizeof("word")];
    char unicode_prop_name_pool_str16[sizeof("lower")];
    char unicode_prop_name_pool_str17[sizeof("graph")];
    char unicode_prop_name_pool_str18[sizeof("digit")];
    char unicode_prop_name_pool_str19[sizeof("blank")];
  };
static const struct unicode_prop_name_pool_t unicode_prop_name_pool_contents =
  {
    "space",
    "alpha",
    "print",
    "xdigit",
    "alnum",
    "upper",
    "posixpunct",
    "newline",
    "ascii",
    "cntrl",
    "word",
    "lower",
    "graph",
    "digit",
    "blank"
  };
#define unicode_prop_name_pool ((const char *) &unicode_prop_name_pool_contents)
static const struct PoolPropertyNameCtype *
unicode_lookup_property_name (register const char *str, register size_t len)
{
  static const struct PoolPropertyNameCtype wordlist[] =
    {
      {-1}, {-1}, {-1}, {-1}, {-1},

      {pool_offset(5),                                9},

      {pool_offset(6),                                1},

      {pool_offset(7),                                7},

      {pool_offset(8),                              11},

      {pool_offset(9),                               13},

      {pool_offset(10),                               10},

      {pool_offset(11),                           8},

      {pool_offset(12),                              0},

      {pool_offset(13),                               14},

      {pool_offset(14),                                3},

      {pool_offset(15),                                12},

      {pool_offset(16),                                6},

      {pool_offset(17),                                5},

      {pool_offset(18),                                4},

      {pool_offset(19),                                2}
    };

  if (len <= MAX_WORD_LENGTH && len >= MIN_WORD_LENGTH)
    {
      register unsigned int key = hash (str, len);

      if (key <= MAX_HASH_VALUE)
        {
          register int o = wordlist[key].name;
          if (o >= 0)
            {
              register const char *s = o + unicode_prop_name_pool;

              if ((((unsigned char)*str ^ (unsigned char)*s) & ~32) == 0 && !gperf_case_strncmp (str, s, len) && s[len] == '\0')
                return &wordlist[key];
            }
        }
    }
  return 0;
}



#define PROPERTY_NAME_MAX_SIZE  20
#define CODE_RANGES_NUM         15

#define PROP_INDEX_NEWLINE 0
#define PROP_INDEX_ALPHA 1
#define PROP_INDEX_BLANK 2
#define PROP_INDEX_CNTRL 3
#define PROP_INDEX_DIGIT 4
#define PROP_INDEX_GRAPH 5
#define PROP_INDEX_LOWER 6
#define PROP_INDEX_PRINT 7
#define PROP_INDEX_POSIXPUNCT 8
#define PROP_INDEX_SPACE 9
#define PROP_INDEX_UPPER 10
#define PROP_INDEX_XDIGIT 11
#define PROP_INDEX_WORD 12
#define PROP_INDEX_ALNUM 13
#define PROP_INDEX_ASCII 14
